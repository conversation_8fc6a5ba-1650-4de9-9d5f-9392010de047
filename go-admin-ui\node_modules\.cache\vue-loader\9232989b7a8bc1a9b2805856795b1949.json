{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue?vue&type=template&id=12d4a639&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue", "mtime": 1753924830069}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXY+DQogICAgPGlucHV0IHJlZj0iZXhjZWwtdXBsb2FkLWlucHV0IiBjbGFzcz0iZXhjZWwtdXBsb2FkLWlucHV0IiB0eXBlPSJmaWxlIiBhY2NlcHQ9Ii54bHN4LCAueGxzIiBAY2hhbmdlPSJoYW5kbGVDbGljayI+DQogICAgPGRpdiBjbGFzcz0iZHJvcCIgQGRyb3A9ImhhbmRsZURyb3AiIEBkcmFnb3Zlcj0iaGFuZGxlRHJhZ292ZXIiIEBkcmFnZW50ZXI9ImhhbmRsZURyYWdvdmVyIj4NCiAgICAgIERyb3AgZXhjZWwgZmlsZSBoZXJlIG9yDQogICAgICA8ZWwtYnV0dG9uIDpsb2FkaW5nPSJsb2FkaW5nIiBzdHlsZT0ibWFyZ2luLWxlZnQ6MTZweDsiIHNpemU9Im1pbmkiIHR5cGU9InByaW1hcnkiIEBjbGljaz0iaGFuZGxlVXBsb2FkIj4NCiAgICAgICAgQnJvd3NlDQogICAgICA8L2VsLWJ1dHRvbj4NCiAgICA8L2Rpdj4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/UploadExcel/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <input ref=\"excel-upload-input\" class=\"excel-upload-input\" type=\"file\" accept=\".xlsx, .xls\" @change=\"handleClick\">\r\n    <div class=\"drop\" @drop=\"handleDrop\" @dragover=\"handleDragover\" @dragenter=\"handleDragover\">\r\n      Drop excel file here or\r\n      <el-button :loading=\"loading\" style=\"margin-left:16px;\" size=\"mini\" type=\"primary\" @click=\"handleUpload\">\r\n        Browse\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport XLSX from 'xlsx'\r\n\r\nexport default {\r\n  props: {\r\n    beforeUpload: Function, // eslint-disable-line\r\n    onSuccess: Function// eslint-disable-line\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      excelData: {\r\n        header: null,\r\n        results: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    generateData({ header, results }) {\r\n      this.excelData.header = header\r\n      this.excelData.results = results\r\n      this.onSuccess && this.onSuccess(this.excelData)\r\n    },\r\n    handleDrop(e) {\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n      if (this.loading) return\r\n      const files = e.dataTransfer.files\r\n      if (files.length !== 1) {\r\n        this.$message.error('Only support uploading one file!')\r\n        return\r\n      }\r\n      const rawFile = files[0] // only use files[0]\r\n\r\n      if (!this.isExcel(rawFile)) {\r\n        this.$message.error('Only supports upload .xlsx, .xls, .csv suffix files')\r\n        return false\r\n      }\r\n      this.upload(rawFile)\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n    },\r\n    handleDragover(e) {\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n      e.dataTransfer.dropEffect = 'copy'\r\n    },\r\n    handleUpload() {\r\n      this.$refs['excel-upload-input'].click()\r\n    },\r\n    handleClick(e) {\r\n      const files = e.target.files\r\n      const rawFile = files[0] // only use files[0]\r\n      if (!rawFile) return\r\n      this.upload(rawFile)\r\n    },\r\n    upload(rawFile) {\r\n      this.$refs['excel-upload-input'].value = null // fix can't select the same excel\r\n\r\n      if (!this.beforeUpload) {\r\n        this.readerData(rawFile)\r\n        return\r\n      }\r\n      const before = this.beforeUpload(rawFile)\r\n      if (before) {\r\n        this.readerData(rawFile)\r\n      }\r\n    },\r\n    readerData(rawFile) {\r\n      this.loading = true\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader()\r\n        reader.onload = e => {\r\n          const data = e.target.result\r\n          const workbook = XLSX.read(data, { type: 'array' })\r\n          const firstSheetName = workbook.SheetNames[0]\r\n          const worksheet = workbook.Sheets[firstSheetName]\r\n          const header = this.getHeaderRow(worksheet)\r\n          const results = XLSX.utils.sheet_to_json(worksheet)\r\n          this.generateData({ header, results })\r\n          this.loading = false\r\n          resolve()\r\n        }\r\n        reader.readAsArrayBuffer(rawFile)\r\n      })\r\n    },\r\n    getHeaderRow(sheet) {\r\n      const headers = []\r\n      const range = XLSX.utils.decode_range(sheet['!ref'])\r\n      let C\r\n      const R = range.s.r\r\n      /* start in the first row */\r\n      for (C = range.s.c; C <= range.e.c; ++C) { /* walk every column in the range */\r\n        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]\r\n        /* find the cell in the first row */\r\n        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default\r\n        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)\r\n        headers.push(hdr)\r\n      }\r\n      return headers\r\n    },\r\n    isExcel(file) {\r\n      return /\\.(xlsx|xls|csv)$/.test(file.name)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.excel-upload-input{\r\n  display: none;\r\n  z-index: -9999;\r\n}\r\n.drop{\r\n  border: 2px dashed #bbb;\r\n  width: 600px;\r\n  height: 160px;\r\n  line-height: 160px;\r\n  margin: 0 auto;\r\n  font-size: 24px;\r\n  border-radius: 5px;\r\n  text-align: center;\r\n  color: #bbb;\r\n  position: relative;\r\n}\r\n</style>\r\n"]}]}