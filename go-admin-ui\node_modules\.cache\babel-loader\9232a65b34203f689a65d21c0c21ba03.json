{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\BarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\BarChart.vue", "mtime": 1753924830283}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "require", "resize", "animationDuration", "mixins", "props", "className", "type", "String", "default", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "axisPointer", "grid", "top", "left", "right", "bottom", "containLabel", "xAxis", "axisTick", "alignWithLabel", "yAxis", "show", "series", "name", "stack", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\BarChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 6000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        grid: {\r\n          top: 10,\r\n          left: '2%',\r\n          right: '2%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          }\r\n        }],\r\n        yAxis: [{\r\n          type: 'value',\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        }],\r\n        series: [{\r\n          name: 'pageA',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [79, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageB',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [80, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageC',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [30, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAKA,OAAOA,OAAM,MAAO,SAAQ;AAC5BC,OAAO,CAAC,wBAAwB,GAAE;AAClC,OAAOC,MAAK,MAAO,iBAAgB;AAEnC,IAAMC,iBAAgB,GAAI,IAAG;AAE7B,eAAe;EACbC,MAAM,EAAE,CAACF,MAAM,CAAC;EAChBG,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE;MACNJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,SAAS,CAAC,YAAM;MACnBD,KAAI,CAACE,SAAS,CAAC;IACjB,CAAC;EACH,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACL,KAAK,EAAE;MACf;IACF;IACA,IAAI,CAACA,KAAK,CAACM,OAAO,CAAC;IACnB,IAAI,CAACN,KAAI,GAAI,IAAG;EAClB,CAAC;EACDO,OAAO,EAAE;IACPH,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACJ,KAAI,GAAIb,OAAO,CAACqB,IAAI,CAAC,IAAI,CAACC,GAAG,EAAE,UAAU;MAE9C,IAAI,CAACT,KAAK,CAACU,SAAS,CAAC;QACnBC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YAAE;YACbnB,IAAI,EAAE,QAAO,CAAE;UACjB;QACF,CAAC;QACDoB,IAAI,EAAE;UACJC,GAAG,EAAE,EAAE;UACPC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE,CAAC;UACN1B,IAAI,EAAE,UAAU;UAChBK,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;UACvDsB,QAAQ,EAAE;YACRC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFC,KAAK,EAAE,CAAC;UACN7B,IAAI,EAAE,OAAO;UACb2B,QAAQ,EAAE;YACRG,IAAI,EAAE;UACR;QACF,CAAC,CAAC;QACFC,MAAM,EAAE,CAAC;UACPC,IAAI,EAAE,OAAO;UACbhC,IAAI,EAAE,KAAK;UACXiC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,KAAK;UACf7B,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACvCT,iBAAgB,EAAhBA;QACF,CAAC,EAAE;UACDoC,IAAI,EAAE,OAAO;UACbhC,IAAI,EAAE,KAAK;UACXiC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,KAAK;UACf7B,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACvCT,iBAAgB,EAAhBA;QACF,CAAC,EAAE;UACDoC,IAAI,EAAE,OAAO;UACbhC,IAAI,EAAE,KAAK;UACXiC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,KAAK;UACf7B,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACvCT,iBAAgB,EAAhBA;QACF,CAAC;MACH,CAAC;IACH;EACF;AACF", "ignoreList": []}]}