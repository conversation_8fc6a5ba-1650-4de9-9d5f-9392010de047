{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PieChart.vue?vue&type=template&id=b569d7f4", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PieChart.vue", "mtime": 1753924830286}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgOmNsYXNzPSJjbGFzc05hbWUiIDpzdHlsZT0ie2hlaWdodDpoZWlnaHQsd2lkdGg6d2lkdGh9IiAvPg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PieChart.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/components/PieChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\r\n        },\r\n        series: [\r\n          {\r\n            name: 'WEEKLY WRITE ARTICLES',\r\n            type: 'pie',\r\n            roseType: 'radius',\r\n            radius: [15, 95],\r\n            center: ['50%', '38%'],\r\n            data: [\r\n              { value: 320, name: 'Industries' },\r\n              { value: 240, name: 'Technology' },\r\n              { value: 149, name: 'Forex' },\r\n              { value: 100, name: 'Gold' },\r\n              { value: 59, name: 'Forecasts' }\r\n            ],\r\n            animationEasing: 'cubicInOut',\r\n            animationDuration: 2600\r\n          }\r\n        ]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}