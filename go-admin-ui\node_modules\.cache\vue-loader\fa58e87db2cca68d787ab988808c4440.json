{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\index.vue?vue&type=template&id=0f566a18", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\index.vue", "mtime": 1753924830469}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\index.vue"], "names": [], "mappings": ";EAEE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC;;UAED,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB;sBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;0BAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC,CAAC,CAAC;wBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB,CAAC,CAAC,CAAC,CAAC,CAAC;wBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB;sBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/schedule/index.vue", "sourceRoot": "", "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <BasicLayout>\r\n      <template #wrapper>\r\n        <el-card class=\"box-card\">\r\n          <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-position=\"left\" label-width=\"68px\">\r\n            <el-form-item label=\"名称\" prop=\"jobName\">\r\n              <el-input\r\n                v-model=\"queryParams.jobName\"\r\n                placeholder=\"请输入名称\"\r\n                clearable\r\n                size=\"small\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"任务分组\" prop=\"jobGroup\">\r\n              <el-select\r\n                v-model=\"queryParams.jobGroup\"\r\n                placeholder=\"定时任务任务分组\"\r\n                clearable\r\n                size=\"small\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in jobGroupOptions\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-select\r\n                v-model=\"queryParams.status\"\r\n                placeholder=\"定时任务状态\"\r\n                clearable\r\n                size=\"small\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-permisaction=\"['job:sysJob:add']\"\r\n                type=\"primary\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                @click=\"handleAdd\"\r\n              >新增\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-permisaction=\"['job:sysJob:edit']\"\r\n                type=\"success\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"single\"\r\n                @click=\"handleUpdate\"\r\n              >修改\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-permisaction=\"['job:sysJob:remove']\"\r\n                type=\"danger\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-permisaction=\"['job:sysJob:log']\"\r\n                type=\"danger\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                @click=\"handleLog\"\r\n              >日志\r\n              </el-button>\r\n\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-table v-loading=\"loading\" :data=\"sysjobList\" @selection-change=\"handleSelectionChange\">\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column\r\n              label=\"编码\"\r\n              align=\"center\"\r\n              prop=\"jobId\"\r\n              :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column\r\n              label=\"名称\"\r\n              align=\"center\"\r\n              prop=\"jobName\"\r\n              :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column\r\n              label=\"任务分组\"\r\n              align=\"center\"\r\n              prop=\"jobGroup\"\r\n              :formatter=\"jobGroupFormat\"\r\n              width=\"100\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                {{ jobGroupFormat(scope.row) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"cron表达式\"\r\n              align=\"center\"\r\n              prop=\"cronExpression\"\r\n              :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column\r\n              label=\"调用目标\"\r\n              align=\"center\"\r\n              prop=\"invokeTarget\"\r\n              :show-overflow-tooltip=\"true\"\r\n            />\r\n            <el-table-column\r\n              label=\"状态\"\r\n              align=\"center\"\r\n              prop=\"status\"\r\n              :formatter=\"statusFormat\"\r\n              width=\"100\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                {{ statusFormat(scope.row) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-permisaction=\"['job:sysJob:edit']\"\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleUpdate(scope.row)\"\r\n                >修改\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.entry_id!==0&& scope.row.status!=1\"\r\n                  v-permisaction=\"['job:sysJob:remove']\"\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleRemove(scope.row)\"\r\n                >停止\r\n                </el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.entry_id==0 && scope.row.status!=1\"\r\n                  v-permisaction=\"['job:sysJob:start']\"\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleStart(scope.row)\"\r\n                >启动\r\n                </el-button>\r\n                <el-button\r\n                  v-permisaction=\"['job:sysJob:remove']\"\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleDelete(scope.row)\"\r\n                >删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n\r\n          <pagination\r\n            v-show=\"total>0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageIndex\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n          />\r\n\r\n          <!-- 添加或修改对话框 -->\r\n          <el-dialog v-dialogDrag :title=\"title\" :visible.sync=\"open\" width=\"700px\" append-to-body :close-on-click-modal=\"false\">\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n              <el-row>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"名称\" prop=\"jobName\">\r\n                    <el-input\r\n                      v-model=\"form.jobName\"\r\n                      placeholder=\"名称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"任务分组\" prop=\"jobGroup\">\r\n                    <el-select\r\n                      v-model=\"form.jobGroup\"\r\n                      placeholder=\"请选择\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in jobGroupOptions\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"调用目标\" prop=\"invokeTarget\">\r\n                    <span slot=\"label\">\r\n                      调用目标\r\n                      <el-tooltip placement=\"top\">\r\n                        <div slot=\"content\">\r\n                          调用示例：func (t *EXEC) ExamplesNoParam(){..} 填写 ExamplesNoParam 即可；\r\n                          <br>参数说明：目前不支持带参调用\r\n                        </div>\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input\r\n                      v-model=\"form.invokeTarget\"\r\n                      placeholder=\"调用目标\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"目标参数\" prop=\"args\">\r\n                    <span slot=\"label\">\r\n                      目标参数\r\n                      <el-tooltip placement=\"top\">\r\n                        <div slot=\"content\">\r\n                          参数示例：有参：请以string格式填写；无参：为空；\r\n                          <br>参数说明：目前仅支持函数调用\r\n                        </div>\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input\r\n                      v-model=\"form.args\"\r\n                      placeholder=\"目标参数\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"cron表达式\" prop=\"cronExpression\">\r\n                    <el-input\r\n                      v-model=\"form.cronExpression\"\r\n                      placeholder=\"cron表达式\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"是否并发\" prop=\"concurrent\">\r\n                    <el-radio-group v-model=\"form.concurrent\" size=\"small\">\r\n                      <el-radio-button label=\"0\">允许</el-radio-button>\r\n                      <el-radio-button label=\"1\">禁止</el-radio-button>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"调用类型\" prop=\"jobType\">\r\n                    <el-radio-group v-model=\"form.jobType\" size=\"small\">\r\n                      <el-radio-button label=\"1\">接口</el-radio-button>\r\n                      <el-radio-button label=\"2\">函数</el-radio-button>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"执行策略\" prop=\"misfirePolicy\">\r\n                    <el-radio-group v-model=\"form.misfirePolicy\" size=\"small\">\r\n                      <el-radio-button label=\"1\">立即执行</el-radio-button>\r\n                      <el-radio-button label=\"2\">执行一次</el-radio-button>\r\n                      <el-radio-button label=\"3\">放弃执行</el-radio-button>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item label=\"状态\" prop=\"status\">\r\n                    <el-select\r\n                      v-model=\"form.status\"\r\n                      placeholder=\"请选择\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in statusOptions\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n            <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n              <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n          </el-dialog>\r\n\r\n        </el-card>\r\n      </template>\r\n    </BasicLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { addSysJob, delSysJob, getSysJob, listSysJob, updateSysJob, removeJob, startJob } from '@/api/job/sys-job'\r\n\r\nexport default {\r\n  name: 'SysJobManage',\r\n  components: {\r\n\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      id: 0,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      isEdit: false,\r\n      // 类型数据字典\r\n      typeOptions: [],\r\n      sysjobList: [],\r\n      jobGroupOptions: [],\r\n      statusOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        jobName: undefined,\r\n        jobGroup: undefined,\r\n        status: undefined\r\n\r\n      },\r\n      // 表单参数\r\n      form: {\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        jobId: [{ required: true, message: '编码不能为空', trigger: 'blur' }],\r\n        jobName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],\r\n        jobGroup: [{ required: true, message: '任务分组不能为空', trigger: 'blur' }],\r\n        cronExpression: [{ required: true, message: 'cron表达式不能为空', trigger: 'blur' }],\r\n        invokeTarget: [{ required: true, message: '调用目标不能为空', trigger: 'blur' }],\r\n        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getDicts('sys_job_group').then(response => {\r\n      this.jobGroupOptions = response.data\r\n    })\r\n\r\n    this.getDicts('sys_job_status').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询参数列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listSysJob(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.sysjobList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        jobId: undefined,\r\n        jobName: undefined,\r\n        jobGroup: undefined,\r\n        cronExpression: undefined,\r\n        invokeTarget: undefined,\r\n        args: undefined,\r\n        misfirePolicy: 1,\r\n        concurrent: 1,\r\n        jobType: 1,\r\n        status: undefined\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    jobGroupFormat(row) {\r\n      return this.selectDictLabel(this.jobGroupOptions, row.jobGroup)\r\n    },\r\n    statusFormat(row) {\r\n      return this.selectDictLabel(this.statusOptions, row.status)\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加定时任务'\r\n      this.isEdit = false\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.jobId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const jobId = row.jobId || this.ids\r\n      getSysJob(jobId).then(response => {\r\n        this.form = response.data\r\n        this.form.status = String(this.form.status)\r\n        this.form.misfirePolicy = String(this.form.misfirePolicy)\r\n        this.form.concurrent = String(this.form.concurrent)\r\n        this.form.jobType = String(this.form.jobType)\r\n        this.open = true\r\n        this.title = '修改定时任务'\r\n        this.isEdit = true\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.jobId !== undefined) {\r\n            this.form.status = parseInt(this.form.status)\r\n            this.form.misfirePolicy = parseInt(this.form.misfirePolicy)\r\n            this.form.concurrent = parseInt(this.form.concurrent)\r\n            this.form.jobType = parseInt(this.form.jobType)\r\n            updateSysJob(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            this.form.status = parseInt(this.form.status)\r\n            this.form.misfirePolicy = parseInt(this.form.misfirePolicy)\r\n            this.form.concurrent = parseInt(this.form.concurrent)\r\n            this.form.jobType = parseInt(this.form.jobType)\r\n            addSysJob(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const Ids = (row.jobId && [row.jobId]) || this.ids\r\n      this.$confirm('是否确认删除编号为\"' + Ids + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delSysJob({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    /** 开始按钮操作 */\r\n    handleStart(row) {\r\n      this.$confirm('是否确认启动编号为\"' + row.jobId + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return startJob(row.jobId)\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    /** 停止按钮操作 */\r\n    handleRemove(row) {\r\n      this.$confirm('是否确认关闭编号为\"' + row.jobId + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return removeJob(row.jobId)\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    handleLog() {\r\n      this.$router.push({ name: 'job_log', params: { }})\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}