{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Pagination\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Pagination\\index.vue", "mtime": 1753924830054}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["scrollTo", "name", "props", "total", "required", "type", "Number", "page", "default", "limit", "pageSizes", "Array", "layout", "String", "background", "Boolean", "autoScroll", "hidden", "computed", "currentPage", "get", "set", "val", "$emit", "pageSize", "methods", "handleSizeChange", "handleCurrentChange"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Pagination\\index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"{'hidden':hidden}\" class=\"pagination-container\">\r\n    <el-pagination\r\n      :background=\"background\"\r\n      :current-page.sync=\"currentPage\"\r\n      :page-size.sync=\"pageSize\"\r\n      :layout=\"layout\"\r\n      :page-sizes=\"pageSizes\"\r\n      :total=\"total\"\r\n      v-bind=\"$attrs\"\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { scrollTo } from '@/utils/scroll-to'\r\n\r\nexport default {\r\n  name: 'Pagination',\r\n  props: {\r\n    total: {\r\n      required: true,\r\n      type: Number\r\n    },\r\n    page: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    limit: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default() {\r\n        return [10, 20, 30, 50]\r\n      }\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'total, sizes, prev, pager, next, jumper'\r\n    },\r\n    background: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    hidden: {\r\n      type: <PERSON>olean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    currentPage: {\r\n      get() {\r\n        return this.page\r\n      },\r\n      set(val) {\r\n        this.$emit('update:page', val)\r\n      }\r\n    },\r\n    pageSize: {\r\n      get() {\r\n        return this.limit\r\n      },\r\n      set(val) {\r\n        this.$emit('update:limit', val)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      this.$emit('pagination', { page: this.currentPage, limit: val })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.$emit('pagination', { page: val, limit: this.pageSize })\r\n      if (this.autoScroll) {\r\n        scrollTo(0, 800)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pagination-container {\r\n  background: #fff;\r\n  padding: 32px 16px;\r\n}\r\n.pagination-container.hidden {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";AAiBA,SAASA,QAAO,QAAS,mBAAkB;AAE3C,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAEC;IACR,CAAC;IACDC,IAAI,EAAE;MACJF,IAAI,EAAEC,MAAM;MACZE,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLJ,IAAI,EAAEC,MAAM;MACZE,OAAO,EAAE;IACX,CAAC;IACDE,SAAS,EAAE;MACTL,IAAI,EAAEM,KAAK;MACXH,OAAO,WAAPA,QAAOA,CAAA,EAAG;QACR,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;MACxB;IACF,CAAC;IACDI,MAAM,EAAE;MACNP,IAAI,EAAEQ,MAAM;MACZL,OAAO,EAAE;IACX,CAAC;IACDM,UAAU,EAAE;MACVT,IAAI,EAAEU,OAAO;MACbP,OAAO,EAAE;IACX,CAAC;IACDQ,UAAU,EAAE;MACVX,IAAI,EAAEU,OAAO;MACbP,OAAO,EAAE;IACX,CAAC;IACDS,MAAM,EAAE;MACNZ,IAAI,EAAEU,OAAO;MACbP,OAAO,EAAE;IACX;EACF,CAAC;EACDU,QAAQ,EAAE;IACRC,WAAW,EAAE;MACXC,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACb,IAAG;MACjB,CAAC;MACDc,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACC,KAAK,CAAC,aAAa,EAAED,GAAG;MAC/B;IACF,CAAC;IACDE,QAAQ,EAAE;MACRJ,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACX,KAAI;MAClB,CAAC;MACDY,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACC,KAAK,CAAC,cAAc,EAAED,GAAG;MAChC;IACF;EACF,CAAC;EACDG,OAAO,EAAE;IACPC,gBAAgB,WAAhBA,gBAAgBA,CAACJ,GAAG,EAAE;MACpB,IAAI,CAACC,KAAK,CAAC,YAAY,EAAE;QAAEhB,IAAI,EAAE,IAAI,CAACY,WAAW;QAAEV,KAAK,EAAEa;MAAI,CAAC;MAC/D,IAAI,IAAI,CAACN,UAAU,EAAE;QACnBhB,QAAQ,CAAC,CAAC,EAAE,GAAG;MACjB;IACF,CAAC;IACD2B,mBAAmB,WAAnBA,mBAAmBA,CAACL,GAAG,EAAE;MACvB,IAAI,CAACC,KAAK,CAAC,YAAY,EAAE;QAAEhB,IAAI,EAAEe,GAAG;QAAEb,KAAK,EAAE,IAAI,CAACe;MAAS,CAAC;MAC5D,IAAI,IAAI,CAACR,UAAU,EAAE;QACnBhB,QAAQ,CAAC,CAAC,EAAE,GAAG;MACjB;IACF;EACF;AACF", "ignoreList": []}]}