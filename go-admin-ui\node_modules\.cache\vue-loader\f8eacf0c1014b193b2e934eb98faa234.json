{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\HeaderSearch\\index.vue", "mtime": 1753924830041}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\HeaderSearch\\index.vue"], "names": [], "mappings": ";AAoBA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC;MACH,CAAC;IACH,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;MACvD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;;MAEX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;QAE9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf;QACF;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC1B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B;QACF;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAClB;IACF;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/HeaderSearch/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :class=\"{'show':show}\" class=\"header-search\">\r\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\r\n    <el-select\r\n      ref=\"headerSearchSelect\"\r\n      v-model=\"search\"\r\n      :remote-method=\"querySearch\"\r\n      filterable\r\n      default-first-option\r\n      remote\r\n      placeholder=\"Search\"\r\n      class=\"header-search-select\"\r\n      @change=\"change\"\r\n    >\r\n      <el-option v-for=\"item in options\" :key=\"item.path\" :value=\"item\" :label=\"item.title.join(' > ')\" />\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// fuse is a lightweight fuzzy-search module\r\n// make search results more in line with expectations\r\nimport Fuse from 'fuse.js'\r\nimport path from 'path'\r\n\r\nexport default {\r\n  name: 'HeaderSearch',\r\n  data() {\r\n    return {\r\n      search: '',\r\n      options: [],\r\n      searchPool: [],\r\n      show: false,\r\n      fuse: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    routes() {\r\n      return this.$store.getters.permission_routes\r\n    }\r\n  },\r\n  watch: {\r\n    routes() {\r\n      this.searchPool = this.generateRoutes(this.routes)\r\n    },\r\n    searchPool(list) {\r\n      this.initFuse(list)\r\n    },\r\n    show(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.close)\r\n      } else {\r\n        document.body.removeEventListener('click', this.close)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.searchPool = this.generateRoutes(this.routes)\r\n  },\r\n  methods: {\r\n    click() {\r\n      this.show = !this.show\r\n      if (this.show) {\r\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\r\n      }\r\n    },\r\n    close() {\r\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\r\n      this.options = []\r\n      this.show = false\r\n    },\r\n    change(val) {\r\n      this.$router.push(val.path)\r\n      this.search = ''\r\n      this.options = []\r\n      this.$nextTick(() => {\r\n        this.show = false\r\n      })\r\n    },\r\n    initFuse(list) {\r\n      this.fuse = new Fuse(list, {\r\n        shouldSort: true,\r\n        threshold: 0.4,\r\n        location: 0,\r\n        distance: 100,\r\n        maxPatternLength: 32,\r\n        minMatchCharLength: 1,\r\n        keys: [{\r\n          name: 'title',\r\n          weight: 0.7\r\n        }, {\r\n          name: 'path',\r\n          weight: 0.3\r\n        }]\r\n      })\r\n    },\r\n    // Filter out the routes that can be displayed in the sidebar\r\n    // And generate the internationalized title\r\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\r\n      let res = []\r\n\r\n      for (const router of routes) {\r\n        // skip hidden router\r\n        if (router.hidden) { continue }\r\n\r\n        const data = {\r\n          path: path.resolve(basePath, router.path),\r\n          title: [...prefixTitle]\r\n        }\r\n\r\n        if (router.meta && router.meta.title) {\r\n          data.title = [...data.title, router.meta.title]\r\n\r\n          if (router.redirect !== 'noRedirect') {\r\n            // only push the routes with title\r\n            // special case: need to exclude parent router without redirect\r\n            res.push(data)\r\n          }\r\n        }\r\n\r\n        // recursive child routes\r\n        if (router.children) {\r\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\r\n          if (tempRoutes.length >= 1) {\r\n            res = [...res, ...tempRoutes]\r\n          }\r\n        }\r\n      }\r\n      return res\r\n    },\r\n    querySearch(query) {\r\n      if (query !== '') {\r\n        this.options = this.fuse.search(query)\r\n      } else {\r\n        this.options = []\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header-search {\r\n  font-size: 0 !important;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n    font-size: 18px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .header-search-select {\r\n    font-size: 18px;\r\n    transition: width 0.2s;\r\n    width: 0;\r\n    overflow: hidden;\r\n    background: transparent;\r\n    border-radius: 0;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    ::v-deep .el-input__inner {\r\n      border-radius: 0;\r\n      border: 0;\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n      box-shadow: none !important;\r\n      border-bottom: 1px solid #d9d9d9;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.show {\r\n    .header-search-select {\r\n      width: 210px;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}