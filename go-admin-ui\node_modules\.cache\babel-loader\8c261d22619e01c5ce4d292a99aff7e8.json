{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\permission.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\permission.js", "mtime": 1753924830227}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["asyncRoutes", "constantRoutes", "getRoutes", "Layout", "hasPermission", "roles", "route", "meta", "some", "role", "includes", "hasPathPermission", "paths", "path", "generaMenu", "routes", "data", "for<PERSON>ach", "item", "menu", "component", "loadView", "hidden", "visible", "children", "name", "menuName", "title", "icon", "noCache", "push", "view", "resolve", "require", "concat", "filterAsyncRoutes", "res", "tmp", "_objectSpread", "filterAsyncPathRoutes", "state", "addRoutes", "defaultRoutes", "topbarRouters", "sidebarRouters", "mutations", "SET_ROUTES", "SET_DEFAULT_ROUTES", "SET_TOPBAR_ROUTES", "SET_SIDEBAR_ROUTERS", "actions", "generateRoutes", "_ref", "_this", "commit", "Promise", "loadMenuData", "then", "response", "code", "$message", "message", "type", "Object", "assign", "redirect", "sidebarRoutes", "catch", "error", "console", "log", "namespaced"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/modules/permission.js"], "sourcesContent": ["import { asyncRoutes, constantRoutes } from '@/router'\r\nimport { getRoutes } from '@/api/admin/sys-role'\r\nimport Layout from '@/layout'\r\n// import sysuserindex from '@/views/sysuser/index'\r\n\r\n/**\r\n * Use meta.role to determine if the current user has permission\r\n * @param roles\r\n * @param route\r\n */\r\nfunction hasPermission(roles, route) {\r\n  if (route.meta && route.meta.roles) {\r\n    return roles.some(role => route.meta.roles.includes(role))\r\n  } else {\r\n    return true\r\n  }\r\n}\r\n\r\n/**\r\n * Use names to determine if the current user has permission\r\n * @param names\r\n * @param route\r\n */\r\nfunction hasPathPermission(paths, route) {\r\n  if (route.path) {\r\n    return paths.some(path => route.path === path.path)\r\n  } else {\r\n    return true\r\n  }\r\n}\r\n\r\n/**\r\n  * 后台查询的菜单数据拼装成路由格式的数据\r\n  * @param routes\r\n  */\r\nexport function generaMenu(routes, data) {\r\n  data.forEach(item => {\r\n    const menu = {\r\n      path: item.path,\r\n      component: item.component === 'Layout' ? Layout : loadView(item.component),\r\n      hidden: item.visible !== '0',\r\n      children: [],\r\n      name: item.menuName,\r\n      meta: {\r\n        title: item.title,\r\n        icon: item.icon,\r\n        noCache: item.noCache\r\n      }\r\n    }\r\n    if (item.children) {\r\n      generaMenu(menu.children, item.children)\r\n    }\r\n    routes.push(menu)\r\n  })\r\n}\r\n\r\nexport const loadView = (view) => { // 路由懒加载\r\n  return (resolve) => require([`@/views${view}`], resolve)\r\n}\r\n\r\n/**\r\n * Filter asynchronous routing tables by recursion\r\n * @param routes asyncRoutes\r\n * @param roles\r\n */\r\nexport function filterAsyncRoutes(routes, roles) {\r\n  const res = []\r\n\r\n  routes.forEach(route => {\r\n    const tmp = { ...route }\r\n    if (hasPermission(roles, tmp)) {\r\n      if (tmp.children) {\r\n        tmp.children = filterAsyncRoutes(tmp.children, roles)\r\n      }\r\n      res.push(tmp)\r\n    }\r\n  })\r\n\r\n  return res\r\n}\r\n\r\n/**\r\n * Filter asynchronous routing tables by recursion\r\n * @param routes asyncRoutes\r\n * @param components\r\n */\r\nexport function filterAsyncPathRoutes(routes, paths) {\r\n  const res = []\r\n\r\n  routes.forEach(route => {\r\n    const tmp = { ...route }\r\n    if (hasPathPermission(paths, tmp)) {\r\n      if (tmp.children) {\r\n        tmp.children = filterAsyncPathRoutes(tmp.children, paths)\r\n      }\r\n      res.push(tmp)\r\n    }\r\n  })\r\n\r\n  return res\r\n}\r\n\r\nconst state = {\r\n  routes: [],\r\n  addRoutes: [],\r\n  defaultRoutes: [],\r\n  topbarRouters: [],\r\n  sidebarRouters: []\r\n}\r\n\r\nconst mutations = {\r\n  SET_ROUTES: (state, routes) => {\r\n    state.addRoutes = routes\r\n    state.routes = constantRoutes.concat(routes)\r\n  },\r\n  SET_DEFAULT_ROUTES: (state, routes) => {\r\n    state.defaultRoutes = constantRoutes.concat(routes)\r\n  },\r\n  SET_TOPBAR_ROUTES: (state, routes) => {\r\n    // 顶部导航菜单默认添加统计报表栏指向首页\r\n    // const index = [{\r\n    //   path: 'dashboard',\r\n    //   meta: { title: '统计报表', icon: 'dashboard' }\r\n    // }]\r\n    state.topbarRouters = routes // .concat(index)\r\n  },\r\n  SET_SIDEBAR_ROUTERS: (state, routes) => {\r\n    state.sidebarRouters = routes\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  generateRoutes({ commit }, roles) {\r\n    return new Promise(resolve => {\r\n      const loadMenuData = []\r\n\r\n      getRoutes().then(response => {\r\n        // console.log(JSON.stringify(response))\r\n        let data = response\r\n        if (response.code !== 200) {\r\n          this.$message({\r\n            message: '菜单数据加载异常',\r\n            type: 0\r\n          })\r\n        } else {\r\n          data = response.data\r\n          Object.assign(loadMenuData, data)\r\n\r\n          generaMenu(asyncRoutes, loadMenuData)\r\n          asyncRoutes.push({ path: '*', redirect: '/', hidden: true })\r\n          commit('SET_ROUTES', asyncRoutes)\r\n          const sidebarRoutes = []\r\n          generaMenu(sidebarRoutes, loadMenuData)\r\n          commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(sidebarRoutes))\r\n          commit('SET_DEFAULT_ROUTES', sidebarRoutes)\r\n          commit('SET_TOPBAR_ROUTES', sidebarRoutes)\r\n          resolve(asyncRoutes)\r\n        }\r\n      }).catch(error => {\r\n        console.log(error)\r\n      })\r\n    })\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,WAAW,EAAEC,cAAc,QAAQ,UAAU;AACtD,SAASC,SAAS,QAAQ,sBAAsB;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACF,KAAK,EAAE;IAClC,OAAOA,KAAK,CAACG,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAIH,KAAK,CAACC,IAAI,CAACF,KAAK,CAACK,QAAQ,CAACD,IAAI,CAAC;IAAA,EAAC;EAC5D,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACC,KAAK,EAAEN,KAAK,EAAE;EACvC,IAAIA,KAAK,CAACO,IAAI,EAAE;IACd,OAAOD,KAAK,CAACJ,IAAI,CAAC,UAAAK,IAAI;MAAA,OAAIP,KAAK,CAACO,IAAI,KAAKA,IAAI,CAACA,IAAI;IAAA,EAAC;EACrD,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACvCA,IAAI,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;IACnB,IAAMC,IAAI,GAAG;MACXN,IAAI,EAAEK,IAAI,CAACL,IAAI;MACfO,SAAS,EAAEF,IAAI,CAACE,SAAS,KAAK,QAAQ,GAAGjB,MAAM,GAAGkB,QAAQ,CAACH,IAAI,CAACE,SAAS,CAAC;MAC1EE,MAAM,EAAEJ,IAAI,CAACK,OAAO,KAAK,GAAG;MAC5BC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAEP,IAAI,CAACQ,QAAQ;MACnBnB,IAAI,EAAE;QACJoB,KAAK,EAAET,IAAI,CAACS,KAAK;QACjBC,IAAI,EAAEV,IAAI,CAACU,IAAI;QACfC,OAAO,EAAEX,IAAI,CAACW;MAChB;IACF,CAAC;IACD,IAAIX,IAAI,CAACM,QAAQ,EAAE;MACjBV,UAAU,CAACK,IAAI,CAACK,QAAQ,EAAEN,IAAI,CAACM,QAAQ,CAAC;IAC1C;IACAT,MAAM,CAACe,IAAI,CAACX,IAAI,CAAC;EACnB,CAAC,CAAC;AACJ;AAEA,OAAO,IAAME,QAAQ,GAAG,SAAXA,QAAQA,CAAIU,IAAI,EAAK;EAAE;EAClC,OAAO,UAACC,OAAO;IAAA,OAAKC,OAAO,CAAC,WAAAC,MAAA,CAAWH,IAAI,EAAG,EAAEC,OAAO,CAAC;EAAA;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,iBAAiBA,CAACpB,MAAM,EAAEV,KAAK,EAAE;EAC/C,IAAM+B,GAAG,GAAG,EAAE;EAEdrB,MAAM,CAACE,OAAO,CAAC,UAAAX,KAAK,EAAI;IACtB,IAAM+B,GAAG,GAAAC,aAAA,KAAQhC,KAAK,CAAE;IACxB,IAAIF,aAAa,CAACC,KAAK,EAAEgC,GAAG,CAAC,EAAE;MAC7B,IAAIA,GAAG,CAACb,QAAQ,EAAE;QAChBa,GAAG,CAACb,QAAQ,GAAGW,iBAAiB,CAACE,GAAG,CAACb,QAAQ,EAAEnB,KAAK,CAAC;MACvD;MACA+B,GAAG,CAACN,IAAI,CAACO,GAAG,CAAC;IACf;EACF,CAAC,CAAC;EAEF,OAAOD,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,qBAAqBA,CAACxB,MAAM,EAAEH,KAAK,EAAE;EACnD,IAAMwB,GAAG,GAAG,EAAE;EAEdrB,MAAM,CAACE,OAAO,CAAC,UAAAX,KAAK,EAAI;IACtB,IAAM+B,GAAG,GAAAC,aAAA,KAAQhC,KAAK,CAAE;IACxB,IAAIK,iBAAiB,CAACC,KAAK,EAAEyB,GAAG,CAAC,EAAE;MACjC,IAAIA,GAAG,CAACb,QAAQ,EAAE;QAChBa,GAAG,CAACb,QAAQ,GAAGe,qBAAqB,CAACF,GAAG,CAACb,QAAQ,EAAEZ,KAAK,CAAC;MAC3D;MACAwB,GAAG,CAACN,IAAI,CAACO,GAAG,CAAC;IACf;EACF,CAAC,CAAC;EAEF,OAAOD,GAAG;AACZ;AAEA,IAAMI,KAAK,GAAG;EACZzB,MAAM,EAAE,EAAE;EACV0B,SAAS,EAAE,EAAE;EACbC,aAAa,EAAE,EAAE;EACjBC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE;AAClB,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE,SAAZA,UAAUA,CAAGN,KAAK,EAAEzB,MAAM,EAAK;IAC7ByB,KAAK,CAACC,SAAS,GAAG1B,MAAM;IACxByB,KAAK,CAACzB,MAAM,GAAGd,cAAc,CAACiC,MAAM,CAACnB,MAAM,CAAC;EAC9C,CAAC;EACDgC,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAGP,KAAK,EAAEzB,MAAM,EAAK;IACrCyB,KAAK,CAACE,aAAa,GAAGzC,cAAc,CAACiC,MAAM,CAACnB,MAAM,CAAC;EACrD,CAAC;EACDiC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGR,KAAK,EAAEzB,MAAM,EAAK;IACpC;IACA;IACA;IACA;IACA;IACAyB,KAAK,CAACG,aAAa,GAAG5B,MAAM,EAAC;EAC/B,CAAC;EACDkC,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGT,KAAK,EAAEzB,MAAM,EAAK;IACtCyB,KAAK,CAACI,cAAc,GAAG7B,MAAM;EAC/B;AACF,CAAC;AAED,IAAMmC,OAAO,GAAG;EACdC,cAAc,WAAdA,cAAcA,CAAAC,IAAA,EAAa/C,KAAK,EAAE;IAAA,IAAAgD,KAAA;IAAA,IAAjBC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACrB,OAAO,IAAIC,OAAO,CAAC,UAAAvB,OAAO,EAAI;MAC5B,IAAMwB,YAAY,GAAG,EAAE;MAEvBtD,SAAS,CAAC,CAAC,CAACuD,IAAI,CAAC,UAAAC,QAAQ,EAAI;QAC3B;QACA,IAAI1C,IAAI,GAAG0C,QAAQ;QACnB,IAAIA,QAAQ,CAACC,IAAI,KAAK,GAAG,EAAE;UACzBN,KAAI,CAACO,QAAQ,CAAC;YACZC,OAAO,EAAE,UAAU;YACnBC,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,CAAC,MAAM;UACL9C,IAAI,GAAG0C,QAAQ,CAAC1C,IAAI;UACpB+C,MAAM,CAACC,MAAM,CAACR,YAAY,EAAExC,IAAI,CAAC;UAEjCF,UAAU,CAACd,WAAW,EAAEwD,YAAY,CAAC;UACrCxD,WAAW,CAAC8B,IAAI,CAAC;YAAEjB,IAAI,EAAE,GAAG;YAAEoD,QAAQ,EAAE,GAAG;YAAE3C,MAAM,EAAE;UAAK,CAAC,CAAC;UAC5DgC,MAAM,CAAC,YAAY,EAAEtD,WAAW,CAAC;UACjC,IAAMkE,aAAa,GAAG,EAAE;UACxBpD,UAAU,CAACoD,aAAa,EAAEV,YAAY,CAAC;UACvCF,MAAM,CAAC,qBAAqB,EAAErD,cAAc,CAACiC,MAAM,CAACgC,aAAa,CAAC,CAAC;UACnEZ,MAAM,CAAC,oBAAoB,EAAEY,aAAa,CAAC;UAC3CZ,MAAM,CAAC,mBAAmB,EAAEY,aAAa,CAAC;UAC1ClC,OAAO,CAAChC,WAAW,CAAC;QACtB;MACF,CAAC,CAAC,CAACmE,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbG,UAAU,EAAE,IAAI;EAChB/B,KAAK,EAALA,KAAK;EACLK,SAAS,EAATA,SAAS;EACTK,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}