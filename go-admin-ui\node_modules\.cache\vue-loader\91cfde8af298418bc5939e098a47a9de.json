{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1753924830293}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCBQYW5UaHVtYiBmcm9tICdAL2NvbXBvbmVudHMvUGFuVGh1bWInDQppbXBvcnQgR2l0aHViQ29ybmVyIGZyb20gJ0AvY29tcG9uZW50cy9HaXRodWJDb3JuZXInDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0Rhc2hib2FyZEVkaXRvcicsDQogIGNvbXBvbmVudHM6IHsgUGFuVGh1bWIsIEdpdGh1YkNvcm5lciB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBlbXB0eUdpZjogJ2h0dHBzOi8vd3BpbWcud2FsbHN0Y24uY29tLzBlMDNiN2RhLWRiOWUtNDgxOS1iYTEwLTkwMTZkZGZkYWVkMycNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycyhbDQogICAgICAnbmFtZScsDQogICAgICAnYXZhdGFyJywNCiAgICAgICdyb2xlcycNCiAgICBdKQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue"], "names": [], "mappings": ";AAoBA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC;EACH;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/editor/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <div class=\" clearfix\">\r\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\r\n        Your roles:\r\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\r\n      </pan-thumb>\r\n      <github-corner style=\"position: absolute; top: 0px; border: 0; right: 0;\" />\r\n      <div class=\"info-container\">\r\n        <span class=\"display_name\">{{ name }}</span>\r\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <img :src=\"emptyGif\" class=\"emptyGif\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport PanThumb from '@/components/PanThumb'\r\nimport GithubCorner from '@/components/GithubCorner'\r\n\r\nexport default {\r\n  name: 'DashboardEditor',\r\n  components: { PanThumb, GithubCorner },\r\n  data() {\r\n    return {\r\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'name',\r\n      'avatar',\r\n      'roles'\r\n    ])\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .emptyGif {\r\n    display: block;\r\n    width: 45%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .dashboard-editor-container {\r\n    background-color: #e3e3e3;\r\n    min-height: 100vh;\r\n    padding: 50px 60px 0px;\r\n    .pan-info-roles {\r\n      font-size: 12px;\r\n      font-weight: 700;\r\n      color: #333;\r\n      display: block;\r\n    }\r\n    .info-container {\r\n      position: relative;\r\n      margin-left: 190px;\r\n      height: 150px;\r\n      line-height: 200px;\r\n      .display_name {\r\n        font-size: 48px;\r\n        line-height: 48px;\r\n        color: #212121;\r\n        position: absolute;\r\n        top: 25px;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}