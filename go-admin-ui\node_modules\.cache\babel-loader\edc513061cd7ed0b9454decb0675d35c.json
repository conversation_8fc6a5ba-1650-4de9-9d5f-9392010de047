{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\AutoWidthOption.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\AutoWidthOption.vue", "mtime": 1753924830409}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICB2YWx1ZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiB0cnVlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgYXV0b1dpZHRoOiB7CiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLnZhbHVlOwogICAgICB9LAogICAgICBzZXQ6IGZ1bmN0aW9uIHNldCh2YWwpIHsKICAgICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIHZhbCk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["props", "value", "type", "Boolean", "default", "computed", "autoWidth", "get", "set", "val", "$emit"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\AutoWidthOption.vue"], "sourcesContent": ["<template>\r\n  <div style=\"display:inline-block;\">\r\n    <label class=\"radio-label\">Cell Auto-Width: </label>\r\n    <el-radio-group v-model=\"autoWidth\">\r\n      <el-radio :label=\"true\" border>\r\n        True\r\n      </el-radio>\r\n      <el-radio :label=\"false\" border>\r\n        False\r\n      </el-radio>\r\n    </el-radio-group>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  computed: {\r\n    autoWidth: {\r\n      get() {\r\n        return this.value\r\n      },\r\n      set(val) {\r\n        this.$emit('input', val)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAeA,eAAe;EACbA,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,SAAS,EAAE;MACTC,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACN,KAAI;MAClB,CAAC;MACDO,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACC,KAAK,CAAC,OAAO,EAAED,GAAG;MACzB;IACF;EACF;AACF", "ignoreList": []}]}