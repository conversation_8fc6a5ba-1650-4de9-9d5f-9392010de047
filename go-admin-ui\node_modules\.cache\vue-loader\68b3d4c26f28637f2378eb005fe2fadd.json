{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue?vue&type=style&index=0&id=126b135a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1753924830207}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZHJhd2VyLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDI0cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgd29yZC13cmFwOiBicmVhay13b3JkOw0KDQogIC5kcmF3ZXItdGl0bGUgew0KICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjg1KTsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgbGluZS1oZWlnaHQ6IDIycHg7DQogIH0NCg0KICAuZHJhd2VyLWl0ZW0gew0KICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC42NSk7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIHBhZGRpbmc6IDEycHggMDsNCiAgfQ0KDQogIC5kcmF3ZXItc3dpdGNoIHsNCiAgICBmbG9hdDogcmlnaHQNCiAgfQ0KfQ0KLnNldHRpbmctZHJhd2VyLWNvbnRlbnR7DQogIC5zZXR0aW5nLWRyYXdlci10aXRsZXsNCiAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgIGNvbG9yOiByZ2JhKDAsMCwwLC44NSk7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAyMnB4Ow0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB9DQogIC5zZXR0aW5nLWRyYXdlci1ibG9jay1jaGVjYm94ew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgLnNldHRpbmctZHJhd2VyLWJsb2NrLWNoZWNib3gtaXRlbSB7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxNnB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgaW1new0KICAgICAgICAgIHdpZHRoOiA0OHB4Ow0KICAgICAgICAgIGhlaWdodDogNDhweDsNCiAgICAgICAgfQ0KICAgICAgICAuc2V0dGluZy1kcmF3ZXItYmxvY2stY2hlY2JveC1zZWxlY3RJY29uew0KICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgICAgdG9wOiAwOw0KICAgICAgICAgICAgcmlnaHQ6IDA7DQogICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICAgIHBhZGRpbmctdG9wOiAxNXB4Ow0KICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiAyNHB4Ow0KICAgICAgICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue"], "names": [], "mappings": ";AAwJA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB;IACJ;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/Settings/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"drawer-container\">\r\n    <div>\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          页面设置\r\n        </div>\r\n        <div class=\"setting-drawer-block-checbox\">\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('light')\">\r\n            <img src=\"@/assets/light.svg\" alt=\"light\">\r\n            <div v-if=\"themeStyle === 'light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" />\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('dark')\">\r\n            <img src=\"@/assets/dark.svg\" alt=\"dark\">\r\n            <div v-if=\"themeStyle === 'dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" />\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-divider />\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          主题设置\r\n        </div>\r\n        <div class=\"drawer-item\">\r\n          <span>主题颜色</span>\r\n          <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\r\n        </div>\r\n      </div>\r\n      <el-divider />\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          布局设置\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启任务栏</span>\r\n          <el-switch v-model=\"tagsView\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>Header 固定</span>\r\n          <el-switch v-model=\"fixedHeader\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>侧边栏Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {\r\n      activeColor: this.$store.state.settings.theme\r\n    }\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    },\r\n    themeStyle() {\r\n      return this.$store.state.settings.themeStyle\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.commit('permission/SET_SIDEBAR_ROUTERS', this.$store.state.permission.defaultRoutes)\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.activeColor = val\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'themeStyle',\r\n        value: val\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer-container {\r\n  padding: 24px;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  word-wrap: break-word;\r\n\r\n  .drawer-title {\r\n    margin-bottom: 12px;\r\n    color: rgba(0, 0, 0, .85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n  }\r\n\r\n  .drawer-item {\r\n    color: rgba(0, 0, 0, .65);\r\n    font-size: 14px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .drawer-switch {\r\n    float: right\r\n  }\r\n}\r\n.setting-drawer-content{\r\n  .setting-drawer-title{\r\n    margin-bottom: 12px;\r\n    color: rgba(0,0,0,.85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n    font-weight: bold;\r\n  }\r\n  .setting-drawer-block-checbox{\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n        img{\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n        .setting-drawer-block-checbox-selectIcon{\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            padding-top: 15px;\r\n            padding-left: 24px;\r\n            color: #1890ff;\r\n            font-weight: 700;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}