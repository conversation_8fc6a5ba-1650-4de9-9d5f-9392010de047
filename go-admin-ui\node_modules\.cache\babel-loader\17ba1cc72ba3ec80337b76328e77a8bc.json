{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\RightPanel\\index.vue", "mtime": 1753924830058}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0IHsgYWRkQ2xhc3MsIHJlbW92ZUNsYXNzIH0gZnJvbSAnQC91dGlscyc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUmlnaHRQYW5lbCcsCiAgcHJvcHM6IHsKICAgIGNsaWNrTm90Q2xvc2U6IHsKICAgICAgZGVmYXVsdDogZmFsc2UsCiAgICAgIHR5cGU6IEJvb2xlYW4KICAgIH0sCiAgICBidXR0b25Ub3A6IHsKICAgICAgZGVmYXVsdDogMjUwLAogICAgICB0eXBlOiBOdW1iZXIKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93OiBmYWxzZQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICB0aGVtZTogZnVuY3Rpb24gdGhlbWUoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50aGVtZTsKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBzaG93OiBmdW5jdGlvbiBzaG93KHZhbHVlKSB7CiAgICAgIGlmICh2YWx1ZSAmJiAhdGhpcy5jbGlja05vdENsb3NlKSB7CiAgICAgICAgdGhpcy5hZGRFdmVudENsaWNrKCk7CiAgICAgIH0KICAgICAgaWYgKHZhbHVlKSB7CiAgICAgICAgYWRkQ2xhc3MoZG9jdW1lbnQuYm9keSwgJ3Nob3dSaWdodFBhbmVsJyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmVtb3ZlQ2xhc3MoZG9jdW1lbnQuYm9keSwgJ3Nob3dSaWdodFBhbmVsJyk7CiAgICAgIH0KICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluc2VydFRvQm9keSgpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHZhciBlbHggPSB0aGlzLiRyZWZzLnJpZ2h0UGFuZWw7CiAgICBlbHgucmVtb3ZlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBhZGRFdmVudENsaWNrOiBmdW5jdGlvbiBhZGRFdmVudENsaWNrKCkgewogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcik7CiAgICB9LAogICAgY2xvc2VTaWRlYmFyOiBmdW5jdGlvbiBjbG9zZVNpZGViYXIoZXZ0KSB7CiAgICAgIHZhciBwYXJlbnQgPSBldnQudGFyZ2V0LmNsb3Nlc3QoJy5yaWdodFBhbmVsJyk7CiAgICAgIGlmICghcGFyZW50KSB7CiAgICAgICAgdGhpcy5zaG93ID0gZmFsc2U7CiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5jbG9zZVNpZGViYXIpOwogICAgICB9CiAgICB9LAogICAgaW5zZXJ0VG9Cb2R5OiBmdW5jdGlvbiBpbnNlcnRUb0JvZHkoKSB7CiAgICAgIHZhciBlbHggPSB0aGlzLiRyZWZzLnJpZ2h0UGFuZWw7CiAgICAgIHZhciBib2R5ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignYm9keScpOwogICAgICBib2R5Lmluc2VydEJlZm9yZShlbHgsIGJvZHkuZmlyc3RDaGlsZCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["addClass", "removeClass", "name", "props", "clickNotClose", "default", "type", "Boolean", "buttonTop", "Number", "data", "show", "computed", "theme", "$store", "state", "settings", "watch", "value", "addEventClick", "document", "body", "mounted", "insertToBody", "<PERSON><PERSON><PERSON><PERSON>", "elx", "$refs", "rightPanel", "remove", "methods", "window", "addEventListener", "closeSidebar", "evt", "parent", "target", "closest", "removeEventListener", "querySelector", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\RightPanel\\index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"rightPanel\" :class=\"{show:show}\" class=\"rightPanel-container\">\r\n    <div class=\"rightPanel-background\" />\r\n    <div class=\"rightPanel\">\r\n      <div class=\"handle-button\" :style=\"{'top':buttonTop+'px','background-color':theme}\" @click=\"show=!show\">\r\n        <i :class=\"show?'el-icon-close':'el-icon-setting'\" />\r\n      </div>\r\n      <div class=\"rightPanel-items\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { addClass, removeClass } from '@/utils'\r\n\r\nexport default {\r\n  name: 'RightPanel',\r\n  props: {\r\n    clickNotClose: {\r\n      default: false,\r\n      type: Boolean\r\n    },\r\n    buttonTop: {\r\n      default: 250,\r\n      type: Number\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      show: false\r\n    }\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    show(value) {\r\n      if (value && !this.clickNotClose) {\r\n        this.addEventClick()\r\n      }\r\n      if (value) {\r\n        addClass(document.body, 'showRightPanel')\r\n      } else {\r\n        removeClass(document.body, 'showRightPanel')\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.insertToBody()\r\n  },\r\n  beforeDestroy() {\r\n    const elx = this.$refs.rightPanel\r\n    elx.remove()\r\n  },\r\n  methods: {\r\n    addEventClick() {\r\n      window.addEventListener('click', this.closeSidebar)\r\n    },\r\n    closeSidebar(evt) {\r\n      const parent = evt.target.closest('.rightPanel')\r\n      if (!parent) {\r\n        this.show = false\r\n        window.removeEventListener('click', this.closeSidebar)\r\n      }\r\n    },\r\n    insertToBody() {\r\n      const elx = this.$refs.rightPanel\r\n      const body = document.querySelector('body')\r\n      body.insertBefore(elx, body.firstChild)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.showRightPanel {\r\n  overflow: hidden;\r\n  position: relative;\r\n  width: calc(100% - 15px);\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rightPanel-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  opacity: 0;\r\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\r\n  background: rgba(0, 0, 0, .2);\r\n  z-index: -1;\r\n}\r\n\r\n.rightPanel {\r\n  width: 100%;\r\n  max-width: 300px;\r\n  height: 100vh;\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\r\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n  transform: translate(100%);\r\n  background: #fff;\r\n  z-index: 40000;\r\n}\r\n\r\n.show {\r\n  transition: all .3s cubic-bezier(.7, .3, .1, 1);\r\n\r\n  .rightPanel-background {\r\n    z-index: 20000;\r\n    opacity: 1;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .rightPanel {\r\n    transform: translate(0);\r\n  }\r\n}\r\n\r\n.handle-button {\r\n  width: 48px;\r\n  height: 48px;\r\n  position: absolute;\r\n  left: -48px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  border-radius: 6px 0 0 6px !important;\r\n  z-index: 0;\r\n  pointer-events: auto;\r\n  cursor: pointer;\r\n  color: #fff;\r\n  line-height: 48px;\r\n  i {\r\n    font-size: 24px;\r\n    line-height: 48px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAeA,SAASA,QAAQ,EAAEC,WAAU,QAAS,SAAQ;AAE9C,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,aAAa,EAAE;MACbC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAEC;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,OAAO,EAAE,GAAG;MACZC,IAAI,EAAEG;IACR;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE;IACR;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACH,KAAI;IACxC;EACF,CAAC;EACDI,KAAK,EAAE;IACLN,IAAI,WAAJA,IAAIA,CAACO,KAAK,EAAE;MACV,IAAIA,KAAI,IAAK,CAAC,IAAI,CAACd,aAAa,EAAE;QAChC,IAAI,CAACe,aAAa,CAAC;MACrB;MACA,IAAID,KAAK,EAAE;QACTlB,QAAQ,CAACoB,QAAQ,CAACC,IAAI,EAAE,gBAAgB;MAC1C,OAAO;QACLpB,WAAW,CAACmB,QAAQ,CAACC,IAAI,EAAE,gBAAgB;MAC7C;IACF;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,CAAC;EACpB,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAMC,GAAE,GAAI,IAAI,CAACC,KAAK,CAACC,UAAS;IAChCF,GAAG,CAACG,MAAM,CAAC;EACb,CAAC;EACDC,OAAO,EAAE;IACPV,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACdW,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,YAAY;IACpD,CAAC;IACDA,YAAY,WAAZA,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAMC,MAAK,GAAID,GAAG,CAACE,MAAM,CAACC,OAAO,CAAC,aAAa;MAC/C,IAAI,CAACF,MAAM,EAAE;QACX,IAAI,CAACvB,IAAG,GAAI,KAAI;QAChBmB,MAAM,CAACO,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACL,YAAY;MACvD;IACF,CAAC;IACDT,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAME,GAAE,GAAI,IAAI,CAACC,KAAK,CAACC,UAAS;MAChC,IAAMN,IAAG,GAAID,QAAQ,CAACkB,aAAa,CAAC,MAAM;MAC1CjB,IAAI,CAACkB,YAAY,CAACd,GAAG,EAAEJ,IAAI,CAACmB,UAAU;IACxC;EACF;AACF", "ignoreList": []}]}