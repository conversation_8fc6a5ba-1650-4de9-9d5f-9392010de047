{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue?vue&type=template&id=2b33be98&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1753924830430}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9InNvY2lhbC1zaWdudXAtY29udGFpbmVyIj4NCiAgICA8ZGl2IGNsYXNzPSJzaWduLWJ0biIgQGNsaWNrPSJ3ZWNoYXRIYW5kbGVDbGljaygnd2VjaGF0JykiPg0KICAgICAgPHNwYW4gY2xhc3M9Ind4LXN2Zy1jb250YWluZXIiPjxzdmctaWNvbiBpY29uLWNsYXNzPSJ3ZWNoYXQiIGNsYXNzPSJpY29uIiAvPjwvc3Bhbj4NCiAgICAgIFdlQ2hhdA0KICAgIDwvZGl2Pg0KICAgIDxkaXYgY2xhc3M9InNpZ24tYnRuIiBAY2xpY2s9InRlbmNlbnRIYW5kbGVDbGljaygndGVuY2VudCcpIj4NCiAgICAgIDxzcGFuIGNsYXNzPSJxcS1zdmctY29udGFpbmVyIj48c3ZnLWljb24gaWNvbi1jbGFzcz0icXEiIGNsYXNzPSJpY29uIiAvPjwvc3Bhbj4NCiAgICAgIFFRDQogICAgPC9kaXY+DQogIDwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClF,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/login/components/SocialSignin.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"social-signup-container\">\r\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\r\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\r\n      WeChat\r\n    </div>\r\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\r\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\r\n      QQ\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import openWindow from '@/utils/open-window'\r\n\r\nexport default {\r\n  name: 'SocialSignin',\r\n  methods: {\r\n    wechatHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const appid = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    },\r\n    tencentHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const client_id = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .social-signup-container {\r\n    margin: 20px 0;\r\n    .sign-btn {\r\n      display: inline-block;\r\n      cursor: pointer;\r\n    }\r\n    .icon {\r\n      color: #fff;\r\n      font-size: 24px;\r\n      margin-top: 8px;\r\n    }\r\n    .wx-svg-container,\r\n    .qq-svg-container {\r\n      display: inline-block;\r\n      width: 40px;\r\n      height: 40px;\r\n      line-height: 40px;\r\n      text-align: center;\r\n      padding-top: 1px;\r\n      border-radius: 4px;\r\n      margin-bottom: 20px;\r\n      margin-right: 5px;\r\n    }\r\n    .wx-svg-container {\r\n      background-color: #24da70;\r\n    }\r\n    .qq-svg-container {\r\n      background-color: #6BA2D6;\r\n      margin-left: 50px;\r\n    }\r\n  }\r\n</style>\r\n"]}]}