{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue", "mtime": 1753924830445}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgUGFuVGh1bWIgZnJvbSAnQC9jb21wb25lbnRzL1BhblRodW1iJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsgUGFuVGh1bWIgfSwNCiAgcHJvcHM6IHsNCiAgICB1c2VyOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgbmFtZTogJycsDQogICAgICAgICAgZW1haWw6ICcnLA0KICAgICAgICAgIGF2YXRhcjogJycsDQogICAgICAgICAgcm9sZXM6ICcnDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue"], "names": [], "mappings": ";AAuDA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV;MACF;IACF;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/UserCard.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card style=\"margin-bottom:20px;\">\r\n    <div slot=\"header\" class=\"clearfix\">\r\n      <span>About me</span>\r\n    </div>\r\n\r\n    <div class=\"user-profile\">\r\n      <div class=\"box-center\">\r\n        <pan-thumb :image=\"user.avatar\" :height=\"'100px'\" :width=\"'100px'\" :hoverable=\"false\">\r\n          <div>Hello</div>\r\n          {{ user.role }}\r\n        </pan-thumb>\r\n      </div>\r\n      <div class=\"box-center\">\r\n        <div class=\"user-name text-center\">{{ user.name }}</div>\r\n        <div class=\"user-role text-center text-muted\">{{ user.role | uppercaseFirst }}</div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"user-bio\">\r\n      <div class=\"user-education user-bio-section\">\r\n        <div class=\"user-bio-section-header\"><svg-icon icon-class=\"education\" /><span>Education</span></div>\r\n        <div class=\"user-bio-section-body\">\r\n          <div class=\"text-muted\">\r\n            JS in Computer Science from the University of Technology\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"user-skills user-bio-section\">\r\n        <div class=\"user-bio-section-header\"><svg-icon icon-class=\"skill\" /><span>Skills</span></div>\r\n        <div class=\"user-bio-section-body\">\r\n          <div class=\"progress-item\">\r\n            <span>Vue</span>\r\n            <el-progress :percentage=\"70\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>JavaScript</span>\r\n            <el-progress :percentage=\"18\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>Css</span>\r\n            <el-progress :percentage=\"12\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>ESLint</span>\r\n            <el-progress :percentage=\"100\" status=\"success\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport PanThumb from '@/components/PanThumb'\r\n\r\nexport default {\r\n  components: { PanThumb },\r\n  props: {\r\n    user: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          name: '',\r\n          email: '',\r\n          avatar: '',\r\n          roles: ''\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box-center {\r\n  margin: 0 auto;\r\n  display: table;\r\n}\r\n\r\n.text-muted {\r\n  color: #777;\r\n}\r\n\r\n.user-profile {\r\n  .user-name {\r\n    font-weight: bold;\r\n  }\r\n\r\n  .box-center {\r\n    padding-top: 10px;\r\n  }\r\n\r\n  .user-role {\r\n    padding-top: 10px;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .box-social {\r\n    padding-top: 30px;\r\n\r\n    .el-table {\r\n      border-top: 1px solid #dfe6ec;\r\n    }\r\n  }\r\n\r\n  .user-follow {\r\n    padding-top: 20px;\r\n  }\r\n}\r\n\r\n.user-bio {\r\n  margin-top: 20px;\r\n  color: #606266;\r\n\r\n  span {\r\n    padding-left: 4px;\r\n  }\r\n\r\n  .user-bio-section {\r\n    font-size: 14px;\r\n    padding: 15px 0;\r\n\r\n    .user-bio-section-header {\r\n      border-bottom: 1px solid #dfe6ec;\r\n      padding-bottom: 10px;\r\n      margin-bottom: 10px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}