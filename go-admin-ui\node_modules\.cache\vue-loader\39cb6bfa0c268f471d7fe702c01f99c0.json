{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\sys-tools\\monitor.vue?vue&type=style&index=0&id=5f5cf47c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\sys-tools\\monitor.vue", "mtime": 1753924830482}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoubGluZXsNCiAgbGluZS1oZWlnaHQ6IDQ5cHg7DQogIGZvbnQtc2l6ZTogMTRweCA7DQogIHBhZGRpbmctbGVmdDogNXB4ICFpbXBvcnRhbnQ7DQogIHBhZGRpbmctcmlnaHQ6IDVweCAhaW1wb3J0YW50Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U2ZWJmNTsNCiAgLmxpbmUtdmFsdWV7DQogICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgY29sb3I6ICM5Njk3OTk7DQogIH0NCn0NCg0KLm1vbml0b3Igew0KICAubW9uaXRvci1oZWFkZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgfQ0KICAubW9uaXRvci1wcm9ncmVzc3sNCiAgICBwYWRkaW5nLXRvcDogMTVweDsNCiAgfQ0KfQ0KDQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\sys-tools\\monitor.vue"], "names": [], "mappings": ";AAyKA,CAAC,CAAC,CAAC,CAAC,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/sys-tools/monitor.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <BasicLayout>\r\n      <template #wrapper>\r\n        <el-row :gutter=\"10\" class=\"mb10\">\r\n          <el-col :sm=\"24\" :md=\"8\">\r\n            <el-card v-if=\"info.cpu\" class=\"box-card\" shadow=\"always\" :body-style=\"{paddingTop:'0 !important'}\">\r\n              <div slot=\"header\" class=\"clearfix\">\r\n                <el-row :gutter=\"10\">\r\n                  <el-col :sm=\"24\" :md=\"8\">\r\n                    <el-tag\r\n                      type=\"success\"\r\n                      effect=\"dark\"\r\n                    >\r\n                      Runing\r\n                    </el-tag>\r\n                  </el-col>\r\n                  <el-col :sm=\"24\" :md=\"8\" class=\"\" style=\"line-height:28px;text-align:center;\">\r\n                    {{ info.location }}\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n              <div class=\"monitor\" style=\"padding-top:0px;\">\r\n                <div class=\"monitor-content\">\r\n                  <el-row :gutter=\"10\">\r\n                    <el-col :sm=\"24\" :md=\"12\">\r\n                      <Cell label=\"系统\" :value=\"info.os.goOs\" border />\r\n                      <Cell label=\"内存\" :value=\"`${info.mem.used}MB/${info.mem.total}MB`\" border />\r\n                      <Cell label=\"交换\" :value=\"`${info.swap.used}/${info.swap.total}`\" border />\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"12\">\r\n                      <Cell label=\"时间\" :value=\"info.os.time\" border />\r\n                      <Cell label=\"在线\" :value=\"`${info.bootTime}小时`\" border />\r\n                      <Cell label=\"硬盘\" :value=\"`${info.disk.used}GB/${info.disk.total}GB`\" border />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\">\r\n                    <el-col :sm=\"12\" :md=\"12\" class=\"line\">\r\n                      <el-row>\r\n                        <el-col span=\"12\" :sm=\"8\" :md=\"8\" xs=\"12\">\r\n                          下载<i class=\"el-icon-caret-bottom\" />\r\n                        </el-col>\r\n                        <el-col span=\"12\" :sm=\"16\" :md=\"16\" xs=\"12\" class=\"line-value\">\r\n                          {{ info.net.in }}KB\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                    <el-col :sm=\"12\" :md=\"12\" class=\"line\">\r\n                      <el-row border>\r\n                        <el-col span=\"12\" :sm=\"6\" :md=\"8\">\r\n                          上传<i class=\"el-icon-caret-top\" />\r\n                        </el-col>\r\n                        <el-col span=\"12\" :sm=\"6\" :md=\"16\" class=\"line-value\">\r\n                          {{ info.net.out }}KB\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\" class=\"monitor-progress\">\r\n                    <el-col :sm=\"24\" :md=\"4\">\r\n                      CPU\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"20\">\r\n                      <el-progress :color=\"customColors\" :text-inside=\"true\" :stroke-width=\"24\" :percentage=\"info.cpu.percent\" />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\" class=\"monitor-progress\">\r\n                    <el-col :sm=\"24\" :md=\"4\">\r\n                      RAM\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"20\">\r\n                      <el-progress :color=\"customColors\" :text-inside=\"true\" :stroke-width=\"24\" :percentage=\"info.mem.percent\" />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\" class=\"monitor-progress\">\r\n                    <el-col :sm=\"24\" :md=\"4\">\r\n                      硬盘\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"20\">\r\n                      <el-progress :color=\"customColors\" :text-inside=\"true\" :stroke-width=\"24\" :percentage=\"info.disk.percent\" />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <!-- <el-progress :color=\"$store.state.settings.theme\" type=\"circle\" :percentage=\"info.cpu.Percent\" /> -->\r\n                </div>\r\n                <!-- <div class=\"monitor-footer\">\r\n\r\n                  <Cell label=\"CPU主频\" :value=\"info.cpu.cpuInfo[0].modelName.split('@ ')[1]\" border />\r\n                  <Cell label=\"核心数\" :value=\"`${info.cpu.cpuInfo[0].cores}`\" />\r\n                </div> -->\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- <el-card v-if=\"info.os\" class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>go运行环境</span>\r\n            </div>\r\n            <div class=\"monitor\">\r\n              <Cell label=\"GO 版本\" :value=\"info.os.version\" border />\r\n              <Cell label=\"Goroutine\" :value=\"`${info.os.numGoroutine}`\" border />\r\n              <Cell label=\"项目地址\" :value=\"info.os.projectDir\" />\r\n            </div>\r\n          </el-card> -->\r\n\r\n          <el-card v-if=\"info.os\" class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>服务器信息</span>\r\n            </div>\r\n            <div class=\"monitor\">\r\n              <Cell label=\"主机名称\" :value=\"info.os.hostName\" border />\r\n              <Cell label=\"操作系统\" :value=\"info.os.goOs\" border />\r\n              <Cell label=\"服务器IP\" :value=\"info.os.ip\" border />\r\n              <Cell label=\"系统架构\" :value=\"info.os.arch\" border />\r\n              <Cell label=\"CPU\" :value=\"info.cpu.cpuInfo[0].modelName\" border />\r\n              <Cell label=\"当前时间\" :value=\"info.os.time\" />\r\n            </div>\r\n          </el-card>\r\n\r\n        </el-row></template>\r\n    </BasicLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Cell from '@/components/Cell/index'\r\nimport {\r\n  getServer\r\n} from '@/api/monitor/server'\r\nexport default {\r\n  name: 'Monitor',\r\n  components: {\r\n    Cell\r\n  },\r\n  data() {\r\n    return {\r\n      info: {},\r\n      customColors: [\r\n        { color: '#13ce66', percentage: 20 },\r\n        { color: '#1890ff', percentage: 40 },\r\n        { color: '#e6a23c', percentage: 60 },\r\n        { color: '#1989fa', percentage: 80 },\r\n        { color: '#F56C6C', percentage: 100 }\r\n      ],\r\n      timer: null\r\n    }\r\n  },\r\n  created() {\r\n    this.getServerInfo()\r\n    this.timer = setInterval(() => {\r\n      this.getServerInfo()\r\n    }, 1000)\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer)\r\n    this.timer = null\r\n  },\r\n  methods: {\r\n    getServerInfo() {\r\n      getServer().then(ret => {\r\n        if (ret.code === 200) {\r\n          this.info = ret\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.line{\r\n  line-height: 49px;\r\n  font-size: 14px ;\r\n  padding-left: 5px !important;\r\n  padding-right: 5px !important;\r\n  border-bottom: 1px solid #e6ebf5;\r\n  .line-value{\r\n    text-align: right;\r\n    color: #969799;\r\n  }\r\n}\r\n\r\n.monitor {\r\n  .monitor-header {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n  .monitor-progress{\r\n    padding-top: 15px;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}