{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\auth-redirect.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\auth-redirect.vue", "mtime": 1753924830416}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zZWFyY2guanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0F1dGhSZWRpcmVjdCcsCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBoYXNoID0gd2luZG93LmxvY2F0aW9uLnNlYXJjaC5zbGljZSgxKTsKICAgIGlmICh3aW5kb3cubG9jYWxTdG9yYWdlKSB7CiAgICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2Uuc2V0SXRlbSgneC1hZG1pbi1vYXV0aC1jb2RlJywgaGFzaCk7CiAgICAgIHdpbmRvdy5jbG9zZSgpOwogICAgfQogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgcmV0dXJuIGgoKTsgLy8gYXZvaWQgd2FybmluZyBtZXNzYWdlCiAgfQp9Ow=="}, {"version": 3, "names": ["name", "created", "hash", "window", "location", "search", "slice", "localStorage", "setItem", "close", "render", "h"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\auth-redirect.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'AuthRedirect',\r\n  created() {\r\n    const hash = window.location.search.slice(1)\r\n    if (window.localStorage) {\r\n      window.localStorage.setItem('x-admin-oauth-code', hash)\r\n      window.close()\r\n    }\r\n  },\r\n  render: function(h) {\r\n    return h() // avoid warning message\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;AACA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAMC,IAAG,GAAIC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC;IAC3C,IAAIH,MAAM,CAACI,YAAY,EAAE;MACvBJ,MAAM,CAACI,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAEN,IAAI;MACtDC,MAAM,CAACM,KAAK,CAAC;IACf;EACF,CAAC;EACDC,MAAM,EAAE,SAARA,MAAMA,CAAWC,CAAC,EAAE;IAClB,OAAOA,CAAC,CAAC,GAAE;EACb;AACF", "ignoreList": []}]}