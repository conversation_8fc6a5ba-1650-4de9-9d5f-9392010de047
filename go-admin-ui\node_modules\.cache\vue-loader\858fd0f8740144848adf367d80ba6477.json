{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue?vue&type=template&id=37dfd6fc&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue", "mtime": 1753924830436}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC;YACP;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD;kBACF,CAAC;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACvD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B;YACF;cACE,CAAC,CAAC,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpB;gBACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB;YACF,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzF,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACd,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzC;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5C,CAAC;oBACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/login/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div id=\"particles-js\">\r\n      <!-- <vue-particles\r\n        v-if=\"refreshParticles\"\r\n        color=\"#dedede\"\r\n        :particle-opacity=\"0.7\"\r\n        :particles-number=\"80\"\r\n        shape-type=\"circle\"\r\n        :particle-size=\"4\"\r\n        lines-color=\"#dedede\"\r\n        :lines-width=\"1\"\r\n        :line-linked=\"true\"\r\n        :line-opacity=\"0.4\"\r\n        :lines-distance=\"150\"\r\n        :move-speed=\"3\"\r\n        :hover-effect=\"true\"\r\n        hover-mode=\"grab\"\r\n        :click-effect=\"true\"\r\n        click-mode=\"push\"\r\n      /> -->\r\n    </div>\r\n\r\n    <div class=\"login-weaper animated bounceInDown\">\r\n      <div class=\"login-left\">\r\n        <div class=\"login-time\" v-text=\"currentTime\" />\r\n        <img :src=\"sysInfo.sys_app_logo\" alt=\"\" class=\"img\">\r\n        <p class=\"title\" v-text=\"sysInfo.sys_app_name\" />\r\n      </div>\r\n      <div class=\"login-border\">\r\n        <div class=\"login-main\">\r\n          <div class=\"login-title\">用户登录</div>\r\n          <el-form\r\n            ref=\"loginForm\"\r\n            :model=\"loginForm\"\r\n            :rules=\"loginRules\"\r\n            class=\"login-form\"\r\n            autocomplete=\"on\"\r\n            label-position=\"left\"\r\n          >\r\n            <el-form-item prop=\"username\">\r\n              <span class=\"svg-container\">\r\n                <i class=\"el-icon-user\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.username\"\r\n                placeholder=\"用户名\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"1\"\r\n                autocomplete=\"on\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-tooltip\r\n              v-model=\"capsTooltip\"\r\n              content=\"Caps lock is On\"\r\n              placement=\"right\"\r\n              manual\r\n            >\r\n              <el-form-item prop=\"password\">\r\n                <span class=\"svg-container\">\r\n                  <svg-icon icon-class=\"password\" />\r\n                </span>\r\n                <el-input\r\n                  :key=\"passwordType\"\r\n                  ref=\"password\"\r\n                  v-model=\"loginForm.password\"\r\n                  :type=\"passwordType\"\r\n                  placeholder=\"密码\"\r\n                  name=\"password\"\r\n                  tabindex=\"2\"\r\n                  autocomplete=\"on\"\r\n                  @keyup.native=\"checkCapslock\"\r\n                  @blur=\"capsTooltip = false\"\r\n                  @keyup.enter.native=\"handleLogin\"\r\n                />\r\n                <span class=\"show-pwd\" @click=\"showPwd\">\r\n                  <svg-icon\r\n                    :icon-class=\"\r\n                      passwordType === 'password' ? 'eye' : 'eye-open'\r\n                    \"\r\n                  />\r\n                </span>\r\n              </el-form-item>\r\n            </el-tooltip>\r\n            <el-form-item prop=\"code\" style=\"width: 66%; float: left\">\r\n              <span class=\"svg-container\">\r\n                <svg-icon icon-class=\"validCode\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.code\"\r\n                placeholder=\"验证码\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"3\"\r\n                maxlength=\"5\"\r\n                autocomplete=\"off\"\r\n                style=\"width: 75%\"\r\n                @keyup.enter.native=\"handleLogin\"\r\n              />\r\n            </el-form-item>\r\n            <div\r\n              class=\"login-code\"\r\n              style=\"\r\n                cursor: pointer;\r\n                width: 30%;\r\n                height: 48px;\r\n                float: right;\r\n                background-color: #f0f1f5;\r\n              \"\r\n            >\r\n              <img\r\n                style=\"\r\n                  height: 48px;\r\n                  width: 100%;\r\n                  border: 1px solid rgba(0, 0, 0, 0.1);\r\n                  border-radius: 5px;\r\n                \"\r\n                :src=\"codeUrl\"\r\n                @click=\"getCode\"\r\n              >\r\n            </div>\r\n\r\n            <el-button\r\n              :loading=\"loading\"\r\n              type=\"primary\"\r\n              style=\"width: 100%; padding: 12px 20px; margin-bottom: 30px\"\r\n              @click.native.prevent=\"handleLogin\"\r\n            >\r\n              <span v-if=\"!loading\">登 录</span>\r\n              <span v-else>登 录 中...</span>\r\n            </el-button>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\" :close-on-click-modal=\"false\">\r\n      Can not be simulated on local, so please combine you own business\r\n      simulation! ! !\r\n      <br>\r\n      <br>\r\n      <br>\r\n      <social-sign />\r\n    </el-dialog>\r\n    <div\r\n      id=\"bottom_layer\"\r\n      class=\"s-bottom-layer s-isindex-wrap\"\r\n      style=\"visibility: visible; width: 100%\"\r\n    >\r\n      <div class=\"s-bottom-layer-content\">\r\n\r\n        <div class=\"lh\">\r\n          <a class=\"text-color\" href=\"https://beian.miit.gov.cn\" target=\"_blank\">\r\n            沪ICP备XXXXXXXXX号-1\r\n          </a>\r\n        </div>\r\n        <div class=\"open-content-info\">\r\n          <div class=\"tip-hover-panel\" style=\"top: -18px; right: -12px\">\r\n            <div class=\"rest_info_tip\">\r\n              <div class=\"tip-wrapper\">\r\n                <div class=\"lh tip-item\" style=\"display: none\">\r\n                  <a\r\n                    class=\"text-color\"\r\n                    href=\"https://beian.miit.gov.cn\"\r\n                    target=\"_blank\"\r\n                  >\r\n                    沪ICP备XXXXXXXXX号-1\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from '@/api/login'\r\nimport moment from 'moment'\r\nimport SocialSign from './components/SocialSignin'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: { SocialSign },\r\n  data() {\r\n    return {\r\n      codeUrl: '',\r\n      cookiePassword: '',\r\n      refreshParticles: true,\r\n      loginForm: {\r\n        username: 'admin',\r\n        password: '123456',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: 'blur', message: '用户名不能为空' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: 'blur', message: '密码不能为空' }\r\n        ],\r\n        code: [\r\n          { required: true, trigger: 'change', message: '验证码不能为空' }\r\n        ]\r\n      },\r\n      passwordType: 'password',\r\n      capsTooltip: false,\r\n      loading: false,\r\n      showDialog: false,\r\n      redirect: undefined,\r\n      otherQuery: {},\r\n      currentTime: null,\r\n      sysInfo: ''\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        const query = route.query\r\n        if (query) {\r\n          this.redirect = query.redirect\r\n          this.otherQuery = this.getOtherQuery(query)\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    // window.addEventListener('storage', this.afterQRScan)\r\n    this.getCurrentTime()\r\n    this.getSystemSetting()\r\n  },\r\n  mounted() {\r\n    if (this.loginForm.username === '') {\r\n      this.$refs.username.focus()\r\n    } else if (this.loginForm.password === '') {\r\n      this.$refs.password.focus()\r\n    }\r\n    window.addEventListener('resize', () => {\r\n      this.refreshParticles = false\r\n      this.$nextTick(() => (this.refreshParticles = true))\r\n    })\r\n  },\r\n  destroyed() {\r\n    clearInterval(this.timer)\r\n    window.removeEventListener('resize', () => {})\r\n    // window.removeEventListener('storage', this.afterQRScan)\r\n  },\r\n  methods: {\r\n    getSystemSetting() {\r\n      this.$store.dispatch('system/settingDetail').then((ret) => {\r\n        this.sysInfo = ret\r\n        document.title = ret.sys_app_name\r\n      })\r\n    },\r\n    getCurrentTime() {\r\n      this.timer = setInterval((_) => {\r\n        this.currentTime = moment().format('YYYY-MM-DD HH时mm分ss秒')\r\n      }, 1000)\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        if (res !== undefined) {\r\n          this.codeUrl = res.data\r\n          this.loginForm.uuid = res.id\r\n        }\r\n      })\r\n    },\r\n    checkCapslock({ shiftKey, key } = {}) {\r\n      if (key && key.length === 1) {\r\n        if (\r\n          (shiftKey && key >= 'a' && key <= 'z') ||\r\n          (!shiftKey && key >= 'A' && key <= 'Z')\r\n        ) {\r\n          this.capsTooltip = true\r\n        } else {\r\n          this.capsTooltip = false\r\n        }\r\n      }\r\n      if (key === 'CapsLock' && this.capsTooltip === true) {\r\n        this.capsTooltip = false\r\n      }\r\n    },\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n          this.$store\r\n            .dispatch('user/login', this.loginForm)\r\n            .then(() => {\r\n              this.$router\r\n                .push({ path: this.redirect || '/', query: this.otherQuery })\r\n                .catch(() => {})\r\n            })\r\n            .catch(() => {\r\n              this.loading = false\r\n              this.getCode()\r\n            })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    getOtherQuery(query) {\r\n      return Object.keys(query).reduce((acc, cur) => {\r\n        if (cur !== 'redirect') {\r\n          acc[cur] = query[cur]\r\n        }\r\n        return acc\r\n      }, {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 修复input 背景不协调 和光标变色 */\r\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\r\n\r\n$bg: #283443;\r\n$light_gray: #fff;\r\n$cursor: #fff;\r\n\r\n#bottom_layer {\r\n  visibility: hidden;\r\n  width: 3000px;\r\n  position: fixed;\r\n  z-index: 302;\r\n  bottom: 0;\r\n  left: 0;\r\n  height: 39px;\r\n  padding-top: 1px;\r\n  zoom: 1;\r\n  margin: 0;\r\n  line-height: 39px;\r\n  // background: #0e6cff;\r\n}\r\n#bottom_layer .lh {\r\n  display: inline-block;\r\n  margin-right: 14px;\r\n}\r\n#bottom_layer .lh .emphasize {\r\n  text-decoration: underline;\r\n  font-weight: 700;\r\n}\r\n#bottom_layer .lh:last-child {\r\n  margin-left: -2px;\r\n  margin-right: 0;\r\n}\r\n#bottom_layer .lh.activity {\r\n  font-weight: 700;\r\n  text-decoration: underline;\r\n}\r\n#bottom_layer a {\r\n  font-size: 12px;\r\n  text-decoration: none;\r\n}\r\n#bottom_layer .text-color {\r\n  color: #bbb;\r\n}\r\n#bottom_layer .aria-img {\r\n  width: 49px;\r\n  height: 20px;\r\n  margin-bottom: -5px;\r\n}\r\n#bottom_layer a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .s-bottom-layer-content {\r\n  margin: 0 17px;\r\n  text-align: center;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line {\r\n  display: inline;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line:first-child {\r\n  margin-right: 14px;\r\n}\r\n.s-bottom-space {\r\n  position: static;\r\n  width: 100%;\r\n  height: 40px;\r\n  margin: 23px auto 12px;\r\n}\r\n#bottom_layer .open-content-info a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .open-content-info .text-color {\r\n  color: #626675;\r\n}\r\n.open-content-info {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 20px;\r\n}\r\n.open-content-info > span {\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n.open-content-info > span:hover {\r\n  color: #fff;\r\n}\r\n.open-content-info .tip-hover-panel {\r\n  position: absolute;\r\n  display: none;\r\n  padding-bottom: 18px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip {\r\n  max-width: 560px;\r\n  padding: 8px 12px 8px 12px;\r\n  background: #fff;\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);\r\n  text-align: left;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper {\r\n  white-space: nowrap;\r\n  line-height: 20px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper .tip-item {\r\n  height: 20px;\r\n  line-height: 20px;\r\n}\r\n.open-content-info\r\n  .tip-hover-panel\r\n  .rest_info_tip\r\n  .tip-wrapper\r\n  .tip-item:last-child {\r\n  margin-right: 0;\r\n}\r\n@media screen and (max-width: 515px) {\r\n  .open-content-info {\r\n    width: 16px;\r\n  }\r\n  .open-content-info .tip-hover-panel {\r\n    right: -16px !important;\r\n  }\r\n}\r\n.footer {\r\n  background-color: #0e6cff;\r\n  margin-bottom: -20px;\r\n}\r\n\r\n.login-container {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  margin: 0 auto;\r\n  background: url(\"../../assets/login.png\") no-repeat;\r\n  background-color: #0e6cff;\r\n  position: relative;\r\n  background-size: cover;\r\n  height: 100vh;\r\n  background-position: 50%;\r\n}\r\n\r\n#particles-js {\r\n  z-index: 1;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n}\r\n\r\n.login-weaper {\r\n  margin: 0 auto;\r\n  width: 1000px;\r\n  -webkit-box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  z-index: 1000;\r\n}\r\n\r\n.login-left {\r\n  border-top-left-radius: 5px;\r\n  border-bottom-left-radius: 5px;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-box-direction: normal;\r\n  -ms-flex-direction: column;\r\n  flex-direction: column;\r\n  background-color: rgba(64, 158, 255, 0);\r\n  color: #fff;\r\n  float: left;\r\n  width: 50%;\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  .login-time {\r\n    position: absolute;\r\n    left: 25px;\r\n    top: 25px;\r\n    width: 100%;\r\n    color: #fff;\r\n    opacity: 0.9;\r\n    font-size: 18px;\r\n    overflow: hidden;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.login-left .img {\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 3px;\r\n}\r\n\r\n.login-left .title {\r\n  text-align: center;\r\n  color: #fff;\r\n  letter-spacing: 2px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.login-border {\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  border-left: none;\r\n  border-top-right-radius: 5px;\r\n  border-bottom-right-radius: 5px;\r\n  color: #fff;\r\n  background-color: hsla(0, 0%, 100%, 0.9);\r\n  width: 50%;\r\n  float: left;\r\n}\r\n\r\n.login-main {\r\n  margin: 0 auto;\r\n  width: 65%;\r\n}\r\n\r\n.login-title {\r\n  color: #333;\r\n  margin-bottom: 40px;\r\n  font-weight: 500;\r\n  font-size: 22px;\r\n  text-align: center;\r\n  letter-spacing: 4px;\r\n}\r\n\r\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\r\n  .login-container .el-input input {\r\n    color: $cursor;\r\n  }\r\n}\r\n\r\n/* reset element-ui css */\r\n.login-container {\r\n  ::v-deep .el-input {\r\n    display: inline-block;\r\n    height: 47px;\r\n    width: 85%;\r\n\r\n    input {\r\n      background: transparent;\r\n      border: 0px;\r\n      -webkit-appearance: none;\r\n      border-radius: 0px;\r\n      padding: 12px 5px 12px 15px;\r\n      color: #333;\r\n      height: 47px;\r\n      caret-color: #333;\r\n\r\n      &:-webkit-autofill {\r\n        box-shadow: 0 0 0px 1000px $bg inset !important;\r\n        -webkit-text-fill-color: $cursor !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-form-item {\r\n    border: 1px solid rgba(0, 0, 0, 0.1);\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border-radius: 5px;\r\n    color: #454545;\r\n  }\r\n}\r\n$bg: #2d3a4b;\r\n$dark_gray: #889aa4;\r\n$light_gray: #eee;\r\n\r\n.login-container {\r\n  .tips {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    margin-bottom: 10px;\r\n\r\n    span {\r\n      &:first-of-type {\r\n        margin-right: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .svg-container {\r\n    padding: 6px 5px 6px 15px;\r\n    color: $dark_gray;\r\n    vertical-align: middle;\r\n    width: 30px;\r\n    display: inline-block;\r\n  }\r\n\r\n  .title-container {\r\n    position: relative;\r\n\r\n    .title {\r\n      font-size: 26px;\r\n      color: $light_gray;\r\n      margin: 0px auto 40px auto;\r\n      text-align: center;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .show-pwd {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 7px;\r\n    font-size: 16px;\r\n    color: $dark_gray;\r\n    cursor: pointer;\r\n    user-select: none;\r\n  }\r\n\r\n  .thirdparty-button {\r\n    position: absolute;\r\n    right: 0;\r\n    bottom: 6px;\r\n  }\r\n\r\n  @media only screen and (max-width: 470px) {\r\n    .thirdparty-button {\r\n      display: none;\r\n    }\r\n    .login-weaper {\r\n      width: 100%;\r\n      padding: 0 30px;\r\n      box-sizing: border-box;\r\n      box-shadow: none;\r\n    }\r\n    .login-main {\r\n      width: 80%;\r\n    }\r\n    .login-left {\r\n      display: none !important;\r\n    }\r\n    .login-border {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}