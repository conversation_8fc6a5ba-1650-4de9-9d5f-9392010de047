{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\system.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\system.js", "mtime": 1753924830229}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCB7IGdldFNldHRpbmcgfSBmcm9tICdAL2FwaS9sb2dpbic7CmltcG9ydCBzdG9yYWdlIGZyb20gJ0AvdXRpbHMvc3RvcmFnZSc7CnZhciBzdGF0ZSA9IHsKICBpbmZvOiBzdG9yYWdlLmdldCgnYXBwX2luZm8nKQp9Owp2YXIgbXV0YXRpb25zID0gewogIFNFVF9JTkZPOiBmdW5jdGlvbiBTRVRfSU5GTyhzdGF0ZSwgZGF0YSkgewogICAgc3RhdGUuaW5mbyA9IGRhdGE7CiAgICBzdG9yYWdlLnNldCgnYXBwX2luZm8nLCBkYXRhKTsKICB9Cn07CnZhciBhY3Rpb25zID0gewogIHNldHRpbmdEZXRhaWw6IGZ1bmN0aW9uIHNldHRpbmdEZXRhaWwoX3JlZikgewogICAgdmFyIGNvbW1pdCA9IF9yZWYuY29tbWl0OwogICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgZ2V0U2V0dGluZygpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgdmFyIGRhdGEgPSByZXNwb25zZS5kYXRhOwogICAgICAgIGNvbW1pdCgnU0VUX0lORk8nLCBkYXRhKTsKICAgICAgICByZXNvbHZlKGRhdGEpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICByZWplY3QoZXJyb3IpOwogICAgICB9KTsKICAgIH0pOwogIH0KfTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWVzcGFjZWQ6IHRydWUsCiAgc3RhdGU6IHN0YXRlLAogIG11dGF0aW9uczogbXV0YXRpb25zLAogIGFjdGlvbnM6IGFjdGlvbnMKfTs="}, {"version": 3, "names": ["getSetting", "storage", "state", "info", "get", "mutations", "SET_INFO", "data", "set", "actions", "settingDetail", "_ref", "commit", "Promise", "resolve", "reject", "then", "response", "catch", "error", "namespaced"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/modules/system.js"], "sourcesContent": ["import { getSetting } from '@/api/login'\r\nimport storage from '@/utils/storage'\r\nconst state = {\r\n  info: storage.get('app_info')\r\n}\r\n\r\nconst mutations = {\r\n  SET_INFO: (state, data) => {\r\n    state.info = data\r\n    storage.set('app_info', data)\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  settingDetail({ commit }) {\r\n    return new Promise((resolve, reject) => {\r\n      getSetting().then(response => {\r\n        const { data } = response\r\n        commit('SET_INFO', data)\r\n        resolve(data)\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,IAAMC,KAAK,GAAG;EACZC,IAAI,EAAEF,OAAO,CAACG,GAAG,CAAC,UAAU;AAC9B,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,QAAQ,EAAE,SAAVA,QAAQA,CAAGJ,KAAK,EAAEK,IAAI,EAAK;IACzBL,KAAK,CAACC,IAAI,GAAGI,IAAI;IACjBN,OAAO,CAACO,GAAG,CAAC,UAAU,EAAED,IAAI,CAAC;EAC/B;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,IAAA,EAAa;IAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACpB,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCf,UAAU,CAAC,CAAC,CAACgB,IAAI,CAAC,UAAAC,QAAQ,EAAI;QAC5B,IAAQV,IAAI,GAAKU,QAAQ,CAAjBV,IAAI;QACZK,MAAM,CAAC,UAAU,EAAEL,IAAI,CAAC;QACxBO,OAAO,CAACP,IAAI,CAAC;MACf,CAAC,CAAC,CAACW,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBJ,MAAM,CAACI,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbC,UAAU,EAAE,IAAI;EAChBlB,KAAK,EAALA,KAAK;EACLG,SAAS,EAATA,SAAS;EACTI,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}