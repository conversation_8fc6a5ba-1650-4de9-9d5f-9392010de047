{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1753924830207}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ThemePicker", "components", "data", "activeColor", "$store", "state", "settings", "theme", "computed", "themeStyle", "fixedHeader", "get", "set", "val", "dispatch", "key", "value", "topNav", "commit", "permission", "defaultRoutes", "tagsView", "sidebarLogo", "methods", "themeChange", "handleTheme"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"drawer-container\">\r\n    <div>\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          页面设置\r\n        </div>\r\n        <div class=\"setting-drawer-block-checbox\">\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('light')\">\r\n            <img src=\"@/assets/light.svg\" alt=\"light\">\r\n            <div v-if=\"themeStyle === 'light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" />\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('dark')\">\r\n            <img src=\"@/assets/dark.svg\" alt=\"dark\">\r\n            <div v-if=\"themeStyle === 'dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" />\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-divider />\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          主题设置\r\n        </div>\r\n        <div class=\"drawer-item\">\r\n          <span>主题颜色</span>\r\n          <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\r\n        </div>\r\n      </div>\r\n      <el-divider />\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          布局设置\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启任务栏</span>\r\n          <el-switch v-model=\"tagsView\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>Header 固定</span>\r\n          <el-switch v-model=\"fixedHeader\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>侧边栏Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {\r\n      activeColor: this.$store.state.settings.theme\r\n    }\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    },\r\n    themeStyle() {\r\n      return this.$store.state.settings.themeStyle\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.commit('permission/SET_SIDEBAR_ROUTERS', this.$store.state.permission.defaultRoutes)\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.activeColor = val\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'themeStyle',\r\n        value: val\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer-container {\r\n  padding: 24px;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  word-wrap: break-word;\r\n\r\n  .drawer-title {\r\n    margin-bottom: 12px;\r\n    color: rgba(0, 0, 0, .85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n  }\r\n\r\n  .drawer-item {\r\n    color: rgba(0, 0, 0, .65);\r\n    font-size: 14px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .drawer-switch {\r\n    float: right\r\n  }\r\n}\r\n.setting-drawer-content{\r\n  .setting-drawer-title{\r\n    margin-bottom: 12px;\r\n    color: rgba(0,0,0,.85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n    font-weight: bold;\r\n  }\r\n  .setting-drawer-block-checbox{\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n        img{\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n        .setting-drawer-block-checbox-selectIcon{\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            padding-top: 15px;\r\n            padding-left: 24px;\r\n            color: #1890ff;\r\n            font-weight: 700;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAqEA,OAAOA,WAAU,MAAO,0BAAyB;AAEjD,eAAe;EACbC,UAAU,EAAE;IAAED,WAAU,EAAVA;EAAY,CAAC;EAC3BE,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACC;IAC1C;EACF,CAAC;EACDC,QAAQ,EAAE;IACRD,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACH,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACC,KAAI;IACxC,CAAC;IACDE,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACL,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACG,UAAS;IAC7C,CAAC;IACDC,WAAW,EAAE;MACXC,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACP,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACI,WAAU;MAC9C,CAAC;MACDE,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,wBAAwB,EAAE;UAC7CC,GAAG,EAAE,aAAa;UAClBC,KAAK,EAAEH;QACT,CAAC;MACH;IACF,CAAC;IACDI,MAAM,EAAE;MACNN,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACP,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACW,MAAK;MACzC,CAAC;MACDL,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,wBAAwB,EAAE;UAC7CC,GAAG,EAAE,QAAQ;UACbC,KAAK,EAAEH;QACT,CAAC;QACD,IAAI,CAACA,GAAG,EAAE;UACR,IAAI,CAACT,MAAM,CAACc,MAAM,CAAC,gCAAgC,EAAE,IAAI,CAACd,MAAM,CAACC,KAAK,CAACc,UAAU,CAACC,aAAa;QACjG;MACF;IACF,CAAC;IACDC,QAAQ,EAAE;MACRV,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACP,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACe,QAAO;MAC3C,CAAC;MACDT,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,wBAAwB,EAAE;UAC7CC,GAAG,EAAE,UAAU;UACfC,KAAK,EAAEH;QACT,CAAC;MACH;IACF,CAAC;IACDS,WAAW,EAAE;MACXX,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACP,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACgB,WAAU;MAC9C,CAAC;MACDV,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,wBAAwB,EAAE;UAC7CC,GAAG,EAAE,aAAa;UAClBC,KAAK,EAAEH;QACT,CAAC;MACH;IACF;EACF,CAAC;EACDU,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAACX,GAAG,EAAE;MACf,IAAI,CAACV,WAAU,GAAIU,GAAE;MACrB,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,wBAAwB,EAAE;QAC7CC,GAAG,EAAE,OAAO;QACZC,KAAK,EAAEH;MACT,CAAC;IACH,CAAC;IACDY,WAAW,WAAXA,WAAWA,CAACZ,GAAG,EAAE;MACf,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,wBAAwB,EAAE;QAC7CC,GAAG,EAAE,YAAY;QACjBC,KAAK,EAAEH;MACT,CAAC;IACH;EACF;AACF", "ignoreList": []}]}