{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue?vue&type=style&index=0&id=3f2df4d6&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue", "mtime": 1753924830023}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2VsbC1pbm5lcnsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgd2lkdGg6IDEwMCU7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGNvbG9yOiAjMzIzMjMzOw0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAxMHB4IDA7DQoNCiAgLmNlbGwtaXRlbXsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB9DQogIC5jZWxsLWl0ZW0tbGFiZWx7DQogICAgc3BhbnsNCiAgICAgIGNvbG9yOiAjMzIzMjMzOw0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgfQ0KICB9DQogIC5jZWxsLWl0ZW0tdmFsdWV7DQogICAgY29sb3I6ICM5Njk3OTk7DQogIH0NCn0NCg0KLmJvcmRlcnsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAmOjphZnRlcnsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgICBjb250ZW50OiAnICc7DQogICAgcG9pbnRlci1ldmVudHM6IG5vbmU7DQogICAgcmlnaHQ6IDA7DQogICAgYm90dG9tOiAwOw0KICAgIGxlZnQ6IDA7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNmViZjU7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue"], "names": [], "mappings": ";AAoDA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;EAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/Cell/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"cell\">\r\n    <div class=\"cell-inner\" :class=\" border ? 'border' : '' \">\r\n      <div class=\"cell-item\">\r\n        <div class=\"cell-item-label\">\r\n          <span v-if=\"label\">\r\n            {{ label }}\r\n          </span>\r\n          <span v-else><slot name=\"label\" />\r\n          </span></div>\r\n        <div class=\"cell-item-value\">\r\n          <span v-if=\"value\">\r\n            {{ value }}\r\n          </span>\r\n          <span v-else>\r\n            <slot name=\"value\" />\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell-item mt5\">\r\n        <span v-if=\"extra\">{{ extra }}</span>\r\n        <span v-else><slot name=\"extra\" /></span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Cell',\r\n  props: {\r\n    border: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    label: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    extra: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cell-inner{\r\n  box-sizing: border-box;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  color: #323233;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  background-color: #fff;\r\n  padding: 10px 0;\r\n\r\n  .cell-item{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n  .cell-item-label{\r\n    span{\r\n      color: #323233;\r\n      font-size: 14px;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .cell-item-value{\r\n    color: #969799;\r\n  }\r\n}\r\n\r\n.border{\r\n  position: relative;\r\n  &::after{\r\n    position: absolute;\r\n    box-sizing: border-box;\r\n    content: ' ';\r\n    pointer-events: none;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    border-bottom: 1px solid #e6ebf5;\r\n  }\r\n}\r\n</style>\r\n"]}]}