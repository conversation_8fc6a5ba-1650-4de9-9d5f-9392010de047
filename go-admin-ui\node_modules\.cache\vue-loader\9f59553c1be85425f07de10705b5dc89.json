{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-user\\index.vue", "mtime": 1753924830281}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-user\\index.vue"], "names": [], "mappings": ";AA8TA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEhD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,EAAE,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvE,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;MACA;IACF,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;IACH,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxC,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;IACF,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC5C,CAAC;IACH,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACxB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;IACH,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,EAAE,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B;YACF,CAAC;UACH,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,EAAE,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B;YACF,CAAC;UACH;QACF;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACxB,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC;IACH,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/admin/sys-user/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-row :gutter=\"20\">\r\n          <!--部门数据-->\r\n          <el-col :span=\"4\" :xs=\"24\">\r\n            <div class=\"head-container\">\r\n              <el-input\r\n                v-model=\"deptName\"\r\n                placeholder=\"请输入部门名称\"\r\n                clearable\r\n                size=\"small\"\r\n                prefix-icon=\"el-icon-search\"\r\n                style=\"margin-bottom: 20px\"\r\n              />\r\n            </div>\r\n            <div class=\"head-container\">\r\n              <el-tree\r\n                ref=\"tree\"\r\n                :data=\"deptOptions\"\r\n                :props=\"defaultProps\"\r\n                :expand-on-click-node=\"false\"\r\n                :filter-node-method=\"filterNode\"\r\n                default-expand-all\r\n                @node-click=\"handleNodeClick\"\r\n              />\r\n            </div>\r\n          </el-col>\r\n          <!--用户数据-->\r\n          <el-col :span=\"20\" :xs=\"24\">\r\n            <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-width=\"68px\">\r\n              <el-form-item label=\"用户名称\" prop=\"username\">\r\n                <el-input\r\n                  v-model=\"queryParams.username\"\r\n                  placeholder=\"请输入用户名称\"\r\n                  clearable\r\n                  size=\"small\"\r\n                  style=\"width: 160px\"\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                <el-input\r\n                  v-model=\"queryParams.phone\"\r\n                  placeholder=\"请输入手机号码\"\r\n                  clearable\r\n                  size=\"small\"\r\n                  style=\"width: 160px\"\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"状态\" prop=\"status\">\r\n                <el-select\r\n                  v-model=\"queryParams.status\"\r\n                  placeholder=\"用户状态\"\r\n                  clearable\r\n                  size=\"small\"\r\n                  style=\"width: 160px\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in statusOptions\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n\r\n            <el-row :gutter=\"10\" class=\"mb8\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  v-permisaction=\"['admin:sysUser:add']\"\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-plus\"\r\n                  size=\"mini\"\r\n                  @click=\"handleAdd\"\r\n                >新增</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  v-permisaction=\"['admin:sysUser:edit']\"\r\n                  type=\"success\"\r\n                  icon=\"el-icon-edit\"\r\n                  size=\"mini\"\r\n                  :disabled=\"single\"\r\n                  @click=\"handleUpdate\"\r\n                >修改</el-button>\r\n              </el-col>\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  v-permisaction=\"['admin:sysUser:remove']\"\r\n                  type=\"danger\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  :disabled=\"multiple\"\r\n                  @click=\"handleDelete\"\r\n                >删除</el-button>\r\n              </el-col>\r\n            </el-row>\r\n\r\n            <el-table\r\n              v-loading=\"loading\"\r\n              :data=\"userList\"\r\n              border\r\n              @selection-change=\"handleSelectionChange\"\r\n              @sort-change=\"handleSortChang\"\r\n            >\r\n              <el-table-column type=\"selection\" width=\"45\" align=\"center\" />\r\n              <el-table-column label=\"编号\" width=\"75\" prop=\"userId\" sortable=\"custom\" />\r\n              <el-table-column label=\"登录名\" width=\"105\" prop=\"username\" sortable=\"custom\" :show-overflow-tooltip=\"true\" />\r\n              <el-table-column label=\"昵称\" prop=\"nickName\" :show-overflow-tooltip=\"true\" />\r\n              <el-table-column label=\"部门\" prop=\"dept.deptName\" :show-overflow-tooltip=\"true\" />\r\n              <el-table-column label=\"手机号\" prop=\"phone\" width=\"108\" />\r\n              <el-table-column label=\"状态\" width=\"80\" sortable=\"custom\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-switch\r\n                    v-model=\"scope.row.status\"\r\n                    active-value=\"2\"\r\n                    inactive-value=\"1\"\r\n                    @change=\"handleStatusChange(scope.row)\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"创建时间\"\r\n                prop=\"createdAt\"\r\n                sortable=\"custom\"\r\n                width=\"155\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"160\"\r\n\r\n                fix=\"right\"\r\n                class-name=\"small-padding fixed-width\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    v-permisaction=\"['admin:sysUser:edit']\"\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-edit\"\r\n                    @click=\"handleUpdate(scope.row)\"\r\n                  >修改</el-button>\r\n                  <el-button\r\n                    v-if=\"scope.row.userId !== 1\"\r\n                    v-permisaction=\"['admin:sysUser:remove']\"\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\"\r\n                  >删除</el-button>\r\n                  <el-button\r\n                    v-permisaction=\"['admin:sysUser:resetPassword']\"\r\n                    size=\"mini\"\r\n                    type=\"text\"\r\n                    icon=\"el-icon-key\"\r\n                    @click=\"handleResetPwd(scope.row)\"\r\n                  >重置</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <pagination\r\n              v-show=\"total>0\"\r\n              :total=\"total\"\r\n              :page.sync=\"queryParams.pageIndex\"\r\n              :limit.sync=\"queryParams.pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </el-card>\r\n      <!-- 添加或修改参数配置对话框 -->\r\n      <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" :close-on-click-modal=\"false\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n                <el-input v-model=\"form.nickName\" placeholder=\"请输入用户昵称\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"归属部门\" prop=\"deptId\">\r\n                <treeselect\r\n                  v-model=\"form.deptId\"\r\n                  :options=\"deptOptions\"\r\n                  placeholder=\"请选择归属部门\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"手机号码\" prop=\"phone\">\r\n                <el-input v-model=\"form.phone\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"邮箱\" prop=\"email\">\r\n                <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"用户名称\" prop=\"username\">\r\n                <el-input v-model=\"form.username\" placeholder=\"请输入用户名称\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item v-if=\"form.userId == undefined\" label=\"用户密码\" prop=\"password\">\r\n                <el-input v-model=\"form.password\" placeholder=\"请输入用户密码\" type=\"password\" />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"用户性别\">\r\n                <el-select v-model=\"form.sex\" placeholder=\"请选择\">\r\n                  <el-option\r\n                    v-for=\"dict in sexOptions\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"状态\">\r\n                <el-radio-group v-model=\"form.status\">\r\n                  <el-radio\r\n                    v-for=\"dict in statusOptions\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.value\"\r\n                  >{{ dict.label }}</el-radio>\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"岗位\">\r\n                <el-select v-model=\"form.postId\" placeholder=\"请选择\" @change=\"$forceUpdate()\">\r\n                  <el-option\r\n                    v-for=\"item in postOptions\"\r\n                    :key=\"item.postId\"\r\n                    :label=\"item.postName\"\r\n                    :value=\"item.postId\"\r\n                    :disabled=\"item.status == 1\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"角色\">\r\n                <el-select v-model=\"form.roleId\" placeholder=\"请选择\" @change=\"$forceUpdate()\">\r\n                  <el-option\r\n                    v-for=\"item in roleOptions\"\r\n                    :key=\"item.roleId\"\r\n                    :label=\"item.roleName\"\r\n                    :value=\"item.roleId\"\r\n                    :disabled=\"item.status == 1\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"备注\">\r\n                <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n      <!-- 用户导入对话框 -->\r\n      <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" :close-on-click-modal=\"false\">\r\n        <el-upload\r\n          ref=\"upload\"\r\n          :limit=\"1\"\r\n          accept=\".xlsx, .xls\"\r\n          :headers=\"upload.headers\"\r\n          :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\r\n          :disabled=\"upload.isUploading\"\r\n          :on-progress=\"handleFileUploadProgress\"\r\n          :on-success=\"handleFileSuccess\"\r\n          :auto-upload=\"false\"\r\n          drag\r\n        >\r\n          <i class=\"el-icon-upload\" />\r\n          <div class=\"el-upload__text\">\r\n            将文件拖到此处，或\r\n            <em>点击上传</em>\r\n          </div>\r\n          <div slot=\"tip\" class=\"el-upload__tip\">\r\n            <el-checkbox v-model=\"upload.updateSupport\" />是否更新已经存在的用户数据\r\n            <el-link type=\"info\" style=\"font-size:12px\" @click=\"importTemplate\">下载模板</el-link>\r\n          </div>\r\n          <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:red\">提示：仅允许导入“xls”或“xlsx”格式文件！</div>\r\n        </el-upload>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n          <el-button @click=\"upload.open = false\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listUser, getUser, delUser, addUser, updateUser, exportUser, resetUserPwd, changeUserStatus, importTemplate } from '@/api/admin/sys-user'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nimport { listPost } from '@/api/admin/sys-post'\r\nimport { listRole } from '@/api/admin/sys-role'\r\nimport { treeselect } from '@/api/admin/sys-dept'\r\n\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\r\n\r\nexport default {\r\n  name: 'SysUserManage',\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: null,\r\n      // 弹出层标题\r\n      title: '',\r\n      // 部门树选项\r\n      deptOptions: undefined,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 部门名称\r\n      deptName: undefined,\r\n      // 默认密码\r\n      initPassword: undefined,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 性别状态字典\r\n      sexOptions: [],\r\n      // 岗位选项\r\n      postOptions: [],\r\n      // 角色选项\r\n      roleOptions: [],\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      // 用户导入参数\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: '',\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: 0,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: 'Bearer ' + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + '/system/user/importData'\r\n      },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        username: undefined,\r\n        phone: undefined,\r\n        status: undefined,\r\n        deptId: undefined\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        username: [{ required: true, message: '用户名称不能为空', trigger: 'blur' }],\r\n        nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],\r\n        deptId: [{ required: true, message: '归属部门不能为空', trigger: 'blur' }],\r\n        password: [{ required: true, message: '用户密码不能为空', trigger: 'blur' }],\r\n        email: [\r\n          { required: true, message: '邮箱地址不能为空', trigger: 'blur' },\r\n          { type: 'email', message: \"'请输入正确的邮箱地址\", trigger: ['blur', 'change'] }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '手机号码不能为空', trigger: 'blur' },\r\n          { pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    // 根据名称筛选部门树\r\n    deptName(val) {\r\n      this.$refs.tree.filter(val)\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getTreeselect()\r\n    this.getDicts('sys_normal_disable').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n    this.getDicts('sys_user_sex').then(response => {\r\n      this.sexOptions = response.data\r\n    })\r\n    this.getConfigKey('sys_user_initPassword').then(response => {\r\n      this.initPassword = response.data.configValue\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.userList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    /** 查询部门下拉树结构 */\r\n    getTreeselect() {\r\n      treeselect().then(response => {\r\n        this.deptOptions = response.data\r\n      })\r\n    },\r\n    // 筛选节点\r\n    filterNode(value, data) {\r\n      if (!value) return true\r\n      return data.label.indexOf(value) !== -1\r\n    },\r\n    // 节点单击事件\r\n    handleNodeClick(data) {\r\n      this.queryParams.deptId = '/' + data.id + '/'\r\n      this.getList()\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      return {\r\n        id: node.id,\r\n        label: node.label,\r\n        children: node.children\r\n      }\r\n    },\r\n    /** 排序回调函数 */\r\n    handleSortChang(column, prop, order) {\r\n      prop = column.prop\r\n      order = column.order\r\n      if (this.order !== '' && this.order !== prop + 'Order') {\r\n        this.queryParams[this.order] = undefined\r\n      }\r\n      if (order === 'descending') {\r\n        this.queryParams[prop + 'Order'] = 'desc'\r\n        this.order = prop + 'Order'\r\n      } else if (order === 'ascending') {\r\n        this.queryParams[prop + 'Order'] = 'asc'\r\n        this.order = prop + 'Order'\r\n      } else {\r\n        this.queryParams[prop + 'Order'] = undefined\r\n      }\r\n      this.getList()\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      const text = row.status === '2' ? '启用' : '停用'\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.username + '\"用户吗?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return changeUserStatus(row)\r\n      }).then(() => {\r\n        this.msgSuccess(text + '成功')\r\n      }).catch(function() {\r\n        row.status = row.status === '2' ? '1' : '2'\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: undefined,\r\n        deptId: undefined,\r\n        username: undefined,\r\n        nickName: undefined,\r\n        password: undefined,\r\n        phone: undefined,\r\n        email: undefined,\r\n        sex: undefined,\r\n        status: '2',\r\n        remark: undefined,\r\n        postIds: undefined,\r\n        roleIds: undefined\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.page = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.queryParams.deptId = ''\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.getTreeselect()\r\n\r\n      listPost({ pageSize: 1000 }).then(response => {\r\n        this.postOptions = response.data.list\r\n      })\r\n      listRole({ pageSize: 1000 }).then(response => {\r\n        this.roleOptions = response.data.list\r\n      })\r\n      this.open = true\r\n      this.title = '添加用户'\r\n      this.form.password = this.initPassword\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n\r\n      const userId = row.userId || this.ids\r\n      getUser(userId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改用户'\r\n        this.form.password = ''\r\n      })\r\n      listPost({ pageSize: 1000 }).then(response => {\r\n        this.postOptions = response.data.list\r\n      })\r\n      listRole({ pageSize: 1000 }).then(response => {\r\n        this.roleOptions = response.data.list\r\n      })\r\n    },\r\n    /** 重置密码按钮操作 */\r\n    handleResetPwd(row) {\r\n      this.$prompt('请输入\"' + row.username + '\"的新密码', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消'\r\n      }).then(({ value }) => {\r\n        resetUserPwd(row.userId, value).then(response => {\r\n          if (response.code === 200) {\r\n            this.msgSuccess(response.msg)\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n        })\r\n      }).catch(() => {})\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId !== undefined) {\r\n            updateUser(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addUser(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const Ids = (row.userId && [row.userId]) || this.ids\r\n      this.$confirm('是否确认删除用户编号为\"' + Ids + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delUser({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams\r\n      this.$confirm('是否确认导出所有用户数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return exportUser(queryParams)\r\n      }).then(response => {\r\n        this.download(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = '用户导入'\r\n      this.upload.open = true\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      importTemplate().then(response => {\r\n        this.download(response.msg)\r\n      })\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false\r\n      this.upload.isUploading = false\r\n      this.$refs.upload.clearFiles()\r\n      this.$alert(response.msg, '导入结果', { dangerouslyUseHTMLString: true })\r\n      this.getList()\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit()\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}