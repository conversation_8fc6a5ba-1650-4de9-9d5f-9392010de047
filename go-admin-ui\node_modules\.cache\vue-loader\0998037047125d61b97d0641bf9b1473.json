{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue", "mtime": 1753924830444}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHRpbWVsaW5lOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aW1lc3RhbXA6ICcyMDE5LzQvMjAnLA0KICAgICAgICAgIHRpdGxlOiAnVXBkYXRlIEdpdGh1YiB0ZW1wbGF0ZScsDQogICAgICAgICAgY29udGVudDogJ1BhbkppYUNoZW4gY29tbWl0dGVkIDIwMTkvNC8yMCAyMDo0NicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpbWVzdGFtcDogJzIwMTkvNC8yMScsDQogICAgICAgICAgdGl0bGU6ICdVcGRhdGUgR2l0aHViIHRlbXBsYXRlJywNCiAgICAgICAgICBjb250ZW50OiAnUGFuSmlhQ2hlbiBjb21taXR0ZWQgMjAxOS80LzIxIDIwOjQ2Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGltZXN0YW1wOiAnMjAxOS80LzIyJywNCiAgICAgICAgICB0aXRsZTogJ0J1aWxkIFRlbXBsYXRlJywNCiAgICAgICAgICBjb250ZW50OiAnUGFuSmlhQ2hlbiBjb21taXR0ZWQgMjAxOS80LzIyIDIwOjQ2Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGltZXN0YW1wOiAnMjAxOS80LzIzJywNCiAgICAgICAgICB0aXRsZTogJ1JlbGVhc2UgTmV3IFZlcnNpb24nLA0KICAgICAgICAgIGNvbnRlbnQ6ICdQYW5KaWFDaGVuIGNvbW1pdHRlZCAyMDE5LzQvMjMgMjA6NDYnDQogICAgICAgIH0NCiAgICAgIF0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue"], "names": [], "mappings": ";AAcA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD;MACF;IACF;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/Timeline.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"block\">\r\n    <el-timeline>\r\n      <el-timeline-item v-for=\"(item,index) of timeline\" :key=\"index\" :timestamp=\"item.timestamp\" placement=\"top\">\r\n        <el-card>\r\n          <h4>{{ item.title }}</h4>\r\n          <p>{{ item.content }}</p>\r\n        </el-card>\r\n      </el-timeline-item>\r\n    </el-timeline>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      timeline: [\r\n        {\r\n          timestamp: '2019/4/20',\r\n          title: 'Update Github template',\r\n          content: 'PanJiaChen committed 2019/4/20 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/21',\r\n          title: 'Update Github template',\r\n          content: 'PanJiaChen committed 2019/4/21 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/22',\r\n          title: 'Build Template',\r\n          content: 'PanJiaChen committed 2019/4/22 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/23',\r\n          title: 'Release New Version',\r\n          content: 'PanJiaChen committed 2019/4/23 20:46'\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}