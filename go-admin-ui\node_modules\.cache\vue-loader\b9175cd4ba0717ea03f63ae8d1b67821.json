{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue", "mtime": 1753924830436}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue"], "names": [], "mappings": ";AAuLA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1D;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE;MACpC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3B,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,EAAE;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACxB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB;MACF;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACvB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACnB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;QACL,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,EAAE,CAAC,CAAC;IACP;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/login/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div id=\"particles-js\">\r\n      <!-- <vue-particles\r\n        v-if=\"refreshParticles\"\r\n        color=\"#dedede\"\r\n        :particle-opacity=\"0.7\"\r\n        :particles-number=\"80\"\r\n        shape-type=\"circle\"\r\n        :particle-size=\"4\"\r\n        lines-color=\"#dedede\"\r\n        :lines-width=\"1\"\r\n        :line-linked=\"true\"\r\n        :line-opacity=\"0.4\"\r\n        :lines-distance=\"150\"\r\n        :move-speed=\"3\"\r\n        :hover-effect=\"true\"\r\n        hover-mode=\"grab\"\r\n        :click-effect=\"true\"\r\n        click-mode=\"push\"\r\n      /> -->\r\n    </div>\r\n\r\n    <div class=\"login-weaper animated bounceInDown\">\r\n      <div class=\"login-left\">\r\n        <div class=\"login-time\" v-text=\"currentTime\" />\r\n        <img :src=\"sysInfo.sys_app_logo\" alt=\"\" class=\"img\">\r\n        <p class=\"title\" v-text=\"sysInfo.sys_app_name\" />\r\n      </div>\r\n      <div class=\"login-border\">\r\n        <div class=\"login-main\">\r\n          <div class=\"login-title\">用户登录</div>\r\n          <el-form\r\n            ref=\"loginForm\"\r\n            :model=\"loginForm\"\r\n            :rules=\"loginRules\"\r\n            class=\"login-form\"\r\n            autocomplete=\"on\"\r\n            label-position=\"left\"\r\n          >\r\n            <el-form-item prop=\"username\">\r\n              <span class=\"svg-container\">\r\n                <i class=\"el-icon-user\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.username\"\r\n                placeholder=\"用户名\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"1\"\r\n                autocomplete=\"on\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-tooltip\r\n              v-model=\"capsTooltip\"\r\n              content=\"Caps lock is On\"\r\n              placement=\"right\"\r\n              manual\r\n            >\r\n              <el-form-item prop=\"password\">\r\n                <span class=\"svg-container\">\r\n                  <svg-icon icon-class=\"password\" />\r\n                </span>\r\n                <el-input\r\n                  :key=\"passwordType\"\r\n                  ref=\"password\"\r\n                  v-model=\"loginForm.password\"\r\n                  :type=\"passwordType\"\r\n                  placeholder=\"密码\"\r\n                  name=\"password\"\r\n                  tabindex=\"2\"\r\n                  autocomplete=\"on\"\r\n                  @keyup.native=\"checkCapslock\"\r\n                  @blur=\"capsTooltip = false\"\r\n                  @keyup.enter.native=\"handleLogin\"\r\n                />\r\n                <span class=\"show-pwd\" @click=\"showPwd\">\r\n                  <svg-icon\r\n                    :icon-class=\"\r\n                      passwordType === 'password' ? 'eye' : 'eye-open'\r\n                    \"\r\n                  />\r\n                </span>\r\n              </el-form-item>\r\n            </el-tooltip>\r\n            <el-form-item prop=\"code\" style=\"width: 66%; float: left\">\r\n              <span class=\"svg-container\">\r\n                <svg-icon icon-class=\"validCode\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.code\"\r\n                placeholder=\"验证码\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"3\"\r\n                maxlength=\"5\"\r\n                autocomplete=\"off\"\r\n                style=\"width: 75%\"\r\n                @keyup.enter.native=\"handleLogin\"\r\n              />\r\n            </el-form-item>\r\n            <div\r\n              class=\"login-code\"\r\n              style=\"\r\n                cursor: pointer;\r\n                width: 30%;\r\n                height: 48px;\r\n                float: right;\r\n                background-color: #f0f1f5;\r\n              \"\r\n            >\r\n              <img\r\n                style=\"\r\n                  height: 48px;\r\n                  width: 100%;\r\n                  border: 1px solid rgba(0, 0, 0, 0.1);\r\n                  border-radius: 5px;\r\n                \"\r\n                :src=\"codeUrl\"\r\n                @click=\"getCode\"\r\n              >\r\n            </div>\r\n\r\n            <el-button\r\n              :loading=\"loading\"\r\n              type=\"primary\"\r\n              style=\"width: 100%; padding: 12px 20px; margin-bottom: 30px\"\r\n              @click.native.prevent=\"handleLogin\"\r\n            >\r\n              <span v-if=\"!loading\">登 录</span>\r\n              <span v-else>登 录 中...</span>\r\n            </el-button>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\" :close-on-click-modal=\"false\">\r\n      Can not be simulated on local, so please combine you own business\r\n      simulation! ! !\r\n      <br>\r\n      <br>\r\n      <br>\r\n      <social-sign />\r\n    </el-dialog>\r\n    <div\r\n      id=\"bottom_layer\"\r\n      class=\"s-bottom-layer s-isindex-wrap\"\r\n      style=\"visibility: visible; width: 100%\"\r\n    >\r\n      <div class=\"s-bottom-layer-content\">\r\n\r\n        <div class=\"lh\">\r\n          <a class=\"text-color\" href=\"https://beian.miit.gov.cn\" target=\"_blank\">\r\n            沪ICP备XXXXXXXXX号-1\r\n          </a>\r\n        </div>\r\n        <div class=\"open-content-info\">\r\n          <div class=\"tip-hover-panel\" style=\"top: -18px; right: -12px\">\r\n            <div class=\"rest_info_tip\">\r\n              <div class=\"tip-wrapper\">\r\n                <div class=\"lh tip-item\" style=\"display: none\">\r\n                  <a\r\n                    class=\"text-color\"\r\n                    href=\"https://beian.miit.gov.cn\"\r\n                    target=\"_blank\"\r\n                  >\r\n                    沪ICP备XXXXXXXXX号-1\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from '@/api/login'\r\nimport moment from 'moment'\r\nimport SocialSign from './components/SocialSignin'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: { SocialSign },\r\n  data() {\r\n    return {\r\n      codeUrl: '',\r\n      cookiePassword: '',\r\n      refreshParticles: true,\r\n      loginForm: {\r\n        username: 'admin',\r\n        password: '123456',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: 'blur', message: '用户名不能为空' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: 'blur', message: '密码不能为空' }\r\n        ],\r\n        code: [\r\n          { required: true, trigger: 'change', message: '验证码不能为空' }\r\n        ]\r\n      },\r\n      passwordType: 'password',\r\n      capsTooltip: false,\r\n      loading: false,\r\n      showDialog: false,\r\n      redirect: undefined,\r\n      otherQuery: {},\r\n      currentTime: null,\r\n      sysInfo: ''\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        const query = route.query\r\n        if (query) {\r\n          this.redirect = query.redirect\r\n          this.otherQuery = this.getOtherQuery(query)\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    // window.addEventListener('storage', this.afterQRScan)\r\n    this.getCurrentTime()\r\n    this.getSystemSetting()\r\n  },\r\n  mounted() {\r\n    if (this.loginForm.username === '') {\r\n      this.$refs.username.focus()\r\n    } else if (this.loginForm.password === '') {\r\n      this.$refs.password.focus()\r\n    }\r\n    window.addEventListener('resize', () => {\r\n      this.refreshParticles = false\r\n      this.$nextTick(() => (this.refreshParticles = true))\r\n    })\r\n  },\r\n  destroyed() {\r\n    clearInterval(this.timer)\r\n    window.removeEventListener('resize', () => {})\r\n    // window.removeEventListener('storage', this.afterQRScan)\r\n  },\r\n  methods: {\r\n    getSystemSetting() {\r\n      this.$store.dispatch('system/settingDetail').then((ret) => {\r\n        this.sysInfo = ret\r\n        document.title = ret.sys_app_name\r\n      })\r\n    },\r\n    getCurrentTime() {\r\n      this.timer = setInterval((_) => {\r\n        this.currentTime = moment().format('YYYY-MM-DD HH时mm分ss秒')\r\n      }, 1000)\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        if (res !== undefined) {\r\n          this.codeUrl = res.data\r\n          this.loginForm.uuid = res.id\r\n        }\r\n      })\r\n    },\r\n    checkCapslock({ shiftKey, key } = {}) {\r\n      if (key && key.length === 1) {\r\n        if (\r\n          (shiftKey && key >= 'a' && key <= 'z') ||\r\n          (!shiftKey && key >= 'A' && key <= 'Z')\r\n        ) {\r\n          this.capsTooltip = true\r\n        } else {\r\n          this.capsTooltip = false\r\n        }\r\n      }\r\n      if (key === 'CapsLock' && this.capsTooltip === true) {\r\n        this.capsTooltip = false\r\n      }\r\n    },\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n          this.$store\r\n            .dispatch('user/login', this.loginForm)\r\n            .then(() => {\r\n              this.$router\r\n                .push({ path: this.redirect || '/', query: this.otherQuery })\r\n                .catch(() => {})\r\n            })\r\n            .catch(() => {\r\n              this.loading = false\r\n              this.getCode()\r\n            })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    getOtherQuery(query) {\r\n      return Object.keys(query).reduce((acc, cur) => {\r\n        if (cur !== 'redirect') {\r\n          acc[cur] = query[cur]\r\n        }\r\n        return acc\r\n      }, {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 修复input 背景不协调 和光标变色 */\r\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\r\n\r\n$bg: #283443;\r\n$light_gray: #fff;\r\n$cursor: #fff;\r\n\r\n#bottom_layer {\r\n  visibility: hidden;\r\n  width: 3000px;\r\n  position: fixed;\r\n  z-index: 302;\r\n  bottom: 0;\r\n  left: 0;\r\n  height: 39px;\r\n  padding-top: 1px;\r\n  zoom: 1;\r\n  margin: 0;\r\n  line-height: 39px;\r\n  // background: #0e6cff;\r\n}\r\n#bottom_layer .lh {\r\n  display: inline-block;\r\n  margin-right: 14px;\r\n}\r\n#bottom_layer .lh .emphasize {\r\n  text-decoration: underline;\r\n  font-weight: 700;\r\n}\r\n#bottom_layer .lh:last-child {\r\n  margin-left: -2px;\r\n  margin-right: 0;\r\n}\r\n#bottom_layer .lh.activity {\r\n  font-weight: 700;\r\n  text-decoration: underline;\r\n}\r\n#bottom_layer a {\r\n  font-size: 12px;\r\n  text-decoration: none;\r\n}\r\n#bottom_layer .text-color {\r\n  color: #bbb;\r\n}\r\n#bottom_layer .aria-img {\r\n  width: 49px;\r\n  height: 20px;\r\n  margin-bottom: -5px;\r\n}\r\n#bottom_layer a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .s-bottom-layer-content {\r\n  margin: 0 17px;\r\n  text-align: center;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line {\r\n  display: inline;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line:first-child {\r\n  margin-right: 14px;\r\n}\r\n.s-bottom-space {\r\n  position: static;\r\n  width: 100%;\r\n  height: 40px;\r\n  margin: 23px auto 12px;\r\n}\r\n#bottom_layer .open-content-info a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .open-content-info .text-color {\r\n  color: #626675;\r\n}\r\n.open-content-info {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 20px;\r\n}\r\n.open-content-info > span {\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n.open-content-info > span:hover {\r\n  color: #fff;\r\n}\r\n.open-content-info .tip-hover-panel {\r\n  position: absolute;\r\n  display: none;\r\n  padding-bottom: 18px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip {\r\n  max-width: 560px;\r\n  padding: 8px 12px 8px 12px;\r\n  background: #fff;\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);\r\n  text-align: left;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper {\r\n  white-space: nowrap;\r\n  line-height: 20px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper .tip-item {\r\n  height: 20px;\r\n  line-height: 20px;\r\n}\r\n.open-content-info\r\n  .tip-hover-panel\r\n  .rest_info_tip\r\n  .tip-wrapper\r\n  .tip-item:last-child {\r\n  margin-right: 0;\r\n}\r\n@media screen and (max-width: 515px) {\r\n  .open-content-info {\r\n    width: 16px;\r\n  }\r\n  .open-content-info .tip-hover-panel {\r\n    right: -16px !important;\r\n  }\r\n}\r\n.footer {\r\n  background-color: #0e6cff;\r\n  margin-bottom: -20px;\r\n}\r\n\r\n.login-container {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  margin: 0 auto;\r\n  background: url(\"../../assets/login.png\") no-repeat;\r\n  background-color: #0e6cff;\r\n  position: relative;\r\n  background-size: cover;\r\n  height: 100vh;\r\n  background-position: 50%;\r\n}\r\n\r\n#particles-js {\r\n  z-index: 1;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n}\r\n\r\n.login-weaper {\r\n  margin: 0 auto;\r\n  width: 1000px;\r\n  -webkit-box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  z-index: 1000;\r\n}\r\n\r\n.login-left {\r\n  border-top-left-radius: 5px;\r\n  border-bottom-left-radius: 5px;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-box-direction: normal;\r\n  -ms-flex-direction: column;\r\n  flex-direction: column;\r\n  background-color: rgba(64, 158, 255, 0);\r\n  color: #fff;\r\n  float: left;\r\n  width: 50%;\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  .login-time {\r\n    position: absolute;\r\n    left: 25px;\r\n    top: 25px;\r\n    width: 100%;\r\n    color: #fff;\r\n    opacity: 0.9;\r\n    font-size: 18px;\r\n    overflow: hidden;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.login-left .img {\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 3px;\r\n}\r\n\r\n.login-left .title {\r\n  text-align: center;\r\n  color: #fff;\r\n  letter-spacing: 2px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.login-border {\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  border-left: none;\r\n  border-top-right-radius: 5px;\r\n  border-bottom-right-radius: 5px;\r\n  color: #fff;\r\n  background-color: hsla(0, 0%, 100%, 0.9);\r\n  width: 50%;\r\n  float: left;\r\n}\r\n\r\n.login-main {\r\n  margin: 0 auto;\r\n  width: 65%;\r\n}\r\n\r\n.login-title {\r\n  color: #333;\r\n  margin-bottom: 40px;\r\n  font-weight: 500;\r\n  font-size: 22px;\r\n  text-align: center;\r\n  letter-spacing: 4px;\r\n}\r\n\r\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\r\n  .login-container .el-input input {\r\n    color: $cursor;\r\n  }\r\n}\r\n\r\n/* reset element-ui css */\r\n.login-container {\r\n  ::v-deep .el-input {\r\n    display: inline-block;\r\n    height: 47px;\r\n    width: 85%;\r\n\r\n    input {\r\n      background: transparent;\r\n      border: 0px;\r\n      -webkit-appearance: none;\r\n      border-radius: 0px;\r\n      padding: 12px 5px 12px 15px;\r\n      color: #333;\r\n      height: 47px;\r\n      caret-color: #333;\r\n\r\n      &:-webkit-autofill {\r\n        box-shadow: 0 0 0px 1000px $bg inset !important;\r\n        -webkit-text-fill-color: $cursor !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-form-item {\r\n    border: 1px solid rgba(0, 0, 0, 0.1);\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border-radius: 5px;\r\n    color: #454545;\r\n  }\r\n}\r\n$bg: #2d3a4b;\r\n$dark_gray: #889aa4;\r\n$light_gray: #eee;\r\n\r\n.login-container {\r\n  .tips {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    margin-bottom: 10px;\r\n\r\n    span {\r\n      &:first-of-type {\r\n        margin-right: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .svg-container {\r\n    padding: 6px 5px 6px 15px;\r\n    color: $dark_gray;\r\n    vertical-align: middle;\r\n    width: 30px;\r\n    display: inline-block;\r\n  }\r\n\r\n  .title-container {\r\n    position: relative;\r\n\r\n    .title {\r\n      font-size: 26px;\r\n      color: $light_gray;\r\n      margin: 0px auto 40px auto;\r\n      text-align: center;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .show-pwd {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 7px;\r\n    font-size: 16px;\r\n    color: $dark_gray;\r\n    cursor: pointer;\r\n    user-select: none;\r\n  }\r\n\r\n  .thirdparty-button {\r\n    position: absolute;\r\n    right: 0;\r\n    bottom: 6px;\r\n  }\r\n\r\n  @media only screen and (max-width: 470px) {\r\n    .thirdparty-button {\r\n      display: none;\r\n    }\r\n    .login-weaper {\r\n      width: 100%;\r\n      padding: 0 30px;\r\n      box-sizing: border-box;\r\n      box-shadow: none;\r\n    }\r\n    .login-main {\r\n      width: 80%;\r\n    }\r\n    .login-left {\r\n      display: none !important;\r\n    }\r\n    .login-border {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}