{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue", "mtime": 1753924830481}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCmltcG9ydCB7IHVuV3NMb2dvdXQgfSBmcm9tICdAL2FwaS93cycNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1N5c0pvYkxvZ01hbmFnZScsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHdlYnNvY2s6IG51bGwsDQogICAgICBhcnJzOiBbXSwNCiAgICAgIGlkOiB1bmRlZmluZWQsDQogICAgICBncm91cDogdW5kZWZpbmVkDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaWQgPSB0aGlzLmd1aWQoKQ0KICAgIHRoaXMuZ3JvdXAgPSAnbG9nJw0KICAgIHRoaXMuaW5pdFdlYlNvY2tldCgpDQogIH0sDQogIGRlc3Ryb3llZCgpIHsNCiAgICBjb25zb2xlLmxvZygn5pat5byAd2Vic29ja2V06L+e5o6lJykNCiAgICB0aGlzLndlYnNvY2suY2xvc2UoKSAvLyDnprvlvIDot6/nlLHkuYvlkI7mlq3lvIB3ZWJzb2NrZXTov57mjqUNCiAgICB1bldzTG9nb3V0KHRoaXMuaWQsIHRoaXMuZ3JvdXApLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UuZGF0YSkNCiAgICB9DQogICAgKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdFdlYlNvY2tldCgpIHsgLy8g5Yid5aeL5YyWd2Vvc29ja2V0DQogICAgICBjb25zb2xlLmxvZyh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnRva2VuKQ0KICAgICAgY29uc3Qgd3N1cmkgPSAnd3M6Ly8xMjcuMC4wLjE6ODAwMC93cy8nICsgdGhpcy5pZCArICcvJyArIHRoaXMuZ3JvdXAgKyAnP3Rva2VuPScgKyB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnRva2VuDQogICAgICB0aGlzLndlYnNvY2sgPSBuZXcgV2ViU29ja2V0KHdzdXJpKQ0KICAgICAgdGhpcy53ZWJzb2NrLm9ubWVzc2FnZSA9IHRoaXMud2Vic29ja2V0b25tZXNzYWdlDQogICAgICB0aGlzLndlYnNvY2sub25vcGVuID0gdGhpcy53ZWJzb2NrZXRvbm9wZW4NCiAgICAgIHRoaXMud2Vic29jay5vbmVycm9yID0gdGhpcy53ZWJzb2NrZXRvbmVycm9yDQogICAgICB0aGlzLndlYnNvY2sub25jbG9zZSA9IHRoaXMud2Vic29ja2V0Y2xvc2UNCiAgICB9LA0KICAgIHdlYnNvY2tldG9ub3BlbigpIHsgLy8g6L+e5o6l5bu656uL5LmL5ZCO5omn6KGMc2VuZOaWueazleWPkemAgeaVsOaNrg0KICAgICAgY29uc29sZS5sb2coJ+i/nuaOpeaJk+W8gCcpDQogICAgLy8gICBjb25zdCBhY3Rpb25zID0geyAndGVzdCc6ICcxMjM0NScgfQ0KICAgIC8vICAgdGhpcy53ZWJzb2NrZXRzZW5kKEpTT04uc3RyaW5naWZ5KGFjdGlvbnMpKQ0KICAgIH0sDQogICAgd2Vic29ja2V0b25lcnJvcigpIHsgLy8g6L+e5o6l5bu656uL5aSx6LSl6YeN6L+eDQogICAgICB0aGlzLmluaXRXZWJTb2NrZXQoKQ0KICAgIH0sDQogICAgd2Vic29ja2V0b25tZXNzYWdlKGUpIHsgLy8g5pWw5o2u5o6l5pS2DQogICAgICBjb25zb2xlLmxvZyhlLmRhdGEpDQogICAgICAvLyAgIGNvbnNvbGUubG9nKHRoaXMuYmluYXJ5QWdlbnQoZSkpDQogICAgICAvLyAgIGNvbnN0IHJlZGF0YSA9IEpTT04ucGFyc2UoZS5kYXRhKQ0KICAgICAgLy8gICBjb25zb2xlLmxvZyhyZWRhdGEpDQogICAgICAvLyAgIHRoaXMuJHJlZnMubG9nLmlubmVyVGV4dCA9IGUuZGF0YSArICdcbicgKyB0aGlzLiRyZWZzLmxvZy5pbm5lclRleHQNCiAgICAgIHRoaXMuYXJycy51bnNoaWZ0KGUuZGF0YSkNCiAgICB9LA0KICAgIHdlYnNvY2tldHNlbmQoRGF0YSkgeyAvLyDmlbDmja7lj5HpgIENCiAgICAvLyAgIHRoaXMud2Vic29jay5zZW5kKERhdGEpDQogICAgfSwNCiAgICB3ZWJzb2NrZXRjbG9zZShlKSB7IC8vIOWFs+mXrQ0KICAgICAgdW5Xc0xvZ291dCh0aGlzLmlkLCB0aGlzLmdyb3VwKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UuZGF0YSkNCiAgICAgIH0NCiAgICAgICkNCiAgICAgIGNvbnNvbGUubG9nKCfmlq3lvIDov57mjqUnLCBlKQ0KICAgIH0sDQogICAgZ3VpZCgpIHsNCiAgICAgIHJldHVybiAneHh4eHh4eHgteHh4eC00eHh4LXl4eHgteHh4eHh4eHh4eHh4Jy5yZXBsYWNlKC9beHldL2csIGZ1bmN0aW9uKGMpIHsNCiAgICAgICAgdmFyIHIgPSBNYXRoLnJhbmRvbSgpICogMTYgfCAwOyB2YXIgdiA9IGMgPT09ICd4JyA/IHIgOiAociAmIDB4MyB8IDB4OCkNCiAgICAgICAgcmV0dXJuIHYudG9TdHJpbmcoMTYpDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0KDQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue"], "names": [], "mappings": ";;AA+BA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;MACA;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC;IACH;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/schedule/log.vue", "sourceRoot": "", "sourcesContent": ["\r\n<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form>\r\n          <el-form-item>\r\n            <el-button type=\"success\" icon=\"el-icon-search\" size=\"mini\">状态</el-button>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\">清空</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row ref=\"log\" :gutter=\"10\" class=\"mb8\">\r\n          <el-scrollbar style=\"height:500px;background-color: black;color: cornflowerblue;\">\r\n            <ul\r\n              style=\"line-height: 25px;padding-top: 15px;padding-bottom: 15px;min-height: 500px; margin: 0;list-style-type: none;\"\r\n            >\r\n              <li v-for=\"(item,index) in arrs\" :key=\"index\">\r\n\r\n                {{ item }}\r\n              </li>\r\n            </ul>\r\n          </el-scrollbar>\r\n        </el-row>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\nimport { unWsLogout } from '@/api/ws'\r\nexport default {\r\n  name: 'SysJobLogManage',\r\n  data() {\r\n    return {\r\n      websock: null,\r\n      arrs: [],\r\n      id: undefined,\r\n      group: undefined\r\n    }\r\n  },\r\n  created() {\r\n    this.id = this.guid()\r\n    this.group = 'log'\r\n    this.initWebSocket()\r\n  },\r\n  destroyed() {\r\n    console.log('断开websocket连接')\r\n    this.websock.close() // 离开路由之后断开websocket连接\r\n    unWsLogout(this.id, this.group).then(response => {\r\n      console.log(response.data)\r\n    }\r\n    )\r\n  },\r\n  methods: {\r\n    initWebSocket() { // 初始化weosocket\r\n      console.log(this.$store.state.user.token)\r\n      const wsuri = 'ws://127.0.0.1:8000/ws/' + this.id + '/' + this.group + '?token=' + this.$store.state.user.token\r\n      this.websock = new WebSocket(wsuri)\r\n      this.websock.onmessage = this.websocketonmessage\r\n      this.websock.onopen = this.websocketonopen\r\n      this.websock.onerror = this.websocketonerror\r\n      this.websock.onclose = this.websocketclose\r\n    },\r\n    websocketonopen() { // 连接建立之后执行send方法发送数据\r\n      console.log('连接打开')\r\n    //   const actions = { 'test': '12345' }\r\n    //   this.websocketsend(JSON.stringify(actions))\r\n    },\r\n    websocketonerror() { // 连接建立失败重连\r\n      this.initWebSocket()\r\n    },\r\n    websocketonmessage(e) { // 数据接收\r\n      console.log(e.data)\r\n      //   console.log(this.binaryAgent(e))\r\n      //   const redata = JSON.parse(e.data)\r\n      //   console.log(redata)\r\n      //   this.$refs.log.innerText = e.data + '\\n' + this.$refs.log.innerText\r\n      this.arrs.unshift(e.data)\r\n    },\r\n    websocketsend(Data) { // 数据发送\r\n    //   this.websock.send(Data)\r\n    },\r\n    websocketclose(e) { // 关闭\r\n      unWsLogout(this.id, this.group).then(response => {\r\n        console.log(response.data)\r\n      }\r\n      )\r\n      console.log('断开连接', e)\r\n    },\r\n    guid() {\r\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n        var r = Math.random() * 16 | 0; var v = c === 'x' ? r : (r & 0x3 | 0x8)\r\n        return v.toString(16)\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n</script>\r\n"]}]}