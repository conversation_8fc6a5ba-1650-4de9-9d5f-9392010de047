{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniBar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniBar\\index.vue", "mtime": 1753924830052}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgbW9tZW50IGZyb20gJ21vbWVudCcNCmNvbnN0IGRhdGEgPSBbXQ0KY29uc3QgYmVnaW5EYXkgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKQ0KZm9yIChsZXQgaSA9IDA7IGkgPCAxMDsgaSsrKSB7DQogIGRhdGEucHVzaCh7DQogICAgeDogbW9tZW50KG5ldyBEYXRlKGJlZ2luRGF5ICsgMTAwMCAqIDYwICogNjAgKiAyNCAqIGkpKS5mb3JtYXQoJ1lZWVktTU0tREQnKSwNCiAgICB5OiBNYXRoLnJvdW5kKE1hdGgucmFuZG9tKCkgKiAxMCkNCiAgfSkNCn0NCmNvbnN0IHRvb2x0aXAgPSBbDQogICd4KnknLA0KICAoeCwgeSkgPT4gKHsNCiAgICBuYW1lOiB4LA0KICAgIHZhbHVlOiB5DQogIH0pDQpdDQpjb25zdCBzY2FsZSA9IFt7DQogIGRhdGFLZXk6ICd4JywNCiAgbWluOiAyDQp9LCB7DQogIGRhdGFLZXk6ICd5JywNCiAgdGl0bGU6ICfml7bpl7QnLA0KICBtaW46IDEsDQogIG1heDogMzANCn1dDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNaW5pQmFyJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZGF0YSwNCiAgICAgIHRvb2x0aXAsDQogICAgICBzY2FsZSwNCiAgICAgIGhlaWdodDogMTAwDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniBar\\index.vue"], "names": [], "mappings": ";AAYA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAClC,CAAC;AACH;AACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC;AACH;AACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,EAAE;AACP,CAAC,EAAE;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,EAAE,CAAC;EACN,CAAC,CAAC,CAAC,EAAE,CAAC;AACR,CAAC;AACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/MiniBar/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"antv-chart-mini\">\r\n    <div class=\"chart-wrapper\" :style=\"{ height: 46 }\">\r\n      <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :padding=\"[36, 5, 18, 5]\">\r\n        <v-tooltip />\r\n        <v-bar position=\"x*y\" />\r\n      </v-chart>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport moment from 'moment'\r\nconst data = []\r\nconst beginDay = new Date().getTime()\r\nfor (let i = 0; i < 10; i++) {\r\n  data.push({\r\n    x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\r\n    y: Math.round(Math.random() * 10)\r\n  })\r\n}\r\nconst tooltip = [\r\n  'x*y',\r\n  (x, y) => ({\r\n    name: x,\r\n    value: y\r\n  })\r\n]\r\nconst scale = [{\r\n  dataKey: 'x',\r\n  min: 2\r\n}, {\r\n  dataKey: 'y',\r\n  title: '时间',\r\n  min: 1,\r\n  max: 30\r\n}]\r\nexport default {\r\n  name: 'MiniBar',\r\n  data() {\r\n    return {\r\n      data,\r\n      tooltip,\r\n      scale,\r\n      height: 100\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .antv-chart-mini {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  .chart-wrapper {\r\n    position: absolute;\r\n    bottom: -28px;\r\n    width: 100%;\r\n\r\n/*    margin: 0 -5px;\r\n    overflow: hidden;*/\r\n  }\r\n}\r\n</style>\r\n"]}]}