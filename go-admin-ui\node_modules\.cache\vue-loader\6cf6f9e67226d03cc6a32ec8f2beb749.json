{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\genInfoForm.vue?vue&type=template&id=6cbc3ca6", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\genInfoForm.vue", "mtime": 1753924830298}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\genInfoForm.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAER,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/gen/genInfoForm.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-form ref=\"genInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tplCategory\">\r\n          <span slot=\"label\">生成模板</span>\r\n          <el-select v-model=\"info.tplCategory\">\r\n            <el-option label=\"关系表（增删改查）\" value=\"crud\" />\r\n            <!-- <el-option label=\"关系表（增删改查）\" value=\"mcrud\" />\r\n            <el-option label=\"树表（增删改查）\" value=\"tree\" /> -->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"packageName\">\r\n          <span slot=\"label\">\r\n            应用名\r\n            <el-tooltip content=\"应用名，例如：在app文件夹下将该功能发到那个应用中，默认：admin\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.packageName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"moduleFrontName\">\r\n          <span slot=\"label\">\r\n            前端文件名\r\n            <el-tooltip content=\"前端项目文件名，例如 sys-user.js \" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.moduleFrontName\" />\r\n        </el-form-item>\r\n      </el-col> -->\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"businessName\">\r\n          <span slot=\"label\">\r\n            业务名\r\n            <el-tooltip content=\"可理解为功能英文名，例如 user\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.businessName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"functionName\">\r\n          <span slot=\"label\">\r\n            功能描述\r\n            <el-tooltip content=\"同步的数据库表备注，用作类描述，例如：用户\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.functionName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"moduleName\">\r\n          <span slot=\"label\">\r\n            接口路径\r\n            <el-tooltip content=\"接口路径，例如：api/v1/{sys-user}\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.moduleName\">\r\n            <template slot=\"prepend\">api/{version}/</template>\r\n            <template slot=\"append\">...</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <!-- <el-alert\r\n          title=\"接口地址示例\"\r\n          description=\"[get]api/{version}/{接口路径} \\r\\n [post]\"\r\n          type=\"success\"\r\n          show-icon\r\n        /> -->\r\n      </el-col>\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isDataScope\">\r\n          <span slot=\"label\">\r\n            是否认证\r\n            <el-tooltip content=\"是指是否使用用户和角色验证中间件\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.isAuth\">\r\n            <el-option label=\"true\" value=\"1\" />\r\n            <el-option label=\"false\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isDataScope\">\r\n          <span slot=\"label\">\r\n            数据权限\r\n            <el-tooltip content=\"暂不支持\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.isDataScope\" disabled>\r\n            <el-option label=\"true\" value=\"1\" />\r\n            <el-option label=\"false\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isActions\">\r\n          <span slot=\"label\">\r\n            是否actions\r\n            <el-tooltip content=\"系统通用增删改查中间件方法\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.isActions\" disabled>\r\n            <el-option label=\"false\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col> -->\r\n    </el-row>\r\n\r\n    <el-row v-show=\"info.tplCategory == 'tree'\">\r\n      <h4 class=\"form-header\">其他信息</h4>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树编码字段\r\n            <el-tooltip content=\"树显示的编码字段名， 如：dept_id\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"column in info.columns\"\r\n              :key=\"column.columnName\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树父编码字段\r\n            <el-tooltip content=\"树显示的父编码字段名， 如：parent_Id\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeParentCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"column in info.columns\"\r\n              :key=\"column.columnName\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树名称字段\r\n            <el-tooltip content=\"树节点的显示名称字段名， 如：dept_name\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeName\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"column in info.columns\"\r\n              :key=\"column.columnName\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoForm',\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tplCategory: [\r\n          { required: true, message: '请选择生成模板', trigger: 'blur' }\r\n        ],\r\n        packageName: [\r\n          { required: true, message: '请输入生成包路径', trigger: 'blur' },\r\n          { pattern: /^[a-z]*$/g, trigger: 'blur', message: '只允许小写字母,例如 system 格式' }\r\n        ],\r\n        moduleName: [\r\n          { required: true, message: '请输入生成模块名', trigger: 'blur' },\r\n          { pattern: /^[a-z\\-]*[a-z]$/g, trigger: 'blur', message: '只允许小写字母,例如 sys-demo 格式' }\r\n        ],\r\n        businessName: [\r\n          { required: true, message: '请输入生成业务名', trigger: 'blur' },\r\n          { pattern: /^[a-z][A-Za-z]+$/, trigger: 'blur', message: '校验规则:  只允许输入字母 a-z 或大写 A-Z ，并且小写字母开头' }\r\n        ],\r\n        functionName: [\r\n          { required: true, message: '请输入生成功能名', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {}\r\n}\r\n</script>\r\n"]}]}