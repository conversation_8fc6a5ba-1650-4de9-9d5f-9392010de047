{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\index.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\index.js", "mtime": 1753924830258}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "time", "cFormat", "arguments", "length", "time_str", "indexOf", "format", "date", "_typeof", "test", "parseInt", "toString", "Date", "formatObj", "y", "getFullYear", "m", "getMonth", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "a", "getDay", "replace", "result", "key", "value", "padStart", "formatTime", "option", "now", "diff", "Math", "ceil", "getQueryObject", "url", "window", "location", "href", "search", "substring", "lastIndexOf", "obj", "reg", "rs", "$1", "$2", "name", "decodeURIComponent", "val", "String", "byteLength", "str", "code", "charCodeAt", "cleanArray", "actual", "newArray", "push", "param", "json", "Object", "keys", "map", "undefined", "encodeURIComponent", "join", "param2Obj", "split", "JSON", "parse", "html2Text", "div", "document", "createElement", "innerHTML", "textContent", "innerText", "objectMerge", "target", "source", "Array", "isArray", "slice", "for<PERSON>ach", "property", "sourceProperty", "toggleClass", "element", "className", "classString", "nameIndex", "substr", "getTime", "type", "toDateString", "debounce", "func", "wait", "immediate", "timeout", "args", "context", "timestamp", "later", "last", "setTimeout", "apply", "_len", "_key", "callNow", "deepClone", "Error", "targetObj", "constructor", "uniqueArr", "arr", "from", "Set", "createUniqueString", "randomNum", "random", "hasClass", "ele", "cls", "match", "RegExp", "addClass", "removeClass", "formatJson", "filterVal", "jsonData", "v", "j"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/index.js"], "sourcesContent": ["/**\r\n * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/11/18.\r\n */\r\n\r\n/**\r\n * Parse the time to string\r\n * @param {(Object|string|number)} time\r\n * @param {string} cFormat\r\n * @returns {string | null}\r\n */\r\nexport function parseTime(time, cFormat) {\r\n  if (arguments.length === 0) {\r\n    return null\r\n  }\r\n  if (time_str.indexOf('01-01-01') > -1) {\r\n    return '-'\r\n  }\r\n  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'\r\n  let date\r\n  if (typeof time === 'object') {\r\n    date = time\r\n  } else {\r\n    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {\r\n      time = parseInt(time)\r\n    }\r\n    if ((typeof time === 'number') && (time.toString().length === 10)) {\r\n      time = time * 1000\r\n    }\r\n    date = new Date(time)\r\n  }\r\n  const formatObj = {\r\n    y: date.getFullYear(),\r\n    m: date.getMonth() + 1,\r\n    d: date.getDate(),\r\n    h: date.getHours(),\r\n    i: date.getMinutes(),\r\n    s: date.getSeconds(),\r\n    a: date.getDay()\r\n  }\r\n  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\r\n    const value = formatObj[key]\r\n    // Note: getDay() returns 0 on Sunday\r\n    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }\r\n    return value.toString().padStart(2, '0')\r\n  })\r\n\r\n  return time_str\r\n}\r\n\r\n/**\r\n * @param {number} time\r\n * @param {string} option\r\n * @returns {string}\r\n */\r\nexport function formatTime(time, option) {\r\n  if (('' + time).length === 10) {\r\n    time = parseInt(time) * 1000\r\n  } else {\r\n    time = +time\r\n  }\r\n  const d = new Date(time)\r\n  const now = Date.now()\r\n\r\n  const diff = (now - d) / 1000\r\n\r\n  if (diff < 30) {\r\n    return '刚刚'\r\n  } else if (diff < 3600) {\r\n    // less 1 hour\r\n    return Math.ceil(diff / 60) + '分钟前'\r\n  } else if (diff < 3600 * 24) {\r\n    return Math.ceil(diff / 3600) + '小时前'\r\n  } else if (diff < 3600 * 24 * 2) {\r\n    return '1天前'\r\n  }\r\n  if (option) {\r\n    return parseTime(time, option)\r\n  } else {\r\n    return (\r\n      d.getMonth() +\r\n      1 +\r\n      '月' +\r\n      d.getDate() +\r\n      '日' +\r\n      d.getHours() +\r\n      '时' +\r\n      d.getMinutes() +\r\n      '分'\r\n    )\r\n  }\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Object}\r\n */\r\nexport function getQueryObject(url) {\r\n  url = url == null ? window.location.href : url\r\n  const search = url.substring(url.lastIndexOf('?') + 1)\r\n  const obj = {}\r\n  const reg = /([^?&=]+)=([^?&=]*)/g\r\n  search.replace(reg, (rs, $1, $2) => {\r\n    const name = decodeURIComponent($1)\r\n    let val = decodeURIComponent($2)\r\n    val = String(val)\r\n    obj[name] = val\r\n    return rs\r\n  })\r\n  return obj\r\n}\r\n\r\n/**\r\n * @param {string} input value\r\n * @returns {number} output value\r\n */\r\nexport function byteLength(str) {\r\n  // returns the byte length of an utf8 string\r\n  let s = str.length\r\n  for (var i = str.length - 1; i >= 0; i--) {\r\n    const code = str.charCodeAt(i)\r\n    if (code > 0x7f && code <= 0x7ff) s++\r\n    else if (code > 0x7ff && code <= 0xffff) s += 2\r\n    if (code >= 0xDC00 && code <= 0xDFFF) i--\r\n  }\r\n  return s\r\n}\r\n\r\n/**\r\n * @param {Array} actual\r\n * @returns {Array}\r\n */\r\nexport function cleanArray(actual) {\r\n  const newArray = []\r\n  for (let i = 0; i < actual.length; i++) {\r\n    if (actual[i]) {\r\n      newArray.push(actual[i])\r\n    }\r\n  }\r\n  return newArray\r\n}\r\n\r\n/**\r\n * @param {Object} json\r\n * @returns {Array}\r\n */\r\nexport function param(json) {\r\n  if (!json) return ''\r\n  return cleanArray(\r\n    Object.keys(json).map(key => {\r\n      if (json[key] === undefined) return ''\r\n      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])\r\n    })\r\n  ).join('&')\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Object}\r\n */\r\nexport function param2Obj(url) {\r\n  const search = url.split('?')[1]\r\n  if (!search) {\r\n    return {}\r\n  }\r\n  return JSON.parse(\r\n    '{\"' +\r\n    decodeURIComponent(search)\r\n      .replace(/\"/g, '\\\\\"')\r\n      .replace(/&/g, '\",\"')\r\n      .replace(/=/g, '\":\"')\r\n      .replace(/\\+/g, ' ') +\r\n    '\"}'\r\n  )\r\n}\r\n\r\n/**\r\n * @param {string} val\r\n * @returns {string}\r\n */\r\nexport function html2Text(val) {\r\n  const div = document.createElement('div')\r\n  div.innerHTML = val\r\n  return div.textContent || div.innerText\r\n}\r\n\r\n/**\r\n * Merges two objects, giving the last one precedence\r\n * @param {Object} target\r\n * @param {(Object|Array)} source\r\n * @returns {Object}\r\n */\r\nexport function objectMerge(target, source) {\r\n  if (typeof target !== 'object') {\r\n    target = {}\r\n  }\r\n  if (Array.isArray(source)) {\r\n    return source.slice()\r\n  }\r\n  Object.keys(source).forEach(property => {\r\n    const sourceProperty = source[property]\r\n    if (typeof sourceProperty === 'object') {\r\n      target[property] = objectMerge(target[property], sourceProperty)\r\n    } else {\r\n      target[property] = sourceProperty\r\n    }\r\n  })\r\n  return target\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} element\r\n * @param {string} className\r\n */\r\nexport function toggleClass(element, className) {\r\n  if (!element || !className) {\r\n    return\r\n  }\r\n  let classString = element.className\r\n  const nameIndex = classString.indexOf(className)\r\n  if (nameIndex === -1) {\r\n    classString += '' + className\r\n  } else {\r\n    classString =\r\n      classString.substr(0, nameIndex) +\r\n      classString.substr(nameIndex + className.length)\r\n  }\r\n  element.className = classString\r\n}\r\n\r\n/**\r\n * @param {string} type\r\n * @returns {Date}\r\n */\r\nexport function getTime(type) {\r\n  if (type === 'start') {\r\n    return new Date().getTime() - 3600 * 1000 * 24 * 90\r\n  } else {\r\n    return new Date(new Date().toDateString())\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Function} func\r\n * @param {number} wait\r\n * @param {boolean} immediate\r\n * @return {*}\r\n */\r\nexport function debounce(func, wait, immediate) {\r\n  let timeout, args, context, timestamp, result\r\n\r\n  const later = function() {\r\n    // 据上一次触发时间间隔\r\n    const last = +new Date() - timestamp\r\n\r\n    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait\r\n    if (last < wait && last > 0) {\r\n      timeout = setTimeout(later, wait - last)\r\n    } else {\r\n      timeout = null\r\n      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用\r\n      if (!immediate) {\r\n        result = func.apply(context, args)\r\n        if (!timeout) context = args = null\r\n      }\r\n    }\r\n  }\r\n\r\n  return function(...args) {\r\n    context = this\r\n    timestamp = +new Date()\r\n    const callNow = immediate && !timeout\r\n    // 如果延时不存在，重新设定延时\r\n    if (!timeout) timeout = setTimeout(later, wait)\r\n    if (callNow) {\r\n      result = func.apply(context, args)\r\n      context = args = null\r\n    }\r\n\r\n    return result\r\n  }\r\n}\r\n\r\n/**\r\n * This is just a simple version of deep copy\r\n * Has a lot of edge cases bug\r\n * If you want to use a perfect deep copy, use lodash's _.cloneDeep\r\n * @param {Object} source\r\n * @returns {Object}\r\n */\r\nexport function deepClone(source) {\r\n  if (!source && typeof source !== 'object') {\r\n    throw new Error('error arguments', 'deepClone')\r\n  }\r\n  const targetObj = source.constructor === Array ? [] : {}\r\n  Object.keys(source).forEach(keys => {\r\n    if (source[keys] && typeof source[keys] === 'object') {\r\n      targetObj[keys] = deepClone(source[keys])\r\n    } else {\r\n      targetObj[keys] = source[keys]\r\n    }\r\n  })\r\n  return targetObj\r\n}\r\n\r\n/**\r\n * @param {Array} arr\r\n * @returns {Array}\r\n */\r\nexport function uniqueArr(arr) {\r\n  return Array.from(new Set(arr))\r\n}\r\n\r\n/**\r\n * @returns {string}\r\n */\r\nexport function createUniqueString() {\r\n  const timestamp = +new Date() + ''\r\n  const randomNum = parseInt((1 + Math.random()) * 65536) + ''\r\n  return (+(randomNum + timestamp)).toString(32)\r\n}\r\n\r\n/**\r\n * Check if an element has a class\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n * @returns {boolean}\r\n */\r\nexport function hasClass(ele, cls) {\r\n  return !!ele.className.match(new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)'))\r\n}\r\n\r\n/**\r\n * Add class to element\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n */\r\nexport function addClass(ele, cls) {\r\n  if (!hasClass(ele, cls)) ele.className += ' ' + cls\r\n}\r\n\r\n/**\r\n * Remove class from element\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n */\r\nexport function removeClass(ele, cls) {\r\n  if (hasClass(ele, cls)) {\r\n    const reg = new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)')\r\n    ele.className = ele.className.replace(reg, ' ')\r\n  }\r\n}\r\n\r\n/**\r\n * @param {Array} filterVal\r\n * @param {Object} jsonData\r\n * @returns {string}\r\n */\r\nexport function formatJson(filterVal, jsonData) {\r\n  return jsonData.map(v => filterVal.map(j => {\r\n    if (j === 'timestamp') {\r\n      return parseTime(v[j])\r\n    } else {\r\n      return v[j]\r\n    }\r\n  }))\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EACA,IAAIC,QAAQ,CAACC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;IACrC,OAAO,GAAG;EACZ;EACA,IAAMC,MAAM,GAAGL,OAAO,IAAI,yBAAyB;EACnD,IAAIM,IAAI;EACR,IAAIC,OAAA,CAAOR,IAAI,MAAK,QAAQ,EAAE;IAC5BO,IAAI,GAAGP,IAAI;EACb,CAAC,MAAM;IACL,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAM,UAAU,CAACS,IAAI,CAACT,IAAI,CAAE,EAAE;MACzDA,IAAI,GAAGU,QAAQ,CAACV,IAAI,CAAC;IACvB;IACA,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAMA,IAAI,CAACW,QAAQ,CAAC,CAAC,CAACR,MAAM,KAAK,EAAG,EAAE;MACjEH,IAAI,GAAGA,IAAI,GAAG,IAAI;IACpB;IACAO,IAAI,GAAG,IAAIK,IAAI,CAACZ,IAAI,CAAC;EACvB;EACA,IAAMa,SAAS,GAAG;IAChBC,CAAC,EAAEP,IAAI,CAACQ,WAAW,CAAC,CAAC;IACrBC,CAAC,EAAET,IAAI,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC;IACtBC,CAAC,EAAEX,IAAI,CAACY,OAAO,CAAC,CAAC;IACjBC,CAAC,EAAEb,IAAI,CAACc,QAAQ,CAAC,CAAC;IAClBC,CAAC,EAAEf,IAAI,CAACgB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEjB,IAAI,CAACkB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEnB,IAAI,CAACoB,MAAM,CAAC;EACjB,CAAC;EACD,IAAMvB,QAAQ,GAAGE,MAAM,CAACsB,OAAO,CAAC,iBAAiB,EAAE,UAACC,MAAM,EAAEC,GAAG,EAAK;IAClE,IAAMC,KAAK,GAAGlB,SAAS,CAACiB,GAAG,CAAC;IAC5B;IACA,IAAIA,GAAG,KAAK,GAAG,EAAE;MAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAC;IAAC;IACrE,OAAOA,KAAK,CAACpB,QAAQ,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1C,CAAC,CAAC;EAEF,OAAO5B,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,UAAUA,CAACjC,IAAI,EAAEkC,MAAM,EAAE;EACvC,IAAI,CAAC,EAAE,GAAGlC,IAAI,EAAEG,MAAM,KAAK,EAAE,EAAE;IAC7BH,IAAI,GAAGU,QAAQ,CAACV,IAAI,CAAC,GAAG,IAAI;EAC9B,CAAC,MAAM;IACLA,IAAI,GAAG,CAACA,IAAI;EACd;EACA,IAAMkB,CAAC,GAAG,IAAIN,IAAI,CAACZ,IAAI,CAAC;EACxB,IAAMmC,GAAG,GAAGvB,IAAI,CAACuB,GAAG,CAAC,CAAC;EAEtB,IAAMC,IAAI,GAAG,CAACD,GAAG,GAAGjB,CAAC,IAAI,IAAI;EAE7B,IAAIkB,IAAI,GAAG,EAAE,EAAE;IACb,OAAO,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,EAAE;IACtB;IACA,OAAOC,IAAI,CAACC,IAAI,CAACF,IAAI,GAAG,EAAE,CAAC,GAAG,KAAK;EACrC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,EAAE,EAAE;IAC3B,OAAOC,IAAI,CAACC,IAAI,CAACF,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK;EACvC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE;IAC/B,OAAO,KAAK;EACd;EACA,IAAIF,MAAM,EAAE;IACV,OAAOnC,SAAS,CAACC,IAAI,EAAEkC,MAAM,CAAC;EAChC,CAAC,MAAM;IACL,OACEhB,CAAC,CAACD,QAAQ,CAAC,CAAC,GACZ,CAAC,GACD,GAAG,GACHC,CAAC,CAACC,OAAO,CAAC,CAAC,GACX,GAAG,GACHD,CAAC,CAACG,QAAQ,CAAC,CAAC,GACZ,GAAG,GACHH,CAAC,CAACK,UAAU,CAAC,CAAC,GACd,GAAG;EAEP;AACF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASgB,cAAcA,CAACC,GAAG,EAAE;EAClCA,GAAG,GAAGA,GAAG,IAAI,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,GAAG;EAC9C,IAAMI,MAAM,GAAGJ,GAAG,CAACK,SAAS,CAACL,GAAG,CAACM,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACtD,IAAMC,GAAG,GAAG,CAAC,CAAC;EACd,IAAMC,GAAG,GAAG,sBAAsB;EAClCJ,MAAM,CAAChB,OAAO,CAACoB,GAAG,EAAE,UAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAK;IAClC,IAAMC,IAAI,GAAGC,kBAAkB,CAACH,EAAE,CAAC;IACnC,IAAII,GAAG,GAAGD,kBAAkB,CAACF,EAAE,CAAC;IAChCG,GAAG,GAAGC,MAAM,CAACD,GAAG,CAAC;IACjBP,GAAG,CAACK,IAAI,CAAC,GAAGE,GAAG;IACf,OAAOL,EAAE;EACX,CAAC,CAAC;EACF,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASS,UAAUA,CAACC,GAAG,EAAE;EAC9B;EACA,IAAIjC,CAAC,GAAGiC,GAAG,CAACtD,MAAM;EAClB,KAAK,IAAImB,CAAC,GAAGmC,GAAG,CAACtD,MAAM,GAAG,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,IAAMoC,IAAI,GAAGD,GAAG,CAACE,UAAU,CAACrC,CAAC,CAAC;IAC9B,IAAIoC,IAAI,GAAG,IAAI,IAAIA,IAAI,IAAI,KAAK,EAAElC,CAAC,EAAE,MAChC,IAAIkC,IAAI,GAAG,KAAK,IAAIA,IAAI,IAAI,MAAM,EAAElC,CAAC,IAAI,CAAC;IAC/C,IAAIkC,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAEpC,CAAC,EAAE;EAC3C;EACA,OAAOE,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASoC,UAAUA,CAACC,MAAM,EAAE;EACjC,IAAMC,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,MAAM,CAAC1D,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACtC,IAAIuC,MAAM,CAACvC,CAAC,CAAC,EAAE;MACbwC,QAAQ,CAACC,IAAI,CAACF,MAAM,CAACvC,CAAC,CAAC,CAAC;IAC1B;EACF;EACA,OAAOwC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,OAAOL,UAAU,CACfM,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,UAAAtC,GAAG,EAAI;IAC3B,IAAImC,IAAI,CAACnC,GAAG,CAAC,KAAKuC,SAAS,EAAE,OAAO,EAAE;IACtC,OAAOC,kBAAkB,CAACxC,GAAG,CAAC,GAAG,GAAG,GAAGwC,kBAAkB,CAACL,IAAI,CAACnC,GAAG,CAAC,CAAC;EACtE,CAAC,CACH,CAAC,CAACyC,IAAI,CAAC,GAAG,CAAC;AACb;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAChC,GAAG,EAAE;EAC7B,IAAMI,MAAM,GAAGJ,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,IAAI,CAAC7B,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,OAAO8B,IAAI,CAACC,KAAK,CACf,IAAI,GACJtB,kBAAkB,CAACT,MAAM,CAAC,CACvBhB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CACpBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GACtB,IACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASgD,SAASA,CAACtB,GAAG,EAAE;EAC7B,IAAMuB,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzCF,GAAG,CAACG,SAAS,GAAG1B,GAAG;EACnB,OAAOuB,GAAG,CAACI,WAAW,IAAIJ,GAAG,CAACK,SAAS;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC1C,IAAI7E,OAAA,CAAO4E,MAAM,MAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAG,CAAC,CAAC;EACb;EACA,IAAIE,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzB,OAAOA,MAAM,CAACG,KAAK,CAAC,CAAC;EACvB;EACAtB,MAAM,CAACC,IAAI,CAACkB,MAAM,CAAC,CAACI,OAAO,CAAC,UAAAC,QAAQ,EAAI;IACtC,IAAMC,cAAc,GAAGN,MAAM,CAACK,QAAQ,CAAC;IACvC,IAAIlF,OAAA,CAAOmF,cAAc,MAAK,QAAQ,EAAE;MACtCP,MAAM,CAACM,QAAQ,CAAC,GAAGP,WAAW,CAACC,MAAM,CAACM,QAAQ,CAAC,EAAEC,cAAc,CAAC;IAClE,CAAC,MAAM;MACLP,MAAM,CAACM,QAAQ,CAAC,GAAGC,cAAc;IACnC;EACF,CAAC,CAAC;EACF,OAAOP,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,WAAWA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAC9C,IAAI,CAACD,OAAO,IAAI,CAACC,SAAS,EAAE;IAC1B;EACF;EACA,IAAIC,WAAW,GAAGF,OAAO,CAACC,SAAS;EACnC,IAAME,SAAS,GAAGD,WAAW,CAAC1F,OAAO,CAACyF,SAAS,CAAC;EAChD,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBD,WAAW,IAAI,EAAE,GAAGD,SAAS;EAC/B,CAAC,MAAM;IACLC,WAAW,GACTA,WAAW,CAACE,MAAM,CAAC,CAAC,EAAED,SAAS,CAAC,GAChCD,WAAW,CAACE,MAAM,CAACD,SAAS,GAAGF,SAAS,CAAC3F,MAAM,CAAC;EACpD;EACA0F,OAAO,CAACC,SAAS,GAAGC,WAAW;AACjC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACC,IAAI,EAAE;EAC5B,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,IAAIvF,IAAI,CAAC,CAAC,CAACsF,OAAO,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;EACrD,CAAC,MAAM;IACL,OAAO,IAAItF,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACwF,YAAY,CAAC,CAAC,CAAC;EAC5C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAE;EAC9C,IAAIC,OAAO,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAE/E,MAAM;EAE7C,IAAMgF,MAAK,GAAG,SAARA,KAAKA,CAAA,EAAc;IACvB;IACA,IAAMC,IAAI,GAAG,CAAC,IAAIlG,IAAI,CAAC,CAAC,GAAGgG,SAAS;;IAEpC;IACA,IAAIE,IAAI,GAAGP,IAAI,IAAIO,IAAI,GAAG,CAAC,EAAE;MAC3BL,OAAO,GAAGM,UAAU,CAACF,MAAK,EAAEN,IAAI,GAAGO,IAAI,CAAC;IAC1C,CAAC,MAAM;MACLL,OAAO,GAAG,IAAI;MACd;MACA,IAAI,CAACD,SAAS,EAAE;QACd3E,MAAM,GAAGyE,IAAI,CAACU,KAAK,CAACL,OAAO,EAAED,IAAI,CAAC;QAClC,IAAI,CAACD,OAAO,EAAEE,OAAO,GAAGD,IAAI,GAAG,IAAI;MACrC;IACF;EACF,CAAC;EAED,OAAO,YAAkB;IAAA,SAAAO,IAAA,GAAA/G,SAAA,CAAAC,MAAA,EAANuG,IAAI,OAAApB,KAAA,CAAA2B,IAAA,GAAAC,IAAA,MAAAA,IAAA,GAAAD,IAAA,EAAAC,IAAA;MAAJR,IAAI,CAAAQ,IAAA,IAAAhH,SAAA,CAAAgH,IAAA;IAAA;IACrBP,OAAO,GAAG,IAAI;IACdC,SAAS,GAAG,CAAC,IAAIhG,IAAI,CAAC,CAAC;IACvB,IAAMuG,OAAO,GAAGX,SAAS,IAAI,CAACC,OAAO;IACrC;IACA,IAAI,CAACA,OAAO,EAAEA,OAAO,GAAGM,UAAU,CAACF,MAAK,EAAEN,IAAI,CAAC;IAC/C,IAAIY,OAAO,EAAE;MACXtF,MAAM,GAAGyE,IAAI,CAACU,KAAK,CAACL,OAAO,EAAED,IAAI,CAAC;MAClCC,OAAO,GAAGD,IAAI,GAAG,IAAI;IACvB;IAEA,OAAO7E,MAAM;EACf,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASuF,SAASA,CAAC/B,MAAM,EAAE;EAChC,IAAI,CAACA,MAAM,IAAI7E,OAAA,CAAO6E,MAAM,MAAK,QAAQ,EAAE;IACzC,MAAM,IAAIgC,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC;EACjD;EACA,IAAMC,SAAS,GAAGjC,MAAM,CAACkC,WAAW,KAAKjC,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;EACxDpB,MAAM,CAACC,IAAI,CAACkB,MAAM,CAAC,CAACI,OAAO,CAAC,UAAAtB,IAAI,EAAI;IAClC,IAAIkB,MAAM,CAAClB,IAAI,CAAC,IAAI3D,OAAA,CAAO6E,MAAM,CAAClB,IAAI,CAAC,MAAK,QAAQ,EAAE;MACpDmD,SAAS,CAACnD,IAAI,CAAC,GAAGiD,SAAS,CAAC/B,MAAM,CAAClB,IAAI,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLmD,SAAS,CAACnD,IAAI,CAAC,GAAGkB,MAAM,CAAClB,IAAI,CAAC;IAChC;EACF,CAAC,CAAC;EACF,OAAOmD,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,SAASA,CAACC,GAAG,EAAE;EAC7B,OAAOnC,KAAK,CAACoC,IAAI,CAAC,IAAIC,GAAG,CAACF,GAAG,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA,OAAO,SAASG,kBAAkBA,CAAA,EAAG;EACnC,IAAMhB,SAAS,GAAG,CAAC,IAAIhG,IAAI,CAAC,CAAC,GAAG,EAAE;EAClC,IAAMiH,SAAS,GAAGnH,QAAQ,CAAC,CAAC,CAAC,GAAG2B,IAAI,CAACyF,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;EAC5D,OAAO,CAAC,EAAED,SAAS,GAAGjB,SAAS,CAAC,EAAEjG,QAAQ,CAAC,EAAE,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoH,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACjC,OAAO,CAAC,CAACD,GAAG,CAAClC,SAAS,CAACoC,KAAK,CAAC,IAAIC,MAAM,CAAC,SAAS,GAAGF,GAAG,GAAG,SAAS,CAAC,CAAC;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,QAAQA,CAACJ,GAAG,EAAEC,GAAG,EAAE;EACjC,IAAI,CAACF,QAAQ,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAED,GAAG,CAAClC,SAAS,IAAI,GAAG,GAAGmC,GAAG;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,WAAWA,CAACL,GAAG,EAAEC,GAAG,EAAE;EACpC,IAAIF,QAAQ,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;IACtB,IAAMjF,GAAG,GAAG,IAAImF,MAAM,CAAC,SAAS,GAAGF,GAAG,GAAG,SAAS,CAAC;IACnDD,GAAG,CAAClC,SAAS,GAAGkC,GAAG,CAAClC,SAAS,CAAClE,OAAO,CAACoB,GAAG,EAAE,GAAG,CAAC;EACjD;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsF,UAAUA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EAC9C,OAAOA,QAAQ,CAACpE,GAAG,CAAC,UAAAqE,CAAC;IAAA,OAAIF,SAAS,CAACnE,GAAG,CAAC,UAAAsE,CAAC,EAAI;MAC1C,IAAIA,CAAC,KAAK,WAAW,EAAE;QACrB,OAAO3I,SAAS,CAAC0I,CAAC,CAACC,CAAC,CAAC,CAAC;MACxB,CAAC,MAAM;QACL,OAAOD,CAAC,CAACC,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EAAA,EAAC;AACL", "ignoreList": []}]}