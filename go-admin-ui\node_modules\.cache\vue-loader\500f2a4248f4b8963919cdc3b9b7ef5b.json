{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniArea\\index.vue?vue&type=style&index=0&id=b197d992&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniArea\\index.vue", "mtime": 1753924830051}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5hbnR2LWNoYXJ0LW1pbmkgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHdpZHRoOiAxMDAlOw0KDQogIC5jaGFydC13cmFwcGVyIHsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgYm90dG9tOiAtMjhweDsNCiAgICB3aWR0aDogMTAwJTsNCg0KLyogICAgbWFyZ2luOiAwIC01cHg7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsqLw0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniArea\\index.vue"], "names": [], "mappings": ";EAmDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEf,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/MiniArea/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"antv-chart-mini\">\r\n    <div class=\"chart-wrapper\" :style=\"{ height: 46 }\">\r\n      <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :padding=\"[36, 0, 18, 0]\">\r\n        <v-tooltip />\r\n        <v-smooth-area position=\"x*y\" />\r\n      </v-chart>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport moment from 'moment'\r\nconst data = []\r\nconst beginDay = new Date().getTime()\r\nfor (let i = 0; i < 10; i++) {\r\n  data.push({\r\n    x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\r\n    y: Math.round(Math.random() * 10)\r\n  })\r\n}\r\nconst tooltip = [\r\n  'x*y',\r\n  (x, y) => ({\r\n    name: x,\r\n    value: y\r\n  })\r\n]\r\nconst scale = [{\r\n  dataKey: 'x',\r\n  min: 2\r\n}, {\r\n  dataKey: 'y',\r\n  title: '时间',\r\n  min: 1,\r\n  max: 22\r\n}]\r\nexport default {\r\n  name: 'MiniArea',\r\n  data() {\r\n    return {\r\n      data,\r\n      tooltip,\r\n      scale,\r\n      height: 100\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .antv-chart-mini {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  .chart-wrapper {\r\n    position: absolute;\r\n    bottom: -28px;\r\n    width: 100%;\r\n\r\n/*    margin: 0 -5px;\r\n    overflow: hidden;*/\r\n  }\r\n}\r\n</style>\r\n"]}]}