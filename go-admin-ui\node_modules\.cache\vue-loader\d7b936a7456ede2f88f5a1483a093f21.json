{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue", "mtime": 1753924830413}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgVXBsb2FkRXhjZWxDb21wb25lbnQgZnJvbSAnQC9jb21wb25lbnRzL1VwbG9hZEV4Y2VsL2luZGV4LnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnVXBsb2FkRXhjZWwnLA0KICBjb21wb25lbnRzOiB7IFVwbG9hZEV4Y2VsQ29tcG9uZW50IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICB0YWJsZUhlYWRlcjogW10NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNMdDFNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMA0KDQogICAgICBpZiAoaXNMdDFNKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQoNCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAnUGxlYXNlIGRvIG5vdCB1cGxvYWQgZmlsZXMgbGFyZ2VyIHRoYW4gMW0gaW4gc2l6ZS4nLA0KICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgIH0pDQogICAgICByZXR1cm4gZmFsc2UNCiAgICB9LA0KICAgIGhhbmRsZVN1Y2Nlc3MoeyByZXN1bHRzLCBoZWFkZXIgfSkgew0KICAgICAgdGhpcy50YWJsZURhdGEgPSByZXN1bHRzDQogICAgICB0aGlzLnRhYmxlSGVhZGVyID0gaGVhZGVyDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue"], "names": [], "mappings": ";AAUA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEpE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;MAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/excel/upload-excel.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <upload-excel-component :on-success=\"handleSuccess\" :before-upload=\"beforeUpload\" />\r\n    <el-table :data=\"tableData\" border highlight-current-row style=\"width: 100%;margin-top:20px;\">\r\n      <el-table-column v-for=\"item of tableHeader\" :key=\"item\" :prop=\"item\" :label=\"item\" />\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UploadExcelComponent from '@/components/UploadExcel/index.vue'\r\n\r\nexport default {\r\n  name: 'UploadExcel',\r\n  components: { UploadExcelComponent },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      tableHeader: []\r\n    }\r\n  },\r\n  methods: {\r\n    beforeUpload(file) {\r\n      const isLt1M = file.size / 1024 / 1024 < 10\r\n\r\n      if (isLt1M) {\r\n        return true\r\n      }\r\n\r\n      this.$message({\r\n        message: 'Please do not upload files larger than 1m in size.',\r\n        type: 'warning'\r\n      })\r\n      return false\r\n    },\r\n    handleSuccess({ results, header }) {\r\n      this.tableData = results\r\n      this.tableHeader = header\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}