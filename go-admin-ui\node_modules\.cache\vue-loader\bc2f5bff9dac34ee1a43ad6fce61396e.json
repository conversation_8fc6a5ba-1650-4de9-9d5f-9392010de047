{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue?vue&type=template&id=b7b4de6a", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue", "mtime": 1753924830296}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/gen/basicInfoForm.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tableName\">\r\n          <span slot=\"label\">\r\n            数据表名称\r\n            <el-tooltip content=\"数据库表名称，针对gorm对应的table()使用，⚠️这里必须是蛇形结构\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.tableName\" placeholder=\"请输入表名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tableComment\">\r\n          <span slot=\"label\">\r\n            菜单名称\r\n            <el-tooltip content=\"同步的数据库表名称，生成配置数据时，用作菜单名称\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.tableComment\" placeholder=\"请输入菜单名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"className\">\r\n          <span slot=\"label\">\r\n            结构体模型名称\r\n            <el-tooltip content=\"结构体模型名称，代码中的struct名称定义使用\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.className\" placeholder=\"请输入\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"作者名称\" prop=\"functionAuthor\">\r\n          <el-input v-model=\"info.functionAuthor\" placeholder=\"请输入作者名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isLogicalDelete\">\r\n          <span slot=\"label\">\r\n            是否逻辑删除\r\n            <el-tooltip content=\"目前只支持逻辑删除\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-radio-group v-model=\"info.isLogicalDelete\">\r\n            <el-radio label=\"1\">是</el-radio>\r\n            <el-radio label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item v-if=\"info.isLogicalDelete == '1'\" label=\"逻辑删除字段\" prop=\"logicalDeleteColumn\">\r\n          <el-input v-model=\"info.logicalDeleteColumn\" placeholder=\"请输入\" />\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <el-col :span=\"24\">\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"info.remark\" type=\"textarea\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoForm',\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tableName: [\r\n          { required: true, message: '请输入表名称', trigger: 'blur' },\r\n          { pattern: /^[a-z\\._]*$/g, trigger: 'blur', message: '只允许小写字母,例如 sys_demo 格式' }\r\n        ],\r\n        tableComment: [\r\n          { required: true, message: '请输入菜单名称', trigger: 'blur' }\r\n        ],\r\n        className: [\r\n          { required: true, message: '请输入模型名称', trigger: 'blur' },\r\n          { pattern: /^[A-Z][A-z0-9]*$/g, trigger: 'blur', message: '必须以大写字母开头,例如 SysDemo 格式' }\r\n        ],\r\n        functionAuthor: [\r\n          { required: true, message: '请输入作者', trigger: 'blur' },\r\n          { pattern: /^[A-Za-z]+$/, trigger: 'blur', message: '校验规则:  只允许输入字母 a-z 或大写 A-Z' }\r\n        ]\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}