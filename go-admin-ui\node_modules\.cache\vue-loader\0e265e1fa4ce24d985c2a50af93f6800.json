{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue", "mtime": 1753924830024}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdDaGFydENhcmQnLA0KICBwcm9wczogew0KICAgIHRpdGxlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgdG90YWw6IHsNCiAgICAgIHR5cGU6IFtGdW5jdGlvbiwgTnVtYmVyLCBTdHJpbmddLA0KICAgICAgcmVxdWlyZWQ6IGZhbHNlLA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0sDQogICAgbG9hZGluZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue"], "names": [], "mappings": ";AAiCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/ChartCard/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card :loading=\"loading\" :body-style=\"{ padding: '20px 24px 8px' }\" :bordered=\"false\">\r\n    <div class=\"chart-card-header\">\r\n      <div class=\"meta\">\r\n        <span class=\"chart-card-title\">\r\n          <slot name=\"title\">\r\n            {{ title }}\r\n          </slot>\r\n        </span>\r\n        <span class=\"chart-card-action\">\r\n          <slot name=\"action\" />\r\n        </span>\r\n      </div>\r\n      <div class=\"total\">\r\n        <slot name=\"total\">\r\n          <span>{{ typeof total === 'function' && total() || total }}</span>\r\n        </slot>\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card-content\">\r\n      <div class=\"content-fix\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card-footer\">\r\n      <div class=\"field\">\r\n        <slot name=\"footer\" />\r\n      </div>\r\n    </div>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ChartCard',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    total: {\r\n      type: [Function, Number, String],\r\n      required: false,\r\n      default: null\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-card-header {\r\n    position: relative;\r\n    overflow: hidden;\r\n    width: 100%;\r\n    .meta {\r\n      position: relative;\r\n      overflow: hidden;\r\n      width: 100%;\r\n      color: rgba(0, 0, 0, .45);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n  }\r\n  .chart-card-action {\r\n    cursor: pointer;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n  }\r\n  .chart-card-footer {\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 9px;\r\n    margin-top: 8px;\r\n    > * {\r\n      position: relative;\r\n    }\r\n    .field {\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 0;\r\n      color: rgba(0,0,0,.65);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  .chart-card-content {\r\n    margin-bottom: 12px;\r\n    position: relative;\r\n    height: 46px;\r\n    width: 100%;\r\n    .content-fix {\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n    }\r\n  }\r\n  .total {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    word-break: break-all;\r\n    white-space: nowrap;\r\n    color: #000;\r\n    margin-top: 4px;\r\n    margin-bottom: 0;\r\n    font-size: 30px;\r\n    line-height: 38px;\r\n    height: 38px;\r\n  }\r\n</style>\r\n"]}]}