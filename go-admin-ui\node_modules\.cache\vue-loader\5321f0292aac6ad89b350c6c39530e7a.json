{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Bar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Bar.vue", "mtime": 1753924830020}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdCYXInLA0KICBwcm9wczogew0KICAgIHRpdGxlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0sDQogICAgbGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiB7fQ0KICAgIH0sDQogICAgc2NhbGU6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gew0KICAgICAgICByZXR1cm4gW3sNCiAgICAgICAgICBkYXRhS2V5OiAneCcsDQogICAgICAgICAgbWluOiAyDQogICAgICAgIH0sIHsNCiAgICAgICAgICBkYXRhS2V5OiAneScsDQogICAgICAgICAgdGl0bGU6ICfml7bpl7QnLA0KICAgICAgICAgIG1pbjogMSwNCiAgICAgICAgICBtYXg6IDIyDQogICAgICAgIH1dDQogICAgICB9DQogICAgfSwNCiAgICB0b29sdGlwOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IHsNCiAgICAgICAgcmV0dXJuIFsNCiAgICAgICAgICAneCp5JywNCiAgICAgICAgICAoeCwgeSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IHgsDQogICAgICAgICAgICB2YWx1ZTogeQ0KICAgICAgICAgIH0pDQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGFycjogW10NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgc2V0VGltZW91dChfID0+IHsNCiAgICAgIHRoaXMuYXJyID0gdGhpcy5saXN0DQogICAgfSwgMzAwKQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Bar.vue"], "names": [], "mappings": ";AAiBA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,EAAE;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,EAAE,CAAC;UACN,CAAC,CAAC,CAAC,EAAE,CAAC;QACR,CAAC;MACH;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC;QACH;MACF;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,EAAE,CAAC;IACR;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/Bar.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\r\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\r\n    <v-chart\r\n      height=\"254\"\r\n      :data=\"arr\"\r\n      :force-fit=\"true\"\r\n      :padding=\"['auto', 'auto', '40', '50']\"\r\n    >\r\n      <v-tooltip />\r\n      <v-axis />\r\n      <v-bar position=\"x*y\" />\r\n    </v-chart>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Bar',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    list: {\r\n      type: Array,\r\n      default: () => {}\r\n    },\r\n    scale: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{\r\n          dataKey: 'x',\r\n          min: 2\r\n        }, {\r\n          dataKey: 'y',\r\n          title: '时间',\r\n          min: 1,\r\n          max: 22\r\n        }]\r\n      }\r\n    },\r\n    tooltip: {\r\n      type: Array,\r\n      default: () => {\r\n        return [\r\n          'x*y',\r\n          (x, y) => ({\r\n            name: x,\r\n            value: y\r\n          })\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      arr: []\r\n    }\r\n  },\r\n  mounted() {\r\n    setTimeout(_ => {\r\n      this.arr = this.list\r\n    }, 300)\r\n  }\r\n}\r\n</script>\r\n"]}]}