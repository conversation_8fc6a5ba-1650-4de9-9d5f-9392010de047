{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue?vue&type=template&id=c1687078", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue", "mtime": 1753924830455}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC;cACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/userAvatar.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <img :src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" @click=\"editCropper()\">\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" :close-on-click-modal=\"false\">\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :auto-crop=\"options.autoCrop\"\r\n            :auto-crop-width=\"options.autoCropWidth\"\r\n            :auto-crop-height=\"options.autoCropHeight\"\r\n            :fixed-box=\"options.fixedBox\"\r\n            @realTime=\"realTime\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\">\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br>\r\n      <el-row>\r\n        <el-col :lg=\"2\" :md=\"2\">\r\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\r\n            <el-button size=\"small\">\r\n              上传\r\n              <i class=\"el-icon-upload el-icon--right\" />\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 2}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 2, offset: 6}\" :md=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from '@/store'\r\nimport { VueCropper } from 'vue-cropper'\r\nimport { uploadAvatar } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  props: {\r\n    // eslint-disable-next-line vue/require-default-prop\r\n    user: { type: Object }\r\n  },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 弹出层标题\r\n      title: '修改头像',\r\n      options: {\r\n        img: store.getters.avatar, // 裁剪图片的地址\r\n        autoCrop: true, // 是否默认生成截图框\r\n        autoCropWidth: 200, // 默认生成截图框宽度\r\n        autoCropHeight: 200, // 默认生成截图框高度\r\n        fixedBox: true // 固定截图框大小 不允许改变\r\n      },\r\n      previews: {}\r\n    }\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {\r\n    },\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft()\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight()\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1\r\n      this.$refs.cropper.changeScale(num)\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf('image/') === -1) {\r\n        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')\r\n      } else {\r\n        const reader = new FileReader()\r\n        reader.readAsDataURL(file)\r\n        reader.onload = () => {\r\n          this.options.img = reader.result\r\n        }\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob(data => {\r\n        const formData = new FormData()\r\n        formData.append('upload[]', data)\r\n        uploadAvatar(formData).then(response => {\r\n          if (response.code === 200) {\r\n            this.open = false\r\n            this.options.img = process.env.VUE_APP_BASE_API + response.data\r\n            this.msgSuccess(response.msg)\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n          this.$refs.cropper.clearCrop()\r\n        })\r\n      })\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}