{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\IconSelect\\index.vue", "mtime": 1753924830043}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5maWx0ZXIuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIjsKaW1wb3J0IGljb25zIGZyb20gJy4vcmVxdWlyZUljb25zJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdJY29uU2VsZWN0JywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbmFtZTogJycsCiAgICAgIGljb25MaXN0OiBpY29ucwogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGZpbHRlckljb25zOiBmdW5jdGlvbiBmaWx0ZXJJY29ucygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgaWYgKHRoaXMubmFtZSkgewogICAgICAgIHRoaXMuaWNvbkxpc3QgPSB0aGlzLmljb25MaXN0LmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0uaW5jbHVkZXMoX3RoaXMubmFtZSk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pY29uTGlzdCA9IGljb25zOwogICAgICB9CiAgICB9LAogICAgc2VsZWN0ZWRJY29uOiBmdW5jdGlvbiBzZWxlY3RlZEljb24obmFtZSkgewogICAgICB0aGlzLiRlbWl0KCdzZWxlY3RlZCcsIG5hbWUpOwogICAgICBkb2N1bWVudC5ib2R5LmNsaWNrKCk7CiAgICB9LAogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLm5hbWUgPSAnJzsKICAgICAgdGhpcy5pY29uTGlzdCA9IGljb25zOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["icons", "name", "data", "iconList", "methods", "filterIcons", "_this", "filter", "item", "includes", "selectedIcon", "$emit", "document", "body", "click", "reset"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\IconSelect\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"icon-body\">\r\n    <el-input v-model=\"name\" style=\"position: relative;\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input.native=\"filterIcons\">\r\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\r\n    </el-input>\r\n    <div class=\"icon-list\">\r\n      <div v-for=\"(item, index) in iconList\" :key=\"index\" @click=\"selectedIcon(item)\">\r\n        <svg-icon :icon-class=\"item\" style=\"height: 30px;width: 16px;\" />\r\n        <span>{{ item }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport icons from './requireIcons'\r\nexport default {\r\n  name: 'IconSelect',\r\n  data() {\r\n    return {\r\n      name: '',\r\n      iconList: icons\r\n    }\r\n  },\r\n  methods: {\r\n    filterIcons() {\r\n      if (this.name) {\r\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\r\n      } else {\r\n        this.iconList = icons\r\n      }\r\n    },\r\n    selectedIcon(name) {\r\n      this.$emit('selected', name)\r\n      document.body.click()\r\n    },\r\n    reset() {\r\n      this.name = ''\r\n      this.iconList = icons\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n  .icon-body {\r\n    width: 100%;\r\n    padding: 10px;\r\n    .icon-list {\r\n      height: 200px;\r\n      overflow-y: scroll;\r\n      div {\r\n        height: 30px;\r\n        line-height: 30px;\r\n        margin-bottom: -5px;\r\n        cursor: pointer;\r\n        width: 33%;\r\n        float: left;\r\n      }\r\n      span {\r\n        display: inline-block;\r\n        vertical-align: -0.15em;\r\n        fill: currentColor;\r\n        overflow: hidden;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;AAeA,OAAOA,KAAI,MAAO,gBAAe;AACjC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLD,IAAI,EAAE,EAAE;MACRE,QAAQ,EAAEH;IACZ;EACF,CAAC;EACDI,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACZ,IAAI,IAAI,CAACL,IAAI,EAAE;QACb,IAAI,CAACE,QAAO,GAAI,IAAI,CAACA,QAAQ,CAACI,MAAM,CAAC,UAAAC,IAAG;UAAA,OAAKA,IAAI,CAACC,QAAQ,CAACH,KAAI,CAACL,IAAI,CAAC;QAAA;MACvE,OAAO;QACL,IAAI,CAACE,QAAO,GAAIH,KAAI;MACtB;IACF,CAAC;IACDU,YAAY,WAAZA,YAAYA,CAACT,IAAI,EAAE;MACjB,IAAI,CAACU,KAAK,CAAC,UAAU,EAAEV,IAAI;MAC3BW,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC;IACtB,CAAC;IACDC,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACd,IAAG,GAAI,EAAC;MACb,IAAI,CAACE,QAAO,GAAIH,KAAI;IACtB;EACF;AACF", "ignoreList": []}]}