{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-login-log\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-login-log\\index.vue", "mtime": 1753924830275}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5tYXAuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0IHsgZGVsU3lzTG9naW5sb2csIGdldFN5c0xvZ2lubG9nLCBsaXN0U3lzTG9naW5sb2cgfSBmcm9tICdAL2FwaS9hZG1pbi9zeXMtbG9naW4tbG9nJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTeXNMb2dpbkxvZ01hbmFnZScsCiAgY29tcG9uZW50czoge30sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogJycsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgaXNFZGl0OiBmYWxzZSwKICAgICAgZmlsZU9wZW46IGZhbHNlLAogICAgICBmaWxlSW5kZXg6IHVuZGVmaW5lZCwKICAgICAgLy8g57G75Z6L5pWw5o2u5a2X5YW4CiAgICAgIHR5cGVPcHRpb25zOiBbXSwKICAgICAgc3lzbG9naW5sb2dMaXN0OiBbXSwKICAgICAgc3RhdHVzT3B0aW9uczogW10sCiAgICAgIC8vIOWFs+ezu+ihqOexu+WeiwoKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZUluZGV4OiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB1c2VybmFtZTogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkLAogICAgICAgIGlwYWRkcjogdW5kZWZpbmVkLAogICAgICAgIGxvZ2luTG9jYXRpb246IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVkQXRPcmRlcjogJ2Rlc2MnCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7fQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLmdldERpY3RzKCdzeXNfY29tbW9uX3N0YXR1cycpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgIF90aGlzLnN0YXR1c09wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5Y+C5pWw5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RTeXNMb2dpbmxvZyh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMyLnN5c2xvZ2lubG9nTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdDsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS5kYXRhLmNvdW50OwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBJRDogdW5kZWZpbmVkLAogICAgICAgIHVzZXJuYW1lOiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsCiAgICAgICAgaXBhZGRyOiB1bmRlZmluZWQsCiAgICAgICAgbG9naW5Mb2NhdGlvbjogdW5kZWZpbmVkLAogICAgICAgIGJyb3dzZXI6IHVuZGVmaW5lZCwKICAgICAgICBvczogdW5kZWZpbmVkLAogICAgICAgIHBsYXRmb3JtOiB1bmRlZmluZWQsCiAgICAgICAgbG9naW5UaW1lOiB1bmRlZmluZWQsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsCiAgICAgICAgbXNnOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKTsKICAgIH0sCiAgICBnZXRJbWdMaXN0OiBmdW5jdGlvbiBnZXRJbWdMaXN0KCkgewogICAgICB0aGlzLmZvcm1bdGhpcy5maWxlSW5kZXhdID0gdGhpcy4kcmVmc1snZmlsZUNob29zZSddLnJlc3VsdExpc3RbMF0uZnVsbFVybDsKICAgIH0sCiAgICBmaWxlQ2xvc2U6IGZ1bmN0aW9uIGZpbGVDbG9zZSgpIHsKICAgICAgdGhpcy5maWxlT3BlbiA9IGZhbHNlOwogICAgfSwKICAgIHN0YXR1c0Zvcm1hdDogZnVuY3Rpb24gc3RhdHVzRm9ybWF0KHJvdykgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5zdGF0dXNPcHRpb25zLCByb3cuc3RhdHVzKTsKICAgIH0sCiAgICAvLyDlhbPns7sKICAgIC8vIOaWh+S7tgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VJbmRleCA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgncXVlcnlGb3JtJyk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovaGFuZGxlQWRkOiBmdW5jdGlvbiBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICfmt7vliqDns7vnu5/nmbvlvZXml6Xlv5cnOwogICAgICB0aGlzLmlzRWRpdCA9IGZhbHNlOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqL2hhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgSUQgPSByb3cuaWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldFN5c0xvZ2lubG9nKElEKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBfdGhpczMub3BlbiA9IHRydWU7CiAgICAgICAgX3RoaXMzLnRpdGxlID0gJ+S/ruaUueezu+e7n+eZu+W9leaXpeW/lyc7CiAgICAgICAgX3RoaXMzLmlzRWRpdCA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHZhciBJZHMgPSByb3cuaWQgJiYgW3Jvdy5pZF0gfHwgdGhpcy5pZHM7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOe8luWPt+S4uiInICsgSWRzICsgJyLnmoTmlbDmja7pobk/JywgJ+itpuWRiicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBkZWxTeXNMb2dpbmxvZyh7CiAgICAgICAgICAnaWRzJzogSWRzCiAgICAgICAgfSk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgX3RoaXM0Lm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICAgIF90aGlzNC5vcGVuID0gZmFsc2U7CiAgICAgICAgICBfdGhpczQuZ2V0TGlzdCgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczQubXNnRXJyb3IocmVzcG9uc2UubXNnKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["delSysLoginlog", "getSysLoginlog", "listSysLoginlog", "name", "components", "data", "loading", "ids", "single", "multiple", "total", "title", "open", "isEdit", "fileOpen", "fileIndex", "undefined", "typeOptions", "sysloginlogList", "statusOptions", "queryParams", "pageIndex", "pageSize", "username", "status", "ipaddr", "loginLocation", "createdAtOrder", "form", "rules", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "date<PERSON><PERSON><PERSON>", "list", "count", "cancel", "reset", "ID", "browser", "os", "platform", "loginTime", "remark", "msg", "resetForm", "getImgList", "$refs", "resultList", "fullUrl", "fileClose", "statusFormat", "row", "selectDictLabel", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleUpdate", "_this3", "handleDelete", "_this4", "Ids", "$confirm", "confirmButtonText", "cancelButtonText", "type", "code", "msgSuccess", "msgError", "catch"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-login-log\\index.vue"], "sourcesContent": ["\r\n<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-width=\"68px\">\r\n          <el-form-item label=\"用户名\" prop=\"username\"><el-input\r\n            v-model=\"queryParams.username\"\r\n            placeholder=\"请输入用户名\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\"><el-select\r\n            v-model=\"queryParams.status\"\r\n            placeholder=\"系统登录日志状态\"\r\n            clearable\r\n            size=\"small\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in statusOptions\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"ip地址\" prop=\"ipaddr\"><el-input\r\n            v-model=\"queryParams.ipaddr\"\r\n            placeholder=\"请输入ip地址\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysLoginLog:remove']\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"sysloginlogList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"用户名\"\r\n            align=\"center\"\r\n            prop=\"username\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column\r\n            label=\"类型\"\r\n            align=\"center\"\r\n            prop=\"msg\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column\r\n            label=\"状态\"\r\n            align=\"center\"\r\n            prop=\"status\"\r\n            :formatter=\"statusFormat\"\r\n            width=\"100\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ statusFormat(scope.row) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"ip地址\"\r\n            align=\"center\"\r\n            prop=\"ipaddr\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-popover trigger=\"hover\" placement=\"top\">\r\n                <p>IP: {{ scope.row.ipaddr }}</p>\r\n                <p>归属地: {{ scope.row.loginLocation }}</p>\r\n                <p>浏览器: {{ scope.row.browser }}</p>\r\n                <p>系统: {{ scope.row.os }}</p>\r\n                <p>固件: {{ scope.row.platform }}</p>\r\n                <div slot=\"reference\" class=\"name-wrapper\">\r\n                  {{ scope.row.ipaddr }}\r\n                </div>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column\r\n            label=\"登录时间\"\r\n            align=\"center\"\r\n            prop=\"loginTime\"\r\n            width=\"180\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.loginTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysLoginLog:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { delSysLoginlog, getSysLoginlog, listSysLoginlog } from '@/api/admin/sys-login-log'\r\n\r\nexport default {\r\n  name: 'SysLoginLogManage',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      isEdit: false,\r\n      fileOpen: false,\r\n      fileIndex: undefined,\r\n      // 类型数据字典\r\n      typeOptions: [],\r\n      sysloginlogList: [],\r\n      statusOptions: [],\r\n      // 关系表类型\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        username: undefined,\r\n        status: undefined,\r\n        ipaddr: undefined,\r\n        loginLocation: undefined,\r\n        createdAtOrder: 'desc'\r\n      },\r\n      // 表单参数\r\n      form: {\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getDicts('sys_common_status').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询参数列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listSysLoginlog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.sysloginlogList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        ID: undefined,\r\n        username: undefined,\r\n        status: undefined,\r\n        ipaddr: undefined,\r\n        loginLocation: undefined,\r\n        browser: undefined,\r\n        os: undefined,\r\n        platform: undefined,\r\n        loginTime: undefined,\r\n        remark: undefined,\r\n        msg: undefined\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    getImgList: function() {\r\n      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl\r\n    },\r\n    fileClose: function() {\r\n      this.fileOpen = false\r\n    },\r\n    statusFormat(row) {\r\n      return this.selectDictLabel(this.statusOptions, row.status)\r\n    },\r\n    // 关系\r\n    // 文件\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加系统登录日志'\r\n      this.isEdit = false\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const ID =\r\n                row.id || this.ids\r\n      getSysLoginlog(ID).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改系统登录日志'\r\n        this.isEdit = true\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      var Ids = (row.id && [row.id]) || this.ids\r\n\r\n      this.$confirm('是否确认删除编号为\"' + Ids + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delSysLoginlog({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;AA0IA,SAASA,cAAc,EAAEC,cAAc,EAAEC,eAAc,QAAS,2BAA0B;AAE1F,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,GAAG,EAAE,EAAE;MACP;MACAC,MAAM,EAAE,IAAI;MACZ;MACAC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,CAAC;MACR;MACAC,KAAK,EAAE,EAAE;MACT;MACAC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAEC,SAAS;MACpB;MACAC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjB;;MAEA;MACAC,WAAW,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAEP,SAAS;QACnBQ,MAAM,EAAER,SAAS;QACjBS,MAAM,EAAET,SAAS;QACjBU,aAAa,EAAEV,SAAS;QACxBW,cAAc,EAAE;MAClB,CAAC;MACD;MACAC,IAAI,EAAE,CACN,CAAC;MACD;MACAC,KAAK,EAAE,CACP;IACF;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,OAAO,CAAC;IACb,IAAI,CAACC,QAAQ,CAAC,mBAAmB,CAAC,CAACC,IAAI,CAAC,UAAAC,QAAO,EAAK;MAClDJ,KAAI,CAACZ,aAAY,GAAIgB,QAAQ,CAAC9B,IAAG;IACnC,CAAC;EACH,CAAC;EACD+B,OAAO,EAAE;IACP,aACAJ,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAK,MAAA;MACR,IAAI,CAAC/B,OAAM,GAAI,IAAG;MAClBJ,eAAe,CAAC,IAAI,CAACoC,YAAY,CAAC,IAAI,CAAClB,WAAW,EAAE,IAAI,CAACmB,SAAS,CAAC,CAAC,CAACL,IAAI,CAAC,UAAAC,QAAO,EAAK;QACpFE,MAAI,CAACnB,eAAc,GAAIiB,QAAQ,CAAC9B,IAAI,CAACmC,IAAG;QACxCH,MAAI,CAAC3B,KAAI,GAAIyB,QAAQ,CAAC9B,IAAI,CAACoC,KAAI;QAC/BJ,MAAI,CAAC/B,OAAM,GAAI,KAAI;MACrB,CACA;IACF,CAAC;IACD;IACAoC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC9B,IAAG,GAAI,KAAI;MAChB,IAAI,CAAC+B,KAAK,CAAC;IACb,CAAC;IACD;IACAA,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACf,IAAG,GAAI;QACVgB,EAAE,EAAE5B,SAAS;QACbO,QAAQ,EAAEP,SAAS;QACnBQ,MAAM,EAAER,SAAS;QACjBS,MAAM,EAAET,SAAS;QACjBU,aAAa,EAAEV,SAAS;QACxB6B,OAAO,EAAE7B,SAAS;QAClB8B,EAAE,EAAE9B,SAAS;QACb+B,QAAQ,EAAE/B,SAAS;QACnBgC,SAAS,EAAEhC,SAAS;QACpBiC,MAAM,EAAEjC,SAAS;QACjBkC,GAAG,EAAElC;MACP;MACA,IAAI,CAACmC,SAAS,CAAC,MAAM;IACvB,CAAC;IACDC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAa;MACrB,IAAI,CAACxB,IAAI,CAAC,IAAI,CAACb,SAAS,IAAI,IAAI,CAACsC,KAAK,CAAC,YAAY,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,OAAM;IAC3E,CAAC;IACDC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAa;MACpB,IAAI,CAAC1C,QAAO,GAAI,KAAI;IACtB,CAAC;IACD2C,YAAY,WAAZA,YAAYA,CAACC,GAAG,EAAE;MAChB,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACxC,aAAa,EAAEuC,GAAG,CAAClC,MAAM;IAC5D,CAAC;IACD;IACA;IACA;IACAoC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACxC,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAACW,OAAO,CAAC;IACf,CAAC;IACD,aACA6B,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAACtB,SAAQ,GAAI,EAAC;MAClB,IAAI,CAACY,SAAS,CAAC,WAAW;MAC1B,IAAI,CAACS,WAAW,CAAC;IACnB,CAAC;IACD,aACAE,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACnB,KAAK,CAAC;MACX,IAAI,CAAC/B,IAAG,GAAI,IAAG;MACf,IAAI,CAACD,KAAI,GAAI,UAAS;MACtB,IAAI,CAACE,MAAK,GAAI,KAAI;IACpB,CAAC;IACD;IACAkD,qBAAqB,WAArBA,qBAAqBA,CAACC,SAAS,EAAE;MAC/B,IAAI,CAACzD,GAAE,GAAIyD,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACC,EAAE;MAAA;MACxC,IAAI,CAAC3D,MAAK,GAAIwD,SAAS,CAACI,MAAK,KAAM;MACnC,IAAI,CAAC3D,QAAO,GAAI,CAACuD,SAAS,CAACI,MAAK;IAClC,CAAC;IACD,aACAC,YAAY,WAAZA,YAAYA,CAACX,GAAG,EAAE;MAAA,IAAAY,MAAA;MAChB,IAAI,CAAC3B,KAAK,CAAC;MACX,IAAMC,EAAC,GACGc,GAAG,CAACS,EAAC,IAAK,IAAI,CAAC5D,GAAE;MAC3BN,cAAc,CAAC2C,EAAE,CAAC,CAACV,IAAI,CAAC,UAAAC,QAAO,EAAK;QAClCmC,MAAI,CAAC1C,IAAG,GAAIO,QAAQ,CAAC9B,IAAG;QACxBiE,MAAI,CAAC1D,IAAG,GAAI,IAAG;QACf0D,MAAI,CAAC3D,KAAI,GAAI,UAAS;QACtB2D,MAAI,CAACzD,MAAK,GAAI,IAAG;MACnB,CAAC;IACH,CAAC;IACD,aACA0D,YAAY,WAAZA,YAAYA,CAACb,GAAG,EAAE;MAAA,IAAAc,MAAA;MAChB,IAAIC,GAAE,GAAKf,GAAG,CAACS,EAAC,IAAK,CAACT,GAAG,CAACS,EAAE,CAAC,IAAK,IAAI,CAAC5D,GAAE;MAEzC,IAAI,CAACmE,QAAQ,CAAC,YAAW,GAAID,GAAE,GAAI,QAAQ,EAAE,IAAI,EAAE;QACjDE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC3C,IAAI,CAAC,YAAW;QACjB,OAAOlC,cAAc,CAAC;UAAE,KAAK,EAAEyE;QAAI,CAAC;MACtC,CAAC,CAAC,CAACvC,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAAC2C,IAAG,KAAM,GAAG,EAAE;UACzBN,MAAI,CAACO,UAAU,CAAC5C,QAAQ,CAACe,GAAG;UAC5BsB,MAAI,CAAC5D,IAAG,GAAI,KAAI;UAChB4D,MAAI,CAACxC,OAAO,CAAC;QACf,OAAO;UACLwC,MAAI,CAACQ,QAAQ,CAAC7C,QAAQ,CAACe,GAAG;QAC5B;MACF,CAAC,CAAC,CAAC+B,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB;EACF;AACF", "ignoreList": []}]}