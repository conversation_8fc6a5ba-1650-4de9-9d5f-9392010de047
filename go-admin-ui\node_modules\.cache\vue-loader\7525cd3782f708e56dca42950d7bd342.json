{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Activity.vue?vue&type=style&index=0&id=7e91c778&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Activity.vue", "mtime": 1753924830443}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoudXNlci1hY3Rpdml0eSB7DQogIC51c2VyLWJsb2NrIHsNCg0KICAgIC51c2VybmFtZSwNCiAgICAuZGVzY3JpcHRpb24gew0KICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICBtYXJnaW4tbGVmdDogNTBweDsNCiAgICAgIHBhZGRpbmc6IDJweCAwOw0KICAgIH0NCg0KICAgIC51c2VybmFtZXsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGNvbG9yOiAjMDAwOw0KICAgIH0NCg0KICAgIDphZnRlciB7DQogICAgICBjbGVhcjogYm90aDsNCiAgICB9DQoNCiAgICAuaW1nLWNpcmNsZSB7DQogICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICB3aWR0aDogNDBweDsNCiAgICAgIGhlaWdodDogNDBweDsNCiAgICAgIGZsb2F0OiBsZWZ0Ow0KICAgIH0NCg0KICAgIHNwYW4gew0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICB9DQogIH0NCg0KICAucG9zdCB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZDJkNmRlOw0KICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogICAgcGFkZGluZy1ib3R0b206IDE1cHg7DQogICAgY29sb3I6ICM2NjY7DQoNCiAgICAuaW1hZ2Ugew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDEwMCU7DQoNCiAgICB9DQoNCiAgICAudXNlci1pbWFnZXMgew0KICAgICAgcGFkZGluZy10b3A6IDIwcHg7DQogICAgfQ0KICB9DQoNCiAgLmxpc3QtaW5saW5lIHsNCiAgICBwYWRkaW5nLWxlZnQ6IDA7DQogICAgbWFyZ2luLWxlZnQ6IC01cHg7DQogICAgbGlzdC1zdHlsZTogbm9uZTsNCg0KICAgIGxpIHsNCiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgIHBhZGRpbmctcmlnaHQ6IDVweDsNCiAgICAgIHBhZGRpbmctbGVmdDogNXB4Ow0KICAgICAgZm9udC1zaXplOiAxM3B4Ow0KICAgIH0NCg0KICAgIC5saW5rLWJsYWNrIHsNCg0KICAgICAgJjpob3ZlciwNCiAgICAgICY6Zm9jdXMgew0KICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KfQ0KDQouYm94LWNlbnRlciB7DQogIG1hcmdpbjogMCBhdXRvOw0KICBkaXNwbGF5OiB0YWJsZTsNCn0NCg0KLnRleHQtbXV0ZWQgew0KICBjb2xvcjogIzc3NzsNCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Activity.vue"], "names": [], "mappings": ";AAuGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAChB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;;IAEA,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,EAAE;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb;IACF;EACF;;AAEF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/Activity.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"user-activity\">\r\n    <div class=\"post\">\r\n      <div class=\"user-block\">\r\n        <img class=\"img-circle\" :src=\"'https://wpimg.wallstcn.com/57ed425a-c71e-4201-9428-68760c0537c4.jpg'+avatarPrefix\">\r\n        <span class=\"username text-muted\">Iron Man</span>\r\n        <span class=\"description\">Shared publicly - 7:30 PM today</span>\r\n      </div>\r\n      <p>\r\n        Lorem ipsum represents a long-held tradition for designers,\r\n        typographers and the like. Some people hate it and argue for\r\n        its demise, but others ignore the hate as they create awesome\r\n        tools to help create filler text for everyone from bacon lovers\r\n        to Charlie Sheen fans.\r\n      </p>\r\n      <ul class=\"list-inline\">\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <i class=\"el-icon-share\" />\r\n            Share\r\n          </span>\r\n        </li>\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <svg-icon icon-class=\"like\" />\r\n            Like\r\n          </span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"post\">\r\n      <div class=\"user-block\">\r\n        <img class=\"img-circle\" :src=\"'https://wpimg.wallstcn.com/9e2a5d0a-bd5b-457f-ac8e-86554616c87b.jpg'+avatarPrefix\">\r\n        <span class=\"username text-muted\">Captain American</span>\r\n        <span class=\"description\">Sent you a message - yesterday</span>\r\n      </div>\r\n      <p>\r\n        Lorem ipsum represents a long-held tradition for designers,\r\n        typographers and the like. Some people hate it and argue for\r\n        its demise, but others ignore the hate as they create awesome\r\n        tools to help create filler text for everyone from bacon lovers\r\n        to Charlie Sheen fans.\r\n      </p>\r\n      <ul class=\"list-inline\">\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <i class=\"el-icon-share\" />\r\n            Share\r\n          </span>\r\n        </li>\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <svg-icon icon-class=\"like\" />\r\n            Like\r\n          </span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"post\">\r\n      <div class=\"user-block\">\r\n        <img class=\"img-circle\" :src=\"'https://wpimg.wallstcn.com/fb57f689-e1ab-443c-af12-8d4066e202e2.jpg'+avatarPrefix\">\r\n        <span class=\"username\">Spider Man</span>\r\n        <span class=\"description\">Posted 4 photos - 2 days ago</span>\r\n      </div>\r\n      <div class=\"user-images\">\r\n        <el-carousel :interval=\"6000\" type=\"card\" height=\"220px\">\r\n          <el-carousel-item v-for=\"item in carouselImages\" :key=\"item\">\r\n            <img :src=\"item+carouselPrefix\" class=\"image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n      <ul class=\"list-inline\">\r\n        <li><span class=\"link-black text-sm\"><i class=\"el-icon-share\" /> Share</span></li>\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <svg-icon icon-class=\"like\" /> Like</span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nconst avatarPrefix = '?imageView2/1/w/80/h/80'\r\nconst carouselPrefix = '?imageView2/2/h/440'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      carouselImages: [\r\n        'https://wpimg.wallstcn.com/9679ffb0-9e0b-4451-9916-e21992218054.jpg',\r\n        'https://wpimg.wallstcn.com/bcce3734-0837-4b9f-9261-351ef384f75a.jpg',\r\n        'https://wpimg.wallstcn.com/d1d7b033-d75e-4cd6-ae39-fcd5f1c0a7c5.jpg',\r\n        'https://wpimg.wallstcn.com/50530061-851b-4ca5-9dc5-2fead928a939.jpg'\r\n      ],\r\n      avatarPrefix,\r\n      carouselPrefix\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user-activity {\r\n  .user-block {\r\n\r\n    .username,\r\n    .description {\r\n      display: block;\r\n      margin-left: 50px;\r\n      padding: 2px 0;\r\n    }\r\n\r\n    .username{\r\n      font-size: 16px;\r\n      color: #000;\r\n    }\r\n\r\n    :after {\r\n      clear: both;\r\n    }\r\n\r\n    .img-circle {\r\n      border-radius: 50%;\r\n      width: 40px;\r\n      height: 40px;\r\n      float: left;\r\n    }\r\n\r\n    span {\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n\r\n  .post {\r\n    font-size: 14px;\r\n    border-bottom: 1px solid #d2d6de;\r\n    margin-bottom: 15px;\r\n    padding-bottom: 15px;\r\n    color: #666;\r\n\r\n    .image {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n    }\r\n\r\n    .user-images {\r\n      padding-top: 20px;\r\n    }\r\n  }\r\n\r\n  .list-inline {\r\n    padding-left: 0;\r\n    margin-left: -5px;\r\n    list-style: none;\r\n\r\n    li {\r\n      display: inline-block;\r\n      padding-right: 5px;\r\n      padding-left: 5px;\r\n      font-size: 13px;\r\n    }\r\n\r\n    .link-black {\r\n\r\n      &:hover,\r\n      &:focus {\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n\r\n.box-center {\r\n  margin: 0 auto;\r\n  display: table;\r\n}\r\n\r\n.text-muted {\r\n  color: #777;\r\n}\r\n</style>\r\n"]}]}