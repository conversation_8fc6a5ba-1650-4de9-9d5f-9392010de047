{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\settings.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\settings.js", "mtime": 1753924830228}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHZhcmlhYmxlcyBmcm9tICdAL3N0eWxlcy9lbGVtZW50LXZhcmlhYmxlcy5zY3NzJzsKaW1wb3J0IGRlZmF1bHRTZXR0aW5ncyBmcm9tICdAL3NldHRpbmdzJzsKdmFyIHNob3dTZXR0aW5ncyA9IGRlZmF1bHRTZXR0aW5ncy5zaG93U2V0dGluZ3MsCiAgdG9wTmF2ID0gZGVmYXVsdFNldHRpbmdzLnRvcE5hdiwKICB0YWdzVmlldyA9IGRlZmF1bHRTZXR0aW5ncy50YWdzVmlldywKICBmaXhlZEhlYWRlciA9IGRlZmF1bHRTZXR0aW5ncy5maXhlZEhlYWRlciwKICBzaWRlYmFyTG9nbyA9IGRlZmF1bHRTZXR0aW5ncy5zaWRlYmFyTG9nbywKICB0aGVtZVN0eWxlID0gZGVmYXVsdFNldHRpbmdzLnRoZW1lU3R5bGU7CnZhciBzdGF0ZSA9IHsKICB0aGVtZTogdmFyaWFibGVzLnRoZW1lLAogIHNob3dTZXR0aW5nczogc2hvd1NldHRpbmdzLAogIHRvcE5hdjogdG9wTmF2LAogIHRhZ3NWaWV3OiB0YWdzVmlldywKICBmaXhlZEhlYWRlcjogZml4ZWRIZWFkZXIsCiAgc2lkZWJhckxvZ286IHNpZGViYXJMb2dvLAogIHRoZW1lU3R5bGU6IHRoZW1lU3R5bGUKfTsKdmFyIG11dGF0aW9ucyA9IHsKICBDSEFOR0VfU0VUVElORzogZnVuY3Rpb24gQ0hBTkdFX1NFVFRJTkcoc3RhdGUsIF9yZWYpIHsKICAgIHZhciBrZXkgPSBfcmVmLmtleSwKICAgICAgdmFsdWUgPSBfcmVmLnZhbHVlOwogICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXByb3RvdHlwZS1idWlsdGlucwogICAgaWYgKHN0YXRlLmhhc093blByb3BlcnR5KGtleSkpIHsKICAgICAgc3RhdGVba2V5XSA9IHZhbHVlOwogICAgfQogIH0KfTsKdmFyIGFjdGlvbnMgPSB7CiAgY2hhbmdlU2V0dGluZzogZnVuY3Rpb24gY2hhbmdlU2V0dGluZyhfcmVmMiwgZGF0YSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWYyLmNvbW1pdDsKICAgIGNvbW1pdCgnQ0hBTkdFX1NFVFRJTkcnLCBkYXRhKTsKICB9Cn07CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lc3BhY2VkOiB0cnVlLAogIHN0YXRlOiBzdGF0ZSwKICBtdXRhdGlvbnM6IG11dGF0aW9ucywKICBhY3Rpb25zOiBhY3Rpb25zCn07"}, {"version": 3, "names": ["variables", "defaultSettings", "showSettings", "topNav", "tagsView", "fixedHeader", "sidebarLogo", "themeStyle", "state", "theme", "mutations", "CHANGE_SETTING", "_ref", "key", "value", "hasOwnProperty", "actions", "changeSetting", "_ref2", "data", "commit", "namespaced"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/modules/settings.js"], "sourcesContent": ["import variables from '@/styles/element-variables.scss'\r\nimport defaultSettings from '@/settings'\r\n\r\nconst { showSettings, topNav, tagsView, fixedHeader, sidebarLogo, themeStyle } = defaultSettings\r\n\r\nconst state = {\r\n  theme: variables.theme,\r\n  showSettings: showSettings,\r\n  topNav: topNav,\r\n  tagsView: tagsView,\r\n  fixedHeader: fixedHeader,\r\n  sidebarLogo: sidebarLogo,\r\n  themeStyle: themeStyle\r\n}\r\n\r\nconst mutations = {\r\n  CHANGE_SETTING: (state, { key, value }) => {\r\n    // eslint-disable-next-line no-prototype-builtins\r\n    if (state.hasOwnProperty(key)) {\r\n      state[key] = value\r\n    }\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  changeSetting({ commit }, data) {\r\n    commit('CHANGE_SETTING', data)\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iCAAiC;AACvD,OAAOC,eAAe,MAAM,YAAY;AAExC,IAAQC,YAAY,GAA6DD,eAAe,CAAxFC,YAAY;EAAEC,MAAM,GAAqDF,eAAe,CAA1EE,MAAM;EAAEC,QAAQ,GAA2CH,eAAe,CAAlEG,QAAQ;EAAEC,WAAW,GAA8BJ,eAAe,CAAxDI,WAAW;EAAEC,WAAW,GAAiBL,eAAe,CAA3CK,WAAW;EAAEC,UAAU,GAAKN,eAAe,CAA9BM,UAAU;AAE5E,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAET,SAAS,CAACS,KAAK;EACtBP,YAAY,EAAEA,YAAY;EAC1BC,MAAM,EAAEA,MAAM;EACdC,QAAQ,EAAEA,QAAQ;EAClBC,WAAW,EAAEA,WAAW;EACxBC,WAAW,EAAEA,WAAW;EACxBC,UAAU,EAAEA;AACd,CAAC;AAED,IAAMG,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAGH,KAAK,EAAAI,IAAA,EAAqB;IAAA,IAAjBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAClC;IACA,IAAIN,KAAK,CAACO,cAAc,CAACF,GAAG,CAAC,EAAE;MAC7BL,KAAK,CAACK,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,KAAA,CAANE,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC;AACF,CAAC;AAED,eAAe;EACbE,UAAU,EAAE,IAAI;EAChBb,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}