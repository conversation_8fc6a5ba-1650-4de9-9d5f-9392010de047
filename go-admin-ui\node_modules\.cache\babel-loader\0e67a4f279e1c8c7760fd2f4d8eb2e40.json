{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\SvgIcon\\index.vue", "mtime": 1753924830061}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gZG9jOiBodHRwczovL3BhbmppYWNoZW4uZ2l0aHViLmlvL3Z1ZS1lbGVtZW50LWFkbWluLXNpdGUvZmVhdHVyZS9jb21wb25lbnQvc3ZnLWljb24uaHRtbCN1c2FnZQppbXBvcnQgeyBpc0V4dGVybmFsIGFzIF9pc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU3ZnSWNvbicsCiAgcHJvcHM6IHsKICAgIGljb25DbGFzczogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gX2lzRXh0ZXJuYWwodGhpcy5pY29uQ2xhc3MpOwogICAgfSwKICAgIGljb25OYW1lOiBmdW5jdGlvbiBpY29uTmFtZSgpIHsKICAgICAgcmV0dXJuICIjaWNvbi0iLmNvbmNhdCh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgc3ZnQ2xhc3M6IGZ1bmN0aW9uIHN2Z0NsYXNzKCkgewogICAgICBpZiAodGhpcy5jbGFzc05hbWUpIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uICcgKyB0aGlzLmNsYXNzTmFtZTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uJzsKICAgICAgfQogICAgfSwKICAgIHN0eWxlRXh0ZXJuYWxJY29uOiBmdW5jdGlvbiBzdHlsZUV4dGVybmFsSWNvbigpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBtYXNrOiAidXJsKCIuY29uY2F0KHRoaXMuaWNvbkNsYXNzLCAiKSBuby1yZXBlYXQgNTAlIDUwJSIpLAogICAgICAgICctd2Via2l0LW1hc2snOiAidXJsKCIuY29uY2F0KHRoaXMuaWNvbkNsYXNzLCAiKSBuby1yZXBlYXQgNTAlIDUwJSIpCiAgICAgIH07CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["isExternal", "name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "iconName", "concat", "svgClass", "styleExternalIcon", "mask"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\SvgIcon\\index.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"isExternal\" :style=\"styleExternalIcon\" class=\"svg-external-icon svg-icon\" v-on=\"$listeners\" />\r\n  <svg v-else :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\r\n    <use :href=\"iconName\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\n// doc: https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage\r\nimport { isExternal } from '@/utils/validate'\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    iconClass: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    className: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.iconClass)\r\n    },\r\n    iconName() {\r\n      return `#icon-${this.iconClass}`\r\n    },\r\n    svgClass() {\r\n      if (this.className) {\r\n        return 'svg-icon ' + this.className\r\n      } else {\r\n        return 'svg-icon'\r\n      }\r\n    },\r\n    styleExternalIcon() {\r\n      return {\r\n        mask: `url(${this.iconClass}) no-repeat 50% 50%`,\r\n        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n.svg-external-icon {\r\n  background-color: currentColor;\r\n  mask-size: cover!important;\r\n  display: inline-block;\r\n}\r\n</style>\r\n"], "mappings": "AAQA;AACA,SAASA,UAAS,IAATA,WAAS,QAAS,kBAAiB;AAC5C,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEC,MAAM;MACZG,OAAO,EAAE;IACX;EACF,CAAC;EACDC,QAAQ,EAAE;IACRT,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAOA,WAAU,CAAC,IAAI,CAACG,SAAS;IAClC,CAAC;IACDO,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,gBAAAC,MAAA,CAAgB,IAAI,CAACR,SAAS;IAChC,CAAC;IACDS,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACL,SAAS,EAAE;QAClB,OAAO,WAAU,GAAI,IAAI,CAACA,SAAQ;MACpC,OAAO;QACL,OAAO,UAAS;MAClB;IACF,CAAC;IACDM,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,SAAAH,MAAA,CAAS,IAAI,CAACR,SAAS,wBAAqB;QAChD,cAAc,SAAAQ,MAAA,CAAS,IAAI,CAACR,SAAS;MACvC;IACF;EACF;AACF", "ignoreList": []}]}