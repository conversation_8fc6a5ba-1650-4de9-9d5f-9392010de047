{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue?vue&type=template&id=a361ec26&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1753924830291}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACvF,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEX,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"总销售额\" total=\"￥126,560\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <trend flag=\"top\" style=\"margin-right: 16px;\" rate=\"12\">\r\n              <span slot=\"term\">周同比</span>\r\n            </trend>\r\n            <trend flag=\"bottom\" rate=\"11\">\r\n              <span slot=\"term\">日同比</span>\r\n            </trend>\r\n          </div>\r\n          <template slot=\"footer\">日均销售额<span>￥ 234.56</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"访问量\" :total=\"8846\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-area />\r\n          </div>\r\n          <template slot=\"footer\">日访问量<span> {{ '1234' }}</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"支付笔数\" :total=\"6560\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-bar />\r\n          </div>\r\n          <template slot=\"footer\">转化率 <span>60%</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"运营活动效果\" total=\"78%\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-progress color=\"rgb(19, 194, 194)\" :target=\"80\" :percentage=\"78\" height=\"8px\" />\r\n          </div>\r\n          <template slot=\"footer\">\r\n            <trend flag=\"top\" style=\"margin-right: 16px;\" rate=\"12\">\r\n              <span slot=\"term\">同周比</span>\r\n            </trend>\r\n            <trend flag=\"bottom\" rate=\"80\">\r\n              <span slot=\"term\">日环比</span>\r\n            </trend>\r\n          </template>\r\n        </chart-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-card :bordered=\"false\" :body-style=\"{padding: '0'}\">\r\n      <div class=\"salesCard\">\r\n        <el-tabs>\r\n          <el-tab-pane label=\"销售额\">\r\n            <el-row>\r\n              <el-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <bar :list=\"barData\" title=\"销售额排行\" />\r\n              </el-col>\r\n              <el-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\" />\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"访问量\">\r\n            <el-row>\r\n              <el-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <bar :list=\"barData2\" title=\"销售额趋势\" />\r\n              </el-col>\r\n              <el-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\" />\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-card>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ChartCard from '@/components/ChartCard'\r\nimport Trend from '@/components/Trend'\r\nimport MiniArea from '@/components/MiniArea'\r\nimport MiniBar from '@/components/MiniBar'\r\nimport MiniProgress from '@/components/MiniProgress'\r\nimport RankList from '@/components/RankList/index'\r\nimport Bar from '@/components/Bar.vue'\r\n\r\nconst barData = []\r\nconst barData2 = []\r\nfor (let i = 0; i < 12; i += 1) {\r\n  barData.push({\r\n    x: `${i + 1}月`,\r\n    y: Math.floor(Math.random() * 1000) + 200\r\n  })\r\n  barData2.push({\r\n    x: `${i + 1}月`,\r\n    y: Math.floor(Math.random() * 1000) + 200\r\n  })\r\n}\r\n\r\nconst rankList = []\r\nfor (let i = 0; i < 7; i++) {\r\n  rankList.push({\r\n    name: '白鹭岛 ' + (i + 1) + ' 号店',\r\n    total: 1234.56 - i * 100\r\n  })\r\n}\r\n\r\nexport default {\r\n  name: 'DashboardAdmin',\r\n  components: {\r\n    ChartCard,\r\n    Trend,\r\n    MiniArea,\r\n    MiniBar,\r\n    MiniProgress,\r\n    RankList,\r\n    Bar\r\n  },\r\n  data() {\r\n    return {\r\n      barData,\r\n      barData2,\r\n      rankList\r\n    }\r\n  },\r\n  methods: {\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 12px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .github-corner {\r\n    position: absolute;\r\n    top: 0;\r\n    border: 0;\r\n    right: 0;\r\n  }\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tabs__item{\r\n   padding-left: 16px!important;\r\n   height: 50px;\r\n   line-height: 50px;\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n</style>\r\n"]}]}