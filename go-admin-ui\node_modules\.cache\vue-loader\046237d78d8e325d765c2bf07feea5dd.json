{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue?vue&type=style&index=0&id=a361ec26&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1753924830291}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZGFzaGJvYXJkLWVkaXRvci1jb250YWluZXIgew0KICBwYWRkaW5nOiAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQwLCAyNDIsIDI0NSk7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAuZ2l0aHViLWNvcm5lciB7DQogICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgIHRvcDogMDsNCiAgICBib3JkZXI6IDA7DQogICAgcmlnaHQ6IDA7DQogIH0NCg0KICAuY2hhcnQtd3JhcHBlciB7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICBwYWRkaW5nOiAxNnB4IDE2cHggMDsNCiAgICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICB9DQp9DQoNCjo6di1kZWVwIC5lbC10YWJzX19pdGVtew0KICAgcGFkZGluZy1sZWZ0OiAxNnB4IWltcG9ydGFudDsNCiAgIGhlaWdodDogNTBweDsNCiAgIGxpbmUtaGVpZ2h0OiA1MHB4Ow0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDoxMDI0cHgpIHsNCiAgLmNoYXJ0LXdyYXBwZXIgew0KICAgIHBhZGRpbmc6IDhweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue"], "names": [], "mappings": ";AAiJA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACd;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"总销售额\" total=\"￥126,560\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <trend flag=\"top\" style=\"margin-right: 16px;\" rate=\"12\">\r\n              <span slot=\"term\">周同比</span>\r\n            </trend>\r\n            <trend flag=\"bottom\" rate=\"11\">\r\n              <span slot=\"term\">日同比</span>\r\n            </trend>\r\n          </div>\r\n          <template slot=\"footer\">日均销售额<span>￥ 234.56</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"访问量\" :total=\"8846\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-area />\r\n          </div>\r\n          <template slot=\"footer\">日访问量<span> {{ '1234' }}</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"支付笔数\" :total=\"6560\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-bar />\r\n          </div>\r\n          <template slot=\"footer\">转化率 <span>60%</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"运营活动效果\" total=\"78%\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-progress color=\"rgb(19, 194, 194)\" :target=\"80\" :percentage=\"78\" height=\"8px\" />\r\n          </div>\r\n          <template slot=\"footer\">\r\n            <trend flag=\"top\" style=\"margin-right: 16px;\" rate=\"12\">\r\n              <span slot=\"term\">同周比</span>\r\n            </trend>\r\n            <trend flag=\"bottom\" rate=\"80\">\r\n              <span slot=\"term\">日环比</span>\r\n            </trend>\r\n          </template>\r\n        </chart-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-card :bordered=\"false\" :body-style=\"{padding: '0'}\">\r\n      <div class=\"salesCard\">\r\n        <el-tabs>\r\n          <el-tab-pane label=\"销售额\">\r\n            <el-row>\r\n              <el-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <bar :list=\"barData\" title=\"销售额排行\" />\r\n              </el-col>\r\n              <el-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\" />\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"访问量\">\r\n            <el-row>\r\n              <el-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <bar :list=\"barData2\" title=\"销售额趋势\" />\r\n              </el-col>\r\n              <el-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\" />\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-card>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ChartCard from '@/components/ChartCard'\r\nimport Trend from '@/components/Trend'\r\nimport MiniArea from '@/components/MiniArea'\r\nimport MiniBar from '@/components/MiniBar'\r\nimport MiniProgress from '@/components/MiniProgress'\r\nimport RankList from '@/components/RankList/index'\r\nimport Bar from '@/components/Bar.vue'\r\n\r\nconst barData = []\r\nconst barData2 = []\r\nfor (let i = 0; i < 12; i += 1) {\r\n  barData.push({\r\n    x: `${i + 1}月`,\r\n    y: Math.floor(Math.random() * 1000) + 200\r\n  })\r\n  barData2.push({\r\n    x: `${i + 1}月`,\r\n    y: Math.floor(Math.random() * 1000) + 200\r\n  })\r\n}\r\n\r\nconst rankList = []\r\nfor (let i = 0; i < 7; i++) {\r\n  rankList.push({\r\n    name: '白鹭岛 ' + (i + 1) + ' 号店',\r\n    total: 1234.56 - i * 100\r\n  })\r\n}\r\n\r\nexport default {\r\n  name: 'DashboardAdmin',\r\n  components: {\r\n    ChartCard,\r\n    Trend,\r\n    MiniArea,\r\n    MiniBar,\r\n    MiniProgress,\r\n    RankList,\r\n    Bar\r\n  },\r\n  data() {\r\n    return {\r\n      barData,\r\n      barData2,\r\n      rankList\r\n    }\r\n  },\r\n  methods: {\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 12px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .github-corner {\r\n    position: absolute;\r\n    top: 0;\r\n    border: 0;\r\n    right: 0;\r\n  }\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tabs__item{\r\n   padding-left: 16px!important;\r\n   height: 50px;\r\n   line-height: 50px;\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n</style>\r\n"]}]}