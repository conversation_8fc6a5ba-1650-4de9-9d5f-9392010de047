{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\table.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\table.js", "mtime": 1753924829938}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwovLyDmn6Xor6LliJfooagKZXhwb3J0IGZ1bmN0aW9uIGdldEl0ZW1zKGYsIHF1ZXJ5KSB7CiAgcXVlcnkgPSBxdWVyeSB8fCB7CiAgICBwYWdlU2l6ZTogMTAwMDAKICB9OwogIHJldHVybiBmKHF1ZXJ5KTsKfQpleHBvcnQgZnVuY3Rpb24gc2V0SXRlbXMocmVzcG9uc2UsIGssIHYpIHsKICB2YXIgZGF0YSA9IFtdOwogIGsgPSBrIHx8ICdpZCc7CiAgdiA9IHYgfHwgJ25hbWUnOwogIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEubGlzdCAmJiByZXNwb25zZS5kYXRhLmxpc3QubGVuZ3RoID4gMCkgewogICAgcmVzcG9uc2UuZGF0YS5saXN0LmZvckVhY2goZnVuY3Rpb24gKGUpIHsKICAgICAgZGF0YS5wdXNoKHsKICAgICAgICBrZXk6IGVba10udG9TdHJpbmcoKSwKICAgICAgICB2YWx1ZTogZVt2XS50b1N0cmluZygpCiAgICAgIH0pOwogICAgfSk7CiAgICByZXR1cm4gZGF0YTsKICB9Cn0="}, {"version": 3, "names": ["getItems", "f", "query", "pageSize", "setItems", "response", "k", "v", "data", "list", "length", "for<PERSON>ach", "e", "push", "key", "toString", "value"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/table.js"], "sourcesContent": ["// 查询列表\r\nexport function getItems(f, query) {\r\n  query = query || { pageSize: 10000 }\r\n  return f(query)\r\n}\r\n\r\nexport function setItems(response, k, v) {\r\n  const data = []\r\n  k = k || 'id'\r\n  v = v || 'name'\r\n  if (response.data && response.data.list && response.data.list.length > 0) {\r\n    response.data.list.forEach(e => {\r\n      data.push({\r\n        key: e[k].toString(),\r\n        value: e[v].toString()\r\n      })\r\n    })\r\n    return data\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA;AACA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAEC,KAAK,EAAE;EACjCA,KAAK,GAAGA,KAAK,IAAI;IAAEC,QAAQ,EAAE;EAAM,CAAC;EACpC,OAAOF,CAAC,CAACC,KAAK,CAAC;AACjB;AAEA,OAAO,SAASE,QAAQA,CAACC,QAAQ,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACvC,IAAMC,IAAI,GAAG,EAAE;EACfF,CAAC,GAAGA,CAAC,IAAI,IAAI;EACbC,CAAC,GAAGA,CAAC,IAAI,MAAM;EACf,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,IAAI,IAAIJ,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;IACxEL,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACE,OAAO,CAAC,UAAAC,CAAC,EAAI;MAC9BJ,IAAI,CAACK,IAAI,CAAC;QACRC,GAAG,EAAEF,CAAC,CAACN,CAAC,CAAC,CAACS,QAAQ,CAAC,CAAC;QACpBC,KAAK,EAAEJ,CAAC,CAACL,CAAC,CAAC,CAACQ,QAAQ,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOP,IAAI;EACb;AACF", "ignoreList": []}]}