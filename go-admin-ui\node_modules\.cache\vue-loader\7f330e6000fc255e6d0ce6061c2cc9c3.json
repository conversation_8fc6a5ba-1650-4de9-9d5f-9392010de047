{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue?vue&type=style&index=0&id=12d4a639&scoped=true&lang=css", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue", "mtime": 1753924830069}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZXhjZWwtdXBsb2FkLWlucHV0ew0KICBkaXNwbGF5OiBub25lOw0KICB6LWluZGV4OiAtOTk5OTsNCn0NCi5kcm9wew0KICBib3JkZXI6IDJweCBkYXNoZWQgI2JiYjsNCiAgd2lkdGg6IDYwMHB4Ow0KICBoZWlnaHQ6IDE2MHB4Ow0KICBsaW5lLWhlaWdodDogMTYwcHg7DQogIG1hcmdpbjogMCBhdXRvOw0KICBmb250LXNpemU6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogI2JiYjsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue"], "names": [], "mappings": ";AAyHA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/UploadExcel/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <input ref=\"excel-upload-input\" class=\"excel-upload-input\" type=\"file\" accept=\".xlsx, .xls\" @change=\"handleClick\">\r\n    <div class=\"drop\" @drop=\"handleDrop\" @dragover=\"handleDragover\" @dragenter=\"handleDragover\">\r\n      Drop excel file here or\r\n      <el-button :loading=\"loading\" style=\"margin-left:16px;\" size=\"mini\" type=\"primary\" @click=\"handleUpload\">\r\n        Browse\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport XLSX from 'xlsx'\r\n\r\nexport default {\r\n  props: {\r\n    beforeUpload: Function, // eslint-disable-line\r\n    onSuccess: Function// eslint-disable-line\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      excelData: {\r\n        header: null,\r\n        results: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    generateData({ header, results }) {\r\n      this.excelData.header = header\r\n      this.excelData.results = results\r\n      this.onSuccess && this.onSuccess(this.excelData)\r\n    },\r\n    handleDrop(e) {\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n      if (this.loading) return\r\n      const files = e.dataTransfer.files\r\n      if (files.length !== 1) {\r\n        this.$message.error('Only support uploading one file!')\r\n        return\r\n      }\r\n      const rawFile = files[0] // only use files[0]\r\n\r\n      if (!this.isExcel(rawFile)) {\r\n        this.$message.error('Only supports upload .xlsx, .xls, .csv suffix files')\r\n        return false\r\n      }\r\n      this.upload(rawFile)\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n    },\r\n    handleDragover(e) {\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n      e.dataTransfer.dropEffect = 'copy'\r\n    },\r\n    handleUpload() {\r\n      this.$refs['excel-upload-input'].click()\r\n    },\r\n    handleClick(e) {\r\n      const files = e.target.files\r\n      const rawFile = files[0] // only use files[0]\r\n      if (!rawFile) return\r\n      this.upload(rawFile)\r\n    },\r\n    upload(rawFile) {\r\n      this.$refs['excel-upload-input'].value = null // fix can't select the same excel\r\n\r\n      if (!this.beforeUpload) {\r\n        this.readerData(rawFile)\r\n        return\r\n      }\r\n      const before = this.beforeUpload(rawFile)\r\n      if (before) {\r\n        this.readerData(rawFile)\r\n      }\r\n    },\r\n    readerData(rawFile) {\r\n      this.loading = true\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader()\r\n        reader.onload = e => {\r\n          const data = e.target.result\r\n          const workbook = XLSX.read(data, { type: 'array' })\r\n          const firstSheetName = workbook.SheetNames[0]\r\n          const worksheet = workbook.Sheets[firstSheetName]\r\n          const header = this.getHeaderRow(worksheet)\r\n          const results = XLSX.utils.sheet_to_json(worksheet)\r\n          this.generateData({ header, results })\r\n          this.loading = false\r\n          resolve()\r\n        }\r\n        reader.readAsArrayBuffer(rawFile)\r\n      })\r\n    },\r\n    getHeaderRow(sheet) {\r\n      const headers = []\r\n      const range = XLSX.utils.decode_range(sheet['!ref'])\r\n      let C\r\n      const R = range.s.r\r\n      /* start in the first row */\r\n      for (C = range.s.c; C <= range.e.c; ++C) { /* walk every column in the range */\r\n        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]\r\n        /* find the cell in the first row */\r\n        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default\r\n        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)\r\n        headers.push(hdr)\r\n      }\r\n      return headers\r\n    },\r\n    isExcel(file) {\r\n      return /\\.(xlsx|xls|csv)$/.test(file.name)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.excel-upload-input{\r\n  display: none;\r\n  z-index: -9999;\r\n}\r\n.drop{\r\n  border: 2px dashed #bbb;\r\n  width: 600px;\r\n  height: 160px;\r\n  line-height: 160px;\r\n  margin: 0 auto;\r\n  font-size: 24px;\r\n  border-radius: 5px;\r\n  text-align: center;\r\n  color: #bbb;\r\n  position: relative;\r\n}\r\n</style>\r\n"]}]}