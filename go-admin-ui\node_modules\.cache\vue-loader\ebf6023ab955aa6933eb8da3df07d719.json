{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue", "mtime": 1753924830455}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue"], "names": [], "mappings": ";AAsDA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACb;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC;MACF;IACF,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC;IACH,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrB;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/userAvatar.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <img :src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" @click=\"editCropper()\">\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" :close-on-click-modal=\"false\">\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :auto-crop=\"options.autoCrop\"\r\n            :auto-crop-width=\"options.autoCropWidth\"\r\n            :auto-crop-height=\"options.autoCropHeight\"\r\n            :fixed-box=\"options.fixedBox\"\r\n            @realTime=\"realTime\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\">\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br>\r\n      <el-row>\r\n        <el-col :lg=\"2\" :md=\"2\">\r\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\r\n            <el-button size=\"small\">\r\n              上传\r\n              <i class=\"el-icon-upload el-icon--right\" />\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 2}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 2, offset: 6}\" :md=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from '@/store'\r\nimport { VueCropper } from 'vue-cropper'\r\nimport { uploadAvatar } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  props: {\r\n    // eslint-disable-next-line vue/require-default-prop\r\n    user: { type: Object }\r\n  },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 弹出层标题\r\n      title: '修改头像',\r\n      options: {\r\n        img: store.getters.avatar, // 裁剪图片的地址\r\n        autoCrop: true, // 是否默认生成截图框\r\n        autoCropWidth: 200, // 默认生成截图框宽度\r\n        autoCropHeight: 200, // 默认生成截图框高度\r\n        fixedBox: true // 固定截图框大小 不允许改变\r\n      },\r\n      previews: {}\r\n    }\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {\r\n    },\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft()\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight()\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1\r\n      this.$refs.cropper.changeScale(num)\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf('image/') === -1) {\r\n        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')\r\n      } else {\r\n        const reader = new FileReader()\r\n        reader.readAsDataURL(file)\r\n        reader.onload = () => {\r\n          this.options.img = reader.result\r\n        }\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob(data => {\r\n        const formData = new FormData()\r\n        formData.append('upload[]', data)\r\n        uploadAvatar(formData).then(response => {\r\n          if (response.code === 200) {\r\n            this.open = false\r\n            this.options.img = process.env.VUE_APP_BASE_API + response.data\r\n            this.msgSuccess(response.msg)\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n          this.$refs.cropper.clearCrop()\r\n        })\r\n      })\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}