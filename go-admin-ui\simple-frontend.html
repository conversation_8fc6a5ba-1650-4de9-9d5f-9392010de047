<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Go Admin UI - Simple Frontend</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/theme-chalk/index.css">
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/index.js"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #fff;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
            padding: 0 20px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #1890ff;
        }
        .content {
            margin-top: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        }
        .login-form {
            width: 350px;
            margin: 100px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        }
        .login-title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #1890ff;
        }
        .table-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div v-if="!isLoggedIn">
            <div class="login-form">
                <div class="login-title">Go Admin</div>
                <el-form :model="loginForm" :rules="loginRules" ref="loginForm">
                    <el-form-item prop="username">
                        <el-input v-model="loginForm.username" prefix-icon="el-icon-user" placeholder="用户名"></el-input>
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input v-model="loginForm.password" prefix-icon="el-icon-lock" type="password" placeholder="密码"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" style="width: 100%" @click="handleLogin">登录</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div v-else>
            <div class="header">
                <div class="logo">Go Admin</div>
                <div>
                    <el-dropdown @command="handleCommand">
                        <span class="el-dropdown-link">
                            {{ username }} <i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
            <div class="container">
                <div class="content">
                    <div class="table-header">
                        <h2>用户管理</h2>
                        <el-button type="primary" @click="handleAdd">添加用户</el-button>
                    </div>
                    <el-table :data="tableData" border style="width: 100%">
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="username" label="用户名"></el-table-column>
                        <el-table-column prop="nickname" label="昵称"></el-table-column>
                        <el-table-column prop="roleName" label="角色"></el-table-column>
                        <el-table-column prop="status" label="状态">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
                                    {{ scope.row.status === '1' ? '启用' : '禁用' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="200">
                            <template slot-scope="scope">
                                <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
                                <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total">
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 添加/编辑用户对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
            <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="form.username"></el-input>
                </el-form-item>
                <el-form-item label="昵称" prop="nickname">
                    <el-input v-model="form.nickname"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password" v-if="dialogStatus === 'create'">
                    <el-input v-model="form.password" type="password"></el-input>
                </el-form-item>
                <el-form-item label="角色" prop="roleId">
                    <el-select v-model="form.roleId" placeholder="请选择角色">
                        <el-option v-for="item in roles" :key="item.id" :label="item.roleName" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio label="1">启用</el-radio>
                        <el-radio label="2">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    isLoggedIn: false,
                    username: '',
                    loginForm: {
                        username: 'admin',
                        password: '123456'
                    },
                    loginRules: {
                        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
                    },
                    tableData: [],
                    currentPage: 1,
                    pageSize: 10,
                    total: 0,
                    dialogVisible: false,
                    dialogStatus: '',
                    dialogTitle: '',
                    form: {
                        id: undefined,
                        username: '',
                        nickname: '',
                        password: '',
                        roleId: '',
                        status: '1'
                    },
                    rules: {
                        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                        nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
                        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
                        roleId: [{ required: true, message: '请选择角色', trigger: 'change' }]
                    },
                    roles: [
                        { id: 1, roleName: '管理员' },
                        { id: 2, roleName: '普通用户' }
                    ]
                };
            },
            created() {
                // 检查是否已登录
                const token = localStorage.getItem('token');
                if (token) {
                    this.isLoggedIn = true;
                    this.username = localStorage.getItem('username') || '用户';
                    this.fetchData();
                }
            },
            methods: {
                handleLogin() {
                    this.$refs.loginForm.validate(valid => {
                        if (valid) {
                            // 模拟登录请求
                            setTimeout(() => {
                                this.isLoggedIn = true;
                                this.username = this.loginForm.username;
                                localStorage.setItem('token', 'demo-token');
                                localStorage.setItem('username', this.loginForm.username);
                                this.fetchData();
                            }, 1000);
                        }
                    });
                },
                handleCommand(command) {
                    if (command === 'logout') {
                        this.isLoggedIn = false;
                        localStorage.removeItem('token');
                        localStorage.removeItem('username');
                    }
                },
                fetchData() {
                    // 模拟获取用户数据
                    setTimeout(() => {
                        const data = [];
                        for (let i = 1; i <= 20; i++) {
                            data.push({
                                id: i,
                                username: `user${i}`,
                                nickname: `用户${i}`,
                                roleName: i % 3 === 0 ? '管理员' : '普通用户',
                                status: i % 4 === 0 ? '2' : '1'
                            });
                        }
                        this.tableData = data.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize);
                        this.total = data.length;
                    }, 500);
                },
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.fetchData();
                },
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.fetchData();
                },
                handleAdd() {
                    this.dialogStatus = 'create';
                    this.dialogTitle = '添加用户';
                    this.form = {
                        id: undefined,
                        username: '',
                        nickname: '',
                        password: '',
                        roleId: '',
                        status: '1'
                    };
                    this.dialogVisible = true;
                    this.$nextTick(() => {
                        this.$refs.form && this.$refs.form.clearValidate();
                    });
                },
                handleEdit(row) {
                    this.dialogStatus = 'update';
                    this.dialogTitle = '编辑用户';
                    this.form = Object.assign({}, row);
                    this.dialogVisible = true;
                    this.$nextTick(() => {
                        this.$refs.form && this.$refs.form.clearValidate();
                    });
                },
                handleDelete(row) {
                    this.$confirm('确认删除该用户?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 模拟删除
                        this.tableData = this.tableData.filter(item => item.id !== row.id);
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                    }).catch(() => {});
                },
                submitForm() {
                    this.$refs.form.validate(valid => {
                        if (valid) {
                            if (this.dialogStatus === 'create') {
                                // 模拟添加
                                const newUser = Object.assign({}, this.form, {
                                    id: this.tableData.length + 1,
                                    roleName: this.roles.find(r => r.id === this.form.roleId).roleName
                                });
                                this.tableData.unshift(newUser);
                            } else {
                                // 模拟更新
                                const index = this.tableData.findIndex(item => item.id === this.form.id);
                                if (index !== -1) {
                                    this.tableData.splice(index, 1, Object.assign({}, this.form, {
                                        roleName: this.roles.find(r => r.id === this.form.roleId).roleName
                                    }));
                                }
                            }
                            this.dialogVisible = false;
                            this.$message({
                                type: 'success',
                                message: this.dialogStatus === 'create' ? '添加成功!' : '更新成功!'
                            });
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
