{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\monitor\\server.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\monitor\\server.js", "mtime": 1753924829936}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouacjeWKoeWZqOivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0U2VydmVyKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvc2VydmVyLW1vbml0b3InLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9"}, {"version": 3, "names": ["request", "getServer", "url", "method"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/monitor/server.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询服务器详细\r\nexport function getServer() {\r\n  return request({\r\n    url: '/api/v1/server-monitor',\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}