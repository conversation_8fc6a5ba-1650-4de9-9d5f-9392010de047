{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\genInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\genInfoForm.vue", "mtime": 1753924830298}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdCYXNpY0luZm9Gb3JtJywKICBwcm9wczogewogICAgaW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBydWxlczogewogICAgICAgIHRwbENhdGVnb3J5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup55Sf5oiQ5qih5p2/JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIHBhY2thZ2VOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl55Sf5oiQ5YyF6Lev5b6EJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH0sIHsKICAgICAgICAgIHBhdHRlcm46IC9eW2Etel0qJC9nLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInLAogICAgICAgICAgbWVzc2FnZTogJ+WPquWFgeiuuOWwj+WGmeWtl+avjSzkvovlpoIgc3lzdGVtIOagvOW8jycKICAgICAgICB9XSwKICAgICAgICBtb2R1bGVOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl55Sf5oiQ5qih5Z2X5ZCNJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH0sIHsKICAgICAgICAgIHBhdHRlcm46IC9eW2EtelwtXSpbYS16XSQvZywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJywKICAgICAgICAgIG1lc3NhZ2U6ICflj6rlhYHorrjlsI/lhpnlrZfmr40s5L6L5aaCIHN5cy1kZW1vIOagvOW8jycKICAgICAgICB9XSwKICAgICAgICBidXNpbmVzc05hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXnlJ/miJDkuJrliqHlkI0nLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfSwgewogICAgICAgICAgcGF0dGVybjogL15bYS16XVtBLVphLXpdKyQvLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInLAogICAgICAgICAgbWVzc2FnZTogJ+agoemqjOinhOWImTogIOWPquWFgeiuuOi+k+WFpeWtl+avjSBhLXog5oiW5aSn5YaZIEEtWiDvvIzlubbkuJTlsI/lhpnlrZfmr43lvIDlpLQnCiAgICAgICAgfV0sCiAgICAgICAgZnVuY3Rpb25OYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl55Sf5oiQ5Yqf6IO95ZCNJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30KfTs="}, {"version": 3, "names": ["name", "props", "info", "type", "Object", "default", "data", "rules", "tplCategory", "required", "message", "trigger", "packageName", "pattern", "moduleName", "businessName", "functionName", "created"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\genInfoForm.vue"], "sourcesContent": ["<template>\r\n  <el-form ref=\"genInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tplCategory\">\r\n          <span slot=\"label\">生成模板</span>\r\n          <el-select v-model=\"info.tplCategory\">\r\n            <el-option label=\"关系表（增删改查）\" value=\"crud\" />\r\n            <!-- <el-option label=\"关系表（增删改查）\" value=\"mcrud\" />\r\n            <el-option label=\"树表（增删改查）\" value=\"tree\" /> -->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"packageName\">\r\n          <span slot=\"label\">\r\n            应用名\r\n            <el-tooltip content=\"应用名，例如：在app文件夹下将该功能发到那个应用中，默认：admin\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.packageName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"moduleFrontName\">\r\n          <span slot=\"label\">\r\n            前端文件名\r\n            <el-tooltip content=\"前端项目文件名，例如 sys-user.js \" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.moduleFrontName\" />\r\n        </el-form-item>\r\n      </el-col> -->\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"businessName\">\r\n          <span slot=\"label\">\r\n            业务名\r\n            <el-tooltip content=\"可理解为功能英文名，例如 user\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.businessName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"functionName\">\r\n          <span slot=\"label\">\r\n            功能描述\r\n            <el-tooltip content=\"同步的数据库表备注，用作类描述，例如：用户\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.functionName\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"moduleName\">\r\n          <span slot=\"label\">\r\n            接口路径\r\n            <el-tooltip content=\"接口路径，例如：api/v1/{sys-user}\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.moduleName\">\r\n            <template slot=\"prepend\">api/{version}/</template>\r\n            <template slot=\"append\">...</template>\r\n          </el-input>\r\n        </el-form-item>\r\n        <!-- <el-alert\r\n          title=\"接口地址示例\"\r\n          description=\"[get]api/{version}/{接口路径} \\r\\n [post]\"\r\n          type=\"success\"\r\n          show-icon\r\n        /> -->\r\n      </el-col>\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isDataScope\">\r\n          <span slot=\"label\">\r\n            是否认证\r\n            <el-tooltip content=\"是指是否使用用户和角色验证中间件\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.isAuth\">\r\n            <el-option label=\"true\" value=\"1\" />\r\n            <el-option label=\"false\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isDataScope\">\r\n          <span slot=\"label\">\r\n            数据权限\r\n            <el-tooltip content=\"暂不支持\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.isDataScope\" disabled>\r\n            <el-option label=\"true\" value=\"1\" />\r\n            <el-option label=\"false\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isActions\">\r\n          <span slot=\"label\">\r\n            是否actions\r\n            <el-tooltip content=\"系统通用增删改查中间件方法\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.isActions\" disabled>\r\n            <el-option label=\"false\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col> -->\r\n    </el-row>\r\n\r\n    <el-row v-show=\"info.tplCategory == 'tree'\">\r\n      <h4 class=\"form-header\">其他信息</h4>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树编码字段\r\n            <el-tooltip content=\"树显示的编码字段名， 如：dept_id\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"column in info.columns\"\r\n              :key=\"column.columnName\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树父编码字段\r\n            <el-tooltip content=\"树显示的父编码字段名， 如：parent_Id\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeParentCode\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"column in info.columns\"\r\n              :key=\"column.columnName\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item>\r\n          <span slot=\"label\">\r\n            树名称字段\r\n            <el-tooltip content=\"树节点的显示名称字段名， 如：dept_name\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-select v-model=\"info.treeName\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"column in info.columns\"\r\n              :key=\"column.columnName\"\r\n              :label=\"column.columnName + '：' + column.columnComment\"\r\n              :value=\"column.columnName\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoForm',\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tplCategory: [\r\n          { required: true, message: '请选择生成模板', trigger: 'blur' }\r\n        ],\r\n        packageName: [\r\n          { required: true, message: '请输入生成包路径', trigger: 'blur' },\r\n          { pattern: /^[a-z]*$/g, trigger: 'blur', message: '只允许小写字母,例如 system 格式' }\r\n        ],\r\n        moduleName: [\r\n          { required: true, message: '请输入生成模块名', trigger: 'blur' },\r\n          { pattern: /^[a-z\\-]*[a-z]$/g, trigger: 'blur', message: '只允许小写字母,例如 sys-demo 格式' }\r\n        ],\r\n        businessName: [\r\n          { required: true, message: '请输入生成业务名', trigger: 'blur' },\r\n          { pattern: /^[a-z][A-Za-z]+$/, trigger: 'blur', message: '校验规则:  只允许输入字母 a-z 或大写 A-Z ，并且小写字母开头' }\r\n        ],\r\n        functionName: [\r\n          { required: true, message: '请输入生成功能名', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {}\r\n}\r\n</script>\r\n"], "mappings": "AAwLA,eAAe;EACbA,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;QACLC,WAAW,EAAE,CACX;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,EACvD;QACDC,WAAW,EAAE,CACX;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC,EACxD;UAAEE,OAAO,EAAE,WAAW;UAAEF,OAAO,EAAE,MAAM;UAAED,OAAO,EAAE;QAAuB,EAC1E;QACDI,UAAU,EAAE,CACV;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC,EACxD;UAAEE,OAAO,EAAE,kBAAkB;UAAEF,OAAO,EAAE,MAAM;UAAED,OAAO,EAAE;QAAyB,EACnF;QACDK,YAAY,EAAE,CACZ;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC,EACxD;UAAEE,OAAO,EAAE,kBAAkB;UAAEF,OAAO,EAAE,MAAM;UAAED,OAAO,EAAE;QAAuC,EACjG;QACDM,YAAY,EAAE,CACZ;UAAEP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO;MAE3D;IACF;EACF,CAAC;EACDM,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;AACb", "ignoreList": []}]}