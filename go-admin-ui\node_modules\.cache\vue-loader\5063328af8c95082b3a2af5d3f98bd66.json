{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\AppMain.vue?vue&type=style&index=0&id=078753dd&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\AppMain.vue", "mtime": 1753924830205}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYXBwLW1haW4gew0KICAvKiA1MD0gbmF2YmFyICA1MCAgKi8NCiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDkzcHgpOw0KICB3aWR0aDogMTAwJTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouZml4ZWQtaGVhZGVyKy5hcHAtbWFpbiB7DQogIHBhZGRpbmctdG9wOiA5M3B4Ow0KfQ0KDQovLyAuaGFzVGFnc1ZpZXcgew0KLy8gICAuYXBwLW1haW4gew0KLy8gICAgIC8qIDg0ID0gbmF2YmFyICsgdGFncy12aWV3ID0gNTAgKyAzNCAqLw0KLy8gICAgIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA5M3B4KTsNCi8vICAgfQ0KDQovLyAgIC5maXhlZC1oZWFkZXIrLmFwcC1tYWluIHsNCi8vICAgICBwYWRkaW5nLXRvcDogOTNweDsNCi8vICAgfQ0KLy8gfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\AppMain.vue"], "names": [], "mappings": ";AAyBA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAChB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACf,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;AAC7C,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,IAAI;;AAEL,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7B,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,IAAI;AACL,CAAC,EAAE", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/AppMain.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AppMain',\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 93px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.fixed-header+.app-main {\r\n  padding-top: 93px;\r\n}\r\n\r\n// .hasTagsView {\r\n//   .app-main {\r\n//     /* 84 = navbar + tags-view = 50 + 34 */\r\n//     min-height: calc(100vh - 93px);\r\n//   }\r\n\r\n//   .fixed-header+.app-main {\r\n//     padding-top: 93px;\r\n//   }\r\n// }\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 15px;\r\n  }\r\n}\r\n</style>\r\n"]}]}