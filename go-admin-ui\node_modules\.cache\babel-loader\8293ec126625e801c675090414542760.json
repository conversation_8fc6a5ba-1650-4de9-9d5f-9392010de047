{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1753924830212}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["path", "isExternal", "<PERSON><PERSON>", "AppLink", "FixiOSBug", "name", "components", "mixins", "props", "item", "type", "Object", "required", "isNest", "Boolean", "default", "basePath", "String", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods", "hasOneShowingChild", "_this", "children", "arguments", "length", "undefined", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "hidden", "_objectSpread", "noShowingChildren", "<PERSON><PERSON><PERSON>", "routePath", "resolve"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"!item.hidden\" class=\"menu-wrapper\">\r\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\r\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path)\">\r\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\r\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n\r\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body :style=\"{ backgroundColor: '#000c17' }\">\r\n      <template slot=\"title\">\r\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\r\n      </template>\r\n      <sidebar-item\r\n        v-for=\"child in item.children\"\r\n        :key=\"child.path\"\r\n        :is-nest=\"true\"\r\n        :item=\"child\"\r\n        :base-path=\"resolvePath(child.path)\"\r\n        class=\"nest-menu\"\r\n      />\r\n    </el-submenu>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\nimport { isExternal } from '@/utils/validate'\r\nimport Item from './Item'\r\nimport AppLink from './Link'\r\nimport FixiOSBug from './FixiOSBug'\r\n\r\nexport default {\r\n  name: 'SidebarItem',\r\n  components: { Item, AppLink },\r\n  mixins: [FixiOSBug],\r\n  props: {\r\n    // route object\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    basePath: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237\r\n    // TODO: refactor with render function\r\n    this.onlyOneChild = null\r\n    return {}\r\n  },\r\n  methods: {\r\n    hasOneShowingChild(children = [], parent) {\r\n      const showingChildren = children.filter(item => {\r\n        if (item.hidden) {\r\n          return false\r\n        } else {\r\n          // Temp set(will be used if only has one showing child)\r\n          this.onlyOneChild = item\r\n          return true\r\n        }\r\n      })\r\n\r\n      // When there is only one child router, the child router is displayed by default\r\n      if (showingChildren.length === 1) {\r\n        return true\r\n      }\r\n\r\n      // Show parent if there are no child router to display\r\n      if (showingChildren.length === 0) {\r\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\r\n        return true\r\n      }\r\n\r\n      return false\r\n    },\r\n    resolvePath(routePath) {\r\n      if (isExternal(routePath)) {\r\n        return routePath\r\n      }\r\n      if (isExternal(this.basePath)) {\r\n        return this.basePath\r\n      }\r\n      return path.resolve(this.basePath, routePath)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;AA2BA,OAAOA,IAAG,MAAO,MAAK;AACtB,SAASC,UAAS,QAAS,kBAAiB;AAC5C,OAAOC,IAAG,MAAO,QAAO;AACxB,OAAOC,OAAM,MAAO,QAAO;AAC3B,OAAOC,SAAQ,MAAO,aAAY;AAElC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IAAEJ,IAAI,EAAJA,IAAI;IAAEC,OAAM,EAANA;EAAQ,CAAC;EAC7BI,MAAM,EAAE,CAACH,SAAS,CAAC;EACnBI,KAAK,EAAE;IACL;IACAC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNH,IAAI,EAAEI,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE;MACRN,IAAI,EAAEO,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL;IACA;IACA,IAAI,CAACC,YAAW,GAAI,IAAG;IACvB,OAAO,CAAC;EACV,CAAC;EACDC,OAAO,EAAE;IACPC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAwB;MAAA,IAAAC,KAAA;MAAA,IAAvBC,QAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAI,EAAE;MAAA,IAAEG,MAAM,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACtC,IAAME,eAAc,GAAIL,QAAQ,CAACM,MAAM,CAAC,UAAApB,IAAG,EAAK;QAC9C,IAAIA,IAAI,CAACqB,MAAM,EAAE;UACf,OAAO,KAAI;QACb,OAAO;UACL;UACAR,KAAI,CAACH,YAAW,GAAIV,IAAG;UACvB,OAAO,IAAG;QACZ;MACF,CAAC;;MAED;MACA,IAAImB,eAAe,CAACH,MAAK,KAAM,CAAC,EAAE;QAChC,OAAO,IAAG;MACZ;;MAEA;MACA,IAAIG,eAAe,CAACH,MAAK,KAAM,CAAC,EAAE;QAChC,IAAI,CAACN,YAAW,GAAAY,aAAA,CAAAA,aAAA,KAAUJ,MAAM;UAAE3B,IAAI,EAAE,EAAE;UAAEgC,iBAAiB,EAAE;QAAG,EAAE;QACpE,OAAO,IAAG;MACZ;MAEA,OAAO,KAAI;IACb,CAAC;IACDC,WAAW,WAAXA,WAAWA,CAACC,SAAS,EAAE;MACrB,IAAIjC,UAAU,CAACiC,SAAS,CAAC,EAAE;QACzB,OAAOA,SAAQ;MACjB;MACA,IAAIjC,UAAU,CAAC,IAAI,CAACe,QAAQ,CAAC,EAAE;QAC7B,OAAO,IAAI,CAACA,QAAO;MACrB;MACA,OAAOhB,IAAI,CAACmC,OAAO,CAAC,IAAI,CAACnB,QAAQ,EAAEkB,SAAS;IAC9C;EACF;AACF", "ignoreList": []}]}