{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\swagger\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\swagger\\index.vue", "mtime": 1753924830301}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTd2FnZ2VyJywKICBjb21wb25lbnRzOiB7fSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc3JjOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgJy9zd2FnZ2VyL2FkbWluL2luZGV4Lmh0bWwnLAogICAgICBoZWlnaHQ6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgJ3B4OycsCiAgICAgIGxvYWRpbmc6IHRydWUKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICB9LCAyMzApOwogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgd2luZG93Lm9ucmVzaXplID0gZnVuY3Rpb24gdGVtcCgpIHsKICAgICAgdGhhdC5oZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICdweDsnOwogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["name", "components", "data", "src", "process", "env", "VUE_APP_BASE_API", "height", "document", "documentElement", "clientHeight", "loading", "mounted", "_this", "setTimeout", "that", "window", "onresize", "temp"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\swagger\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <div v-loading=\"loading\" :style=\"'height:' + height\">\r\n          <iframe\r\n            :src=\"src\"\r\n            frameborder=\"no\"\r\n            style=\"width: 100%; height: 100%\"\r\n            scrolling=\"auto\"\r\n          />\r\n        </div>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'Swagger',\r\n  components: {},\r\n  data() {\r\n    return {\r\n      src: process.env.VUE_APP_BASE_API + '/swagger/admin/index.html',\r\n      height: document.documentElement.clientHeight - 94.5 + 'px;',\r\n      loading: true\r\n    }\r\n  },\r\n  mounted: function() {\r\n    setTimeout(() => {\r\n      this.loading = false\r\n    }, 230)\r\n    const that = this\r\n    window.onresize = function temp() {\r\n      that.height = document.documentElement.clientHeight - 94.5 + 'px;'\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAiBA,eAAe;EACbA,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,CAAC,CAAC;EACdC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,2BAA2B;MAC/DC,MAAM,EAAEC,QAAQ,CAACC,eAAe,CAACC,YAAW,GAAI,IAAG,GAAI,KAAK;MAC5DC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAa;IAAA,IAAAC,KAAA;IAClBC,UAAU,CAAC,YAAM;MACfD,KAAI,CAACF,OAAM,GAAI,KAAI;IACrB,CAAC,EAAE,GAAG;IACN,IAAMI,IAAG,GAAI,IAAG;IAChBC,MAAM,CAACC,QAAO,GAAI,SAASC,IAAIA,CAAA,EAAG;MAChCH,IAAI,CAACR,MAAK,GAAIC,QAAQ,CAACC,eAAe,CAACC,YAAW,GAAI,IAAG,GAAI,KAAI;IACnE;EACF;AACF", "ignoreList": []}]}