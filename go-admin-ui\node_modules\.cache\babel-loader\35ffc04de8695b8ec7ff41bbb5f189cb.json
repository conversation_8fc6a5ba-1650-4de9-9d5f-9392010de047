{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\validate.js", "mtime": 1753924830263}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["isExternal", "path", "test", "validUsername", "str", "valid_map", "indexOf", "trim", "validURL", "url", "reg", "validLowerCase", "validUpperCase", "validAlphabets", "validEmail", "email", "isString", "String", "isArray", "arg", "Array", "Object", "prototype", "toString", "call"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/validate.js"], "sourcesContent": ["/**\r\n * Created by PanJiaChen on 16/11/18.\r\n */\r\n\r\n/**\r\n * @param {string} path\r\n * @returns {Boolean}\r\n */\r\nexport function isExternal(path) {\r\n  return /^(https?:|mailto:|tel:)/.test(path)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validUsername(str) {\r\n  const valid_map = ['admin', 'editor']\r\n  return valid_map.indexOf(str.trim()) >= 0\r\n}\r\n\r\n/**\r\n * @param {string} url\r\n * @returns {Boolean}\r\n */\r\nexport function validURL(url) {\r\n  const reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/\r\n  return reg.test(url)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validLowerCase(str) {\r\n  const reg = /^[a-z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validUpperCase(str) {\r\n  const reg = /^[A-Z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function validAlphabets(str) {\r\n  const reg = /^[A-Za-z]+$/\r\n  return reg.test(str)\r\n}\r\n\r\n/**\r\n * @param {string} email\r\n * @returns {Boolean}\r\n */\r\nexport function validEmail(email) {\r\n  const reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/\r\n  return reg.test(email)\r\n}\r\n\r\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\r\nexport function isString(str) {\r\n  if (typeof str === 'string' || str instanceof String) {\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\n/**\r\n * @param {Array} arg\r\n * @returns {Boolean}\r\n */\r\nexport function isArray(arg) {\r\n  if (typeof Array.isArray === 'undefined') {\r\n    return Object.prototype.toString.call(arg) === '[object Array]'\r\n  }\r\n  return Array.isArray(arg)\r\n}\r\n"], "mappings": ";;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,yBAAyB,CAACC,IAAI,CAACD,IAAI,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAACC,GAAG,EAAE;EACjC,IAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EACrC,OAAOA,SAAS,CAACC,OAAO,CAACF,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAE;EAC5B,IAAMC,GAAG,GAAG,4TAA4T;EACxU,OAAOA,GAAG,CAACR,IAAI,CAACO,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,cAAcA,CAACP,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASQ,cAAcA,CAACR,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,UAAU;EACtB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASS,cAAcA,CAACT,GAAG,EAAE;EAClC,IAAMM,GAAG,GAAG,aAAa;EACzB,OAAOA,GAAG,CAACR,IAAI,CAACE,GAAG,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASU,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAML,GAAG,GAAG,yJAAyJ;EACrK,OAAOA,GAAG,CAACR,IAAI,CAACa,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACZ,GAAG,EAAE;EAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYa,MAAM,EAAE;IACpD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOC,KAAK,CAACF,OAAO,KAAK,WAAW,EAAE;IACxC,OAAOG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;EACjE;EACA,OAAOC,KAAK,CAACF,OAAO,CAACC,GAAG,CAAC;AAC3B", "ignoreList": []}]}