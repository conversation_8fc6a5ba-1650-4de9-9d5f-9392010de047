{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ThemePicker\\index.vue", "mtime": 1753924830063}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["version", "require", "ORIGINAL_THEME", "data", "chalk", "theme", "computed", "defaultTheme", "$store", "state", "settings", "watch", "handler", "val", "oldVal", "immediate", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "themeCluster", "originalCluster", "$message", "<PERSON><PERSON><PERSON><PERSON>", "url", "<PERSON><PERSON><PERSON><PERSON>", "styles", "w", "_context", "n", "a", "getThemeCluster", "replace", "message", "customClass", "type", "duration", "iconClass", "variable", "id", "newStyle", "updateStyle", "styleTag", "document", "getElementById", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "innerText", "concat", "getCSSString", "slice", "call", "querySelectorAll", "filter", "style", "text", "RegExp", "test", "for<PERSON>ach", "$emit", "close", "methods", "oldCluster", "newCluster", "color", "index", "_this2", "Promise", "resolve", "xhr", "XMLHttpRequest", "onreadystatechange", "readyState", "status", "responseText", "open", "send", "tintColor", "tint", "red", "parseInt", "green", "blue", "join", "Math", "round", "toString", "shadeColor", "shade", "clusters", "i", "push", "Number", "toFixed"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ThemePicker\\index.vue"], "sourcesContent": ["<template>\r\n  <el-color-picker\r\n    v-model=\"theme\"\r\n    :predefine=\"['#1890FF', '#F5222D', '#FA541C','#FAAD14','#13C2C2', '#52C460', '#2F54EB', '#722ED1', '#00b38a', '#2878FF']\"\r\n    class=\"theme-picker\"\r\n    popper-class=\"theme-picker-dropdown\"\r\n  />\r\n</template>\r\n\r\n<script>\r\nconst version = require('element-ui/package.json').version // element-ui version from node_modules\r\nconst ORIGINAL_THEME = '#1890FF' // default color\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      chalk: '', // content of theme-chalk css\r\n      theme: ''\r\n    }\r\n  },\r\n  computed: {\r\n    defaultTheme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    defaultTheme: {\r\n      handler: function(val, oldVal) {\r\n        this.theme = val\r\n      },\r\n      immediate: true\r\n    },\r\n    async theme(val) {\r\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\r\n      if (typeof val !== 'string') return\r\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\r\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\r\n\r\n      const $message = this.$message({\r\n        message: '编译主题中',\r\n        customClass: 'theme-message',\r\n        type: 'success',\r\n        duration: 0,\r\n        iconClass: 'el-icon-loading'\r\n      })\r\n\r\n      const getHandler = (variable, id) => {\r\n        return () => {\r\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\r\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\r\n\r\n          let styleTag = document.getElementById(id)\r\n          if (!styleTag) {\r\n            styleTag = document.createElement('style')\r\n            styleTag.setAttribute('id', id)\r\n            document.head.appendChild(styleTag)\r\n          }\r\n          styleTag.innerText = newStyle\r\n        }\r\n      }\r\n\r\n      if (!this.chalk) {\r\n        const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`\r\n        await this.getCSSString(url, 'chalk')\r\n      }\r\n\r\n      const chalkHandler = getHandler('chalk', 'chalk-style')\r\n\r\n      chalkHandler()\r\n\r\n      const styles = [].slice.call(document.querySelectorAll('style'))\r\n        .filter(style => {\r\n          const text = style.innerText\r\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\r\n        })\r\n      styles.forEach(style => {\r\n        const { innerText } = style\r\n        if (typeof innerText !== 'string') return\r\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\r\n      })\r\n      this.$emit('change', val)\r\n      $message.close()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateStyle(style, oldCluster, newCluster) {\r\n      let newStyle = style\r\n      oldCluster.forEach((color, index) => {\r\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\r\n      })\r\n      return newStyle\r\n    },\r\n\r\n    getCSSString(url, variable) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.onreadystatechange = () => {\r\n          if (xhr.readyState === 4 && xhr.status === 200) {\r\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\r\n            resolve()\r\n          }\r\n        }\r\n        xhr.open('GET', url)\r\n        xhr.send()\r\n      })\r\n    },\r\n\r\n    getThemeCluster(theme) {\r\n      const tintColor = (color, tint) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        if (tint === 0) { // when primary color is in its rgb space\r\n          return [red, green, blue].join(',')\r\n        } else {\r\n          red += Math.round(tint * (255 - red))\r\n          green += Math.round(tint * (255 - green))\r\n          blue += Math.round(tint * (255 - blue))\r\n\r\n          red = red.toString(16)\r\n          green = green.toString(16)\r\n          blue = blue.toString(16)\r\n\r\n          return `#${red}${green}${blue}`\r\n        }\r\n      }\r\n\r\n      const shadeColor = (color, shade) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        red = Math.round((1 - shade) * red)\r\n        green = Math.round((1 - shade) * green)\r\n        blue = Math.round((1 - shade) * blue)\r\n\r\n        red = red.toString(16)\r\n        green = green.toString(16)\r\n        blue = blue.toString(16)\r\n\r\n        return `#${red}${green}${blue}`\r\n      }\r\n\r\n      const clusters = [theme]\r\n      for (let i = 0; i <= 9; i++) {\r\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\r\n      }\r\n      clusters.push(shadeColor(theme, 0.1))\r\n      return clusters\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.theme-message,\r\n.theme-picker-dropdown {\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.theme-picker .el-color-picker__trigger {\r\n  height: 26px !important;\r\n  width: 26px !important;\r\n  padding: 2px;\r\n}\r\n\r\n.theme-picker-dropdown .el-color-dropdown__link-btn {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAUA,IAAMA,OAAM,GAAIC,OAAO,CAAC,yBAAyB,CAAC,CAACD,OAAM,EAAE;AAC3D,IAAME,cAAa,GAAI,SAAQ,EAAE;;AAEjC,eAAe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE,EAAE;MAAE;MACXC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACL,KAAI;IACxC;EACF,CAAC;EACDM,KAAK,EAAE;IACLJ,YAAY,EAAE;MACZK,OAAO,EAAE,SAATA,OAAOA,CAAWC,GAAG,EAAEC,MAAM,EAAE;QAC7B,IAAI,CAACT,KAAI,GAAIQ,GAAE;MACjB,CAAC;MACDE,SAAS,EAAE;IACb,CAAC;IACKV,KAAK,WAALA,KAAKA,CAACQ,GAAG,EAAE;MAAA,IAAAG,KAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,IAAAN,MAAA,EAAAO,YAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,GAAA,EAAAC,YAAA,EAAAC,MAAA;QAAA,OAAAT,YAAA,GAAAU,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACThB,MAAK,GAAIE,KAAI,CAACZ,KAAI,GAAIY,KAAI,CAACX,KAAI,GAAIH,cAAa;cAAA,MAClD,OAAOW,GAAE,KAAM,QAAQ;gBAAAgB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAE,CAAA;YAAA;cACrBV,YAAW,GAAIL,KAAI,CAACgB,eAAe,CAACnB,GAAG,CAACoB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cACxDX,eAAc,GAAIN,KAAI,CAACgB,eAAe,CAAClB,MAAM,CAACmB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cAE9DV,QAAO,GAAIP,KAAI,CAACO,QAAQ,CAAC;gBAC7BW,OAAO,EAAE,OAAO;gBAChBC,WAAW,EAAE,eAAe;gBAC5BC,IAAI,EAAE,SAAS;gBACfC,QAAQ,EAAE,CAAC;gBACXC,SAAS,EAAE;cACb,CAAC;cAEKd,UAAS,GAAI,SAAbA,UAASA,CAAKe,QAAQ,EAAEC,EAAE,EAAK;gBACnC,OAAO,YAAM;kBACX,IAAMlB,eAAc,GAAIN,KAAI,CAACgB,eAAe,CAAC9B,cAAc,CAAC+B,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;kBAC5E,IAAMQ,QAAO,GAAIzB,KAAI,CAAC0B,WAAW,CAAC1B,KAAI,CAACuB,QAAQ,CAAC,EAAEjB,eAAe,EAAED,YAAY;kBAE/E,IAAIsB,QAAO,GAAIC,QAAQ,CAACC,cAAc,CAACL,EAAE;kBACzC,IAAI,CAACG,QAAQ,EAAE;oBACbA,QAAO,GAAIC,QAAQ,CAACE,aAAa,CAAC,OAAO;oBACzCH,QAAQ,CAACI,YAAY,CAAC,IAAI,EAAEP,EAAE;oBAC9BI,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,QAAQ;kBACpC;kBACAA,QAAQ,CAACO,SAAQ,GAAIT,QAAO;gBAC9B;cACF;cAAA,IAEKzB,KAAI,CAACZ,KAAK;gBAAAyB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACPL,GAAE,mCAAA0B,MAAA,CAAoCnD,OAAO;cAAA6B,QAAA,CAAAC,CAAA;cAAA,OAC7Cd,KAAI,CAACoC,YAAY,CAAC3B,GAAG,EAAE,OAAO;YAAA;cAGhCC,YAAW,GAAIF,UAAU,CAAC,OAAO,EAAE,aAAa;cAEtDE,YAAY,CAAC;cAEPC,MAAK,GAAI,EAAE,CAAC0B,KAAK,CAACC,IAAI,CAACV,QAAQ,CAACW,gBAAgB,CAAC,OAAO,CAAC,EAC5DC,MAAM,CAAC,UAAAC,KAAI,EAAK;gBACf,IAAMC,IAAG,GAAID,KAAK,CAACP,SAAQ;gBAC3B,OAAO,IAAIS,MAAM,CAAC7C,MAAM,EAAE,GAAG,CAAC,CAAC8C,IAAI,CAACF,IAAI,KAAK,CAAC,iBAAiB,CAACE,IAAI,CAACF,IAAI;cAC3E,CAAC;cACH/B,MAAM,CAACkC,OAAO,CAAC,UAAAJ,KAAI,EAAK;gBACtB,IAAQP,SAAQ,GAAMO,KAAI,CAAlBP,SAAQ;gBAChB,IAAI,OAAOA,SAAQ,KAAM,QAAQ,EAAE;gBACnCO,KAAK,CAACP,SAAQ,GAAIlC,KAAI,CAAC0B,WAAW,CAACQ,SAAS,EAAE5B,eAAe,EAAED,YAAY;cAC7E,CAAC;cACDL,KAAI,CAAC8C,KAAK,CAAC,QAAQ,EAAEjD,GAAG;cACxBU,QAAQ,CAACwC,KAAK,CAAC;YAAA;cAAA,OAAAlC,QAAA,CAAAE,CAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IACjB;EACF,CAAC;EAED4C,OAAO,EAAE;IACPtB,WAAW,WAAXA,WAAWA,CAACe,KAAK,EAAEQ,UAAU,EAAEC,UAAU,EAAE;MACzC,IAAIzB,QAAO,GAAIgB,KAAI;MACnBQ,UAAU,CAACJ,OAAO,CAAC,UAACM,KAAK,EAAEC,KAAK,EAAK;QACnC3B,QAAO,GAAIA,QAAQ,CAACR,OAAO,CAAC,IAAI0B,MAAM,CAACQ,KAAK,EAAE,IAAI,CAAC,EAAED,UAAU,CAACE,KAAK,CAAC;MACxE,CAAC;MACD,OAAO3B,QAAO;IAChB,CAAC;IAEDW,YAAY,WAAZA,YAAYA,CAAC3B,GAAG,EAAEc,QAAQ,EAAE;MAAA,IAAA8B,MAAA;MAC1B,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAM,EAAK;QAC5B,IAAMC,GAAE,GAAI,IAAIC,cAAc,CAAC;QAC/BD,GAAG,CAACE,kBAAiB,GAAI,YAAM;UAC7B,IAAIF,GAAG,CAACG,UAAS,KAAM,KAAKH,GAAG,CAACI,MAAK,KAAM,GAAG,EAAE;YAC9CP,MAAI,CAAC9B,QAAQ,IAAIiC,GAAG,CAACK,YAAY,CAAC5C,OAAO,CAAC,mBAAmB,EAAE,EAAE;YACjEsC,OAAO,CAAC;UACV;QACF;QACAC,GAAG,CAACM,IAAI,CAAC,KAAK,EAAErD,GAAG;QACnB+C,GAAG,CAACO,IAAI,CAAC;MACX,CAAC;IACH,CAAC;IAED/C,eAAe,WAAfA,eAAeA,CAAC3B,KAAK,EAAE;MACrB,IAAM2E,SAAQ,GAAI,SAAZA,SAAQA,CAAKb,KAAK,EAAEc,IAAI,EAAK;QACjC,IAAIC,GAAE,GAAIC,QAAQ,CAAChB,KAAK,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACxC,IAAI+B,KAAI,GAAID,QAAQ,CAAChB,KAAK,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QAC1C,IAAIgC,IAAG,GAAIF,QAAQ,CAAChB,KAAK,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QAEzC,IAAI4B,IAAG,KAAM,CAAC,EAAE;UAAE;UAChB,OAAO,CAACC,GAAG,EAAEE,KAAK,EAAEC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG;QACpC,OAAO;UACLJ,GAAE,IAAKK,IAAI,CAACC,KAAK,CAACP,IAAG,IAAK,GAAE,GAAIC,GAAG,CAAC;UACpCE,KAAI,IAAKG,IAAI,CAACC,KAAK,CAACP,IAAG,IAAK,GAAE,GAAIG,KAAK,CAAC;UACxCC,IAAG,IAAKE,IAAI,CAACC,KAAK,CAACP,IAAG,IAAK,GAAE,GAAII,IAAI,CAAC;UAEtCH,GAAE,GAAIA,GAAG,CAACO,QAAQ,CAAC,EAAE;UACrBL,KAAI,GAAIA,KAAK,CAACK,QAAQ,CAAC,EAAE;UACzBJ,IAAG,GAAIA,IAAI,CAACI,QAAQ,CAAC,EAAE;UAEvB,WAAAtC,MAAA,CAAW+B,GAAG,EAAA/B,MAAA,CAAGiC,KAAK,EAAAjC,MAAA,CAAGkC,IAAI;QAC/B;MACF;MAEA,IAAMK,UAAS,GAAI,SAAbA,UAASA,CAAKvB,KAAK,EAAEwB,KAAK,EAAK;QACnC,IAAIT,GAAE,GAAIC,QAAQ,CAAChB,KAAK,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QACxC,IAAI+B,KAAI,GAAID,QAAQ,CAAChB,KAAK,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QAC1C,IAAIgC,IAAG,GAAIF,QAAQ,CAAChB,KAAK,CAACd,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;QAEzC6B,GAAE,GAAIK,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIG,KAAK,IAAIT,GAAG;QAClCE,KAAI,GAAIG,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIG,KAAK,IAAIP,KAAK;QACtCC,IAAG,GAAIE,IAAI,CAACC,KAAK,CAAC,CAAC,IAAIG,KAAK,IAAIN,IAAI;QAEpCH,GAAE,GAAIA,GAAG,CAACO,QAAQ,CAAC,EAAE;QACrBL,KAAI,GAAIA,KAAK,CAACK,QAAQ,CAAC,EAAE;QACzBJ,IAAG,GAAIA,IAAI,CAACI,QAAQ,CAAC,EAAE;QAEvB,WAAAtC,MAAA,CAAW+B,GAAG,EAAA/B,MAAA,CAAGiC,KAAK,EAAAjC,MAAA,CAAGkC,IAAI;MAC/B;MAEA,IAAMO,QAAO,GAAI,CAACvF,KAAK;MACvB,KAAK,IAAIwF,CAAA,GAAI,CAAC,EAAEA,CAAA,IAAK,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3BD,QAAQ,CAACE,IAAI,CAACd,SAAS,CAAC3E,KAAK,EAAE0F,MAAM,CAAC,CAACF,CAAA,GAAI,EAAE,EAAEG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D;MACAJ,QAAQ,CAACE,IAAI,CAACJ,UAAU,CAACrF,KAAK,EAAE,GAAG,CAAC;MACpC,OAAOuF,QAAO;IAChB;EACF;AACF", "ignoreList": []}]}