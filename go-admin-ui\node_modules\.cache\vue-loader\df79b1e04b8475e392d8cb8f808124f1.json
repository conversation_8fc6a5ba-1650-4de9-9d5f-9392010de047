{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1753924830213}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCmltcG9ydCBMb2dvIGZyb20gJy4vTG9nbycNCmltcG9ydCBTaWRlYmFySXRlbSBmcm9tICcuL1NpZGViYXJJdGVtJw0KaW1wb3J0IHZhcmlhYmxlcyBmcm9tICdAL3N0eWxlcy92YXJpYWJsZXMuc2NzcycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFNpZGViYXJJdGVtLCBMb2dvIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycyhbDQogICAgICAnc2lkZWJhclJvdXRlcnMnLA0KICAgICAgJ3NpZGViYXInDQogICAgXSksDQogICAgYWN0aXZlTWVudSgpIHsNCiAgICAgIGNvbnN0IHJvdXRlID0gdGhpcy4kcm91dGUNCiAgICAgIGNvbnN0IHsgbWV0YSwgcGF0aCB9ID0gcm91dGUNCiAgICAgIC8vIGlmIHNldCBwYXRoLCB0aGUgc2lkZWJhciB3aWxsIGhpZ2hsaWdodCB0aGUgcGF0aCB5b3Ugc2V0DQogICAgICBpZiAobWV0YS5hY3RpdmVNZW51KSB7DQogICAgICAgIHJldHVybiBtZXRhLmFjdGl2ZU1lbnUNCiAgICAgIH0NCiAgICAgIHJldHVybiBwYXRoDQogICAgfSwNCiAgICBzaG93TG9nbygpIHsNCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaWRlYmFyTG9nbw0KICAgIH0sDQogICAgdmFyaWFibGVzKCkgew0KICAgICAgcmV0dXJuIHZhcmlhYmxlcw0KICAgIH0sDQogICAgaXNDb2xsYXBzZSgpIHsNCiAgICAgIHJldHVybiAhdGhpcy5zaWRlYmFyLm9wZW5lZA0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCg0KICB9LA0KICBtZXRob2RzOiB7DQoNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue"], "names": [], "mappings": ";AA2BA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAEV,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;EAET;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/Sidebar/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :class=\"{'has-logo':showLogo}\">\r\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\r\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\r\n      <el-menu\r\n        :default-active=\"activeMenu\"\r\n        :collapse=\"isCollapse\"\r\n        :background-color=\" $store.state.settings.themeStyle === 'light' ? variables.menuLightBg : variables.menuBg\"\r\n        :text-color=\"$store.state.settings.themeStyle === 'light' ? 'rgba(0,0,0,.65)' : '#fff'\"\r\n        :active-text-color=\"$store.state.settings.theme\"\r\n        :unique-opened=\"true\"\r\n        :collapse-transition=\"true\"\r\n        mode=\"vertical\"\r\n      >\r\n        <sidebar-item\r\n          v-for=\"(route) in sidebarRouters\"\r\n          :key=\"route.path\"\r\n          :item=\"route\"\r\n          :base-path=\"route.path\"\r\n        />\r\n\r\n      </el-menu>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Logo from './Logo'\r\nimport SidebarItem from './SidebarItem'\r\nimport variables from '@/styles/variables.scss'\r\n\r\nexport default {\r\n  components: { SidebarItem, Logo },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebarRouters',\r\n      'sidebar'\r\n    ]),\r\n    activeMenu() {\r\n      const route = this.$route\r\n      const { meta, path } = route\r\n      // if set path, the sidebar will highlight the path you set\r\n      if (meta.activeMenu) {\r\n        return meta.activeMenu\r\n      }\r\n      return path\r\n    },\r\n    showLogo() {\r\n      return this.$store.state.settings.sidebarLogo\r\n    },\r\n    variables() {\r\n      return variables\r\n    },\r\n    isCollapse() {\r\n      return !this.sidebar.opened\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n\r\n  }\r\n}\r\n</script>\r\n"]}]}