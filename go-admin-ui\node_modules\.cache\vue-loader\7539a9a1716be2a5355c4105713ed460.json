{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue?vue&type=style&index=0&id=9792f17c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue", "mtime": 1753924830453}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5saXN0LWdyb3VwLWl0ZW17DQogICAgcGFkZGluZzogMThweCAwOw0KICB9DQogIC5zdmctaWNvbnsNCiAgICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue"], "names": [], "mappings": ";EAkHE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACjB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"6\" :xs=\"24\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>个人信息</span>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-center\">\r\n                <userAvatar :user=\"user\" />\r\n              </div>\r\n              <ul class=\"list-group list-group-striped\">\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />用户名称\r\n                  <div class=\"pull-right\">{{ user.username }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"phone\" />手机号码\r\n                  <div class=\"pull-right\">{{ user.phone }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"email\" />用户邮箱\r\n                  <div class=\"pull-right\">{{ user.email }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"tree\" />所属部门\r\n                  <div class=\"pull-right\">{{ deptName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"peoples\" />所属角色\r\n                  <div class=\"pull-right\">{{ roleName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"date\" />创建日期\r\n                  <div class=\"pull-right\">{{ user.createdAt }}</div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <el-card>\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>基本资料</span>\r\n            </div>\r\n            <el-tabs v-model=\"activeTab\">\r\n              <el-tab-pane label=\"基本资料\" name=\"userinfo\">\r\n                <userInfo :user=\"user\" />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\r\n                <resetPwd :user=\"user\" />\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport userAvatar from './userAvatar'\r\nimport userInfo from './userInfo'\r\nimport resetPwd from './resetPwd'\r\nimport { getUserProfile } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  name: 'Profile',\r\n  components: { userAvatar, userInfo, resetPwd },\r\n  data() {\r\n    return {\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      deptGroup: {},\r\n      activeTab: 'userinfo',\r\n      roleIds: undefined,\r\n      postIds: undefined,\r\n      roleName: undefined,\r\n      postName: undefined,\r\n      dept: {},\r\n      deptName: undefined\r\n    }\r\n  },\r\n  created() {\r\n    this.getUser()\r\n  },\r\n  methods: {\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data.user\r\n        this.roleIds = response.data.user.roleIds\r\n        this.roleGroup = response.data.roles\r\n\r\n        if (this.roleIds[0]) {\r\n          for (const key in this.roleGroup) {\r\n            if (this.roleIds[0] === this.roleGroup[key].roleId) {\r\n              this.roleName = this.roleGroup[key].roleName\r\n            }\r\n          }\r\n        } else {\r\n          this.roleName = '暂无'\r\n        }\r\n        this.dept = response.data.user.dept\r\n        this.deptName = this.dept.deptName\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .list-group-item{\r\n    padding: 18px 0;\r\n  }\r\n  .svg-icon{\r\n    margin-right: 5px;\r\n  }\r\n</style>\r\n"]}]}