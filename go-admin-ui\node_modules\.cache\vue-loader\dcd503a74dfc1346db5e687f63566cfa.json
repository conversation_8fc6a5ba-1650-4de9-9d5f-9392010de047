{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue?vue&type=style&index=0&id=c6aa0552&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue", "mtime": 1753924830053}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5jaGFydC1taW5pLXByb2dyZXNzIHsNCiAgICBwYWRkaW5nOiA1cHggMDsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgd2lkdGg6IDEwMCU7DQogICAgLnRhcmdldCB7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IDA7DQogICAgICBib3R0b206IDA7DQogICAgICBzcGFuIHsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMTAwcHg7DQogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgdG9wOiAwOw0KICAgICAgICBsZWZ0OiAwOw0KICAgICAgICBoZWlnaHQ6IDRweDsNCiAgICAgICAgd2lkdGg6IDJweDsNCiAgICAgICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgICAgICB0b3A6IGF1dG87DQogICAgICAgICAgYm90dG9tOiAwOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICAgIC5wcm9ncmVzcy13cmFwcGVyIHsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICAucHJvZ3Jlc3Mgew0KICAgICAgICB0cmFuc2l0aW9uOiBhbGwgLjRzIGN1YmljLWJlemllciguMDgsLjgyLC4xNywxKSAwczsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMXB4IDAgMCAxcHg7DQogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMxODkwZmY7DQogICAgICAgIHdpZHRoOiAwOw0KICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICB9DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue"], "names": [], "mappings": ";EAqCE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACX;MACF;IACF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;IACF;EACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/MiniProgress/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"chart-mini-progress\">\r\n    <div class=\"target\" :style=\"{ left: target + '%'}\">\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n    </div>\r\n    <div class=\"progress-wrapper\">\r\n      <div class=\"progress\" :style=\"{ backgroundColor: color, width: percentage + '%', height: height }\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MiniProgress',\r\n  props: {\r\n    target: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '10px'\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: '#13C2C2'\r\n    },\r\n    percentage: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-mini-progress {\r\n    padding: 5px 0;\r\n    position: relative;\r\n    width: 100%;\r\n    .target {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      span {\r\n        border-radius: 100px;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        height: 4px;\r\n        width: 2px;\r\n        &:last-child {\r\n          top: auto;\r\n          bottom: 0;\r\n        }\r\n      }\r\n    }\r\n    .progress-wrapper {\r\n      background-color: #f5f5f5;\r\n      position: relative;\r\n      .progress {\r\n        transition: all .4s cubic-bezier(.08,.82,.17,1) 0s;\r\n        border-radius: 1px 0 0 1px;\r\n        background-color: #1890ff;\r\n        width: 0;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}