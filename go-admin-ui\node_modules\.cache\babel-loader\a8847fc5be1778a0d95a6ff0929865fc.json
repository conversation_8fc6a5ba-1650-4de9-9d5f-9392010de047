{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\index.vue", "mtime": 1753924830218}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["RightPanel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Settings", "Sidebar", "TagsView", "ResizeMixin", "mapState", "variables", "name", "components", "mixins", "computed", "_objectSpread", "sidebar", "state", "app", "device", "showSettings", "settings", "needTagsView", "tagsView", "fixedHeader", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "methods", "handleClickOutside", "$store", "dispatch"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': $store.state.settings.theme}\">\r\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\r\n    <sidebar class=\"sidebar-container\" :style=\"{ backgroundColor: $store.state.settings.themeStyle === 'dark' ? variables.menuBg : variables.menuLightBg }\" />\r\n    <div :class=\"{hasTagsView:needTagsView}\" class=\"main-container\">\r\n      <div :class=\"{'fixed-header':fixedHeader}\">\r\n        <navbar />\r\n        <tags-view v-if=\"needTagsView\" />\r\n      </div>\r\n      <app-main />\r\n      <right-panel v-if=\"showSettings\">\r\n        <settings />\r\n      </right-panel>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RightPanel from '@/components/RightPanel'\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    RightPanel,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      showSettings: state => state.settings.showSettings,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/styles/mixin.scss\";\r\n  @import \"~@/styles/variables.scss\";\r\n\r\n  .app-wrapper {\r\n    @include clearfix;\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    &.mobile.openSidebar {\r\n      position: fixed;\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .drawer-bg {\r\n    background: #000;\r\n    opacity: 0.3;\r\n    width: 100%;\r\n    top: 0;\r\n    height: 100%;\r\n    position: absolute;\r\n    z-index: 999;\r\n  }\r\n\r\n  .fixed-header {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 9;\r\n    width: calc(100% - #{$sideBarWidth});\r\n    transition: width 0.28s;\r\n  }\r\n\r\n  .hideSidebar .fixed-header {\r\n    width: calc(100% - 54px)\r\n  }\r\n\r\n  .mobile .fixed-header {\r\n    width: 100%;\r\n  }\r\n</style>\r\n"], "mappings": ";AAkBA,OAAOA,UAAS,MAAO,yBAAwB;AAC/C,SAASC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAO,QAAS,cAAa;AAC1E,OAAOC,WAAU,MAAO,uBAAsB;AAC9C,SAASC,QAAO,QAAS,MAAK;AAC9B,OAAOC,UAAQ,MAAO,yBAAwB;AAE9C,eAAe;EACbC,IAAI,EAAE,QAAQ;EACdC,UAAU,EAAE;IACVT,OAAO,EAAPA,OAAO;IACPC,MAAM,EAANA,MAAM;IACNF,UAAU,EAAVA,UAAU;IACVG,QAAQ,EAARA,QAAQ;IACRC,OAAO,EAAPA,OAAO;IACPC,QAAO,EAAPA;EACF,CAAC;EACDM,MAAM,EAAE,CAACL,WAAW,CAAC;EACrBM,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHN,QAAQ,CAAC;IACVO,OAAO,EAAE,SAATA,OAAOA,CAAEC,KAAI;MAAA,OAAKA,KAAK,CAACC,GAAG,CAACF,OAAO;IAAA;IACnCG,MAAM,EAAE,SAARA,MAAMA,CAAEF,KAAI;MAAA,OAAKA,KAAK,CAACC,GAAG,CAACC,MAAM;IAAA;IACjCC,YAAY,EAAE,SAAdA,YAAYA,CAAEH,KAAI;MAAA,OAAKA,KAAK,CAACI,QAAQ,CAACD,YAAY;IAAA;IAClDE,YAAY,EAAE,SAAdA,YAAYA,CAAEL,KAAI;MAAA,OAAKA,KAAK,CAACI,QAAQ,CAACE,QAAQ;IAAA;IAC9CC,WAAW,EAAE,SAAbA,WAAWA,CAAEP,KAAI;MAAA,OAAKA,KAAK,CAACI,QAAQ,CAACG,WAAU;IAAA;EACjD,CAAC,CAAC;IACFC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,OAAO;QACLC,WAAW,EAAE,CAAC,IAAI,CAACV,OAAO,CAACW,MAAM;QACjCC,WAAW,EAAE,IAAI,CAACZ,OAAO,CAACW,MAAM;QAChCE,gBAAgB,EAAE,IAAI,CAACb,OAAO,CAACa,gBAAgB;QAC/CC,MAAM,EAAE,IAAI,CAACX,MAAK,KAAM;MAC1B;IACF,CAAC;IACDT,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,OAAOA,UAAQ;IACjB;EAAA,EACD;EACDqB,OAAO,EAAE;IACPC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,kBAAkB,EAAE;QAAEL,gBAAgB,EAAE;MAAM,CAAC;IACtE;EACF;AACF", "ignoreList": []}]}