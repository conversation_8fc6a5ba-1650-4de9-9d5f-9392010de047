{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\costum.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\costum.js", "mtime": 1753924830246}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "time", "pattern", "arguments", "length", "indexOf", "format", "date", "_typeof", "test", "parseInt", "toString", "Date", "formatObj", "y", "getFullYear", "m", "getMonth", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "a", "getDay", "time_str", "replace", "result", "key", "value", "resetForm", "refName", "$refs", "resetFields", "addDateRange", "params", "date<PERSON><PERSON><PERSON>", "search", "beginTime", "endTime", "selectDictLabel", "datas", "actions", "Object", "keys", "map", "push", "label", "join", "selectItemsLabel", "sprintf", "str", "args", "flag", "arg", "praseStrEmpty"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/costum.js"], "sourcesContent": ["\r\n// 日期格式化\r\nexport function parseTime(time, pattern) {\r\n  if (arguments.length === 0 || !time) {\r\n    return null\r\n  }\r\n  if (time.indexOf('01-01-01') > -1) {\r\n    return '-'\r\n  }\r\n  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'\r\n  let date\r\n  if (typeof time === 'object') {\r\n    date = time\r\n  } else {\r\n    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {\r\n      time = parseInt(time)\r\n    }\r\n    if ((typeof time === 'number') && (time.toString().length === 10)) {\r\n      time = time * 1000\r\n    }\r\n    date = new Date(time)\r\n  }\r\n  const formatObj = {\r\n    y: date.getFullYear(),\r\n    m: date.getMonth() + 1,\r\n    d: date.getDate(),\r\n    h: date.getHours(),\r\n    i: date.getMinutes(),\r\n    s: date.getSeconds(),\r\n    a: date.getDay()\r\n  }\r\n  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {\r\n    let value = formatObj[key]\r\n    // Note: getDay() returns 0 on Sunday\r\n    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }\r\n    if (result.length > 0 && value < 10) {\r\n      value = '0' + value\r\n    }\r\n    return value || 0\r\n  })\r\n  return time_str\r\n}\r\n\r\n// 表单重置\r\nexport function resetForm(refName) {\r\n  if (this.$refs[refName]) {\r\n    this.$refs[refName].resetFields()\r\n  }\r\n}\r\n\r\n// 添加日期范围\r\nexport function addDateRange(params, dateRange) {\r\n  var search = params\r\n  search.beginTime = ''\r\n  search.endTime = ''\r\n  if (dateRange != null && dateRange !== '') {\r\n    search.beginTime = this.dateRange[0]\r\n    search.endTime = this.dateRange[1]\r\n  }\r\n  return search\r\n}\r\n\r\n// 回显数据字典\r\nexport function selectDictLabel(datas, value) {\r\n  var actions = []\r\n  Object.keys(datas).map((key) => {\r\n    if (datas[key].value === ('' + value)) {\r\n      actions.push(datas[key].label)\r\n      return false\r\n    }\r\n  })\r\n  return actions.join('')\r\n}\r\n\r\nexport function selectItemsLabel(datas, value) {\r\n  var actions = []\r\n  Object.keys(datas).map((key) => {\r\n    if (datas[key].key === ('' + value)) {\r\n      actions.push(datas[key].value)\r\n      return false\r\n    }\r\n  })\r\n  return actions.join('')\r\n}\r\n\r\n// 字符串格式化(%s )\r\nexport function sprintf(str) {\r\n  var args = arguments; var flag = true; var i = 1\r\n  str = str.replace(/%s/g, function() {\r\n    var arg = args[i++]\r\n    if (typeof arg === 'undefined') {\r\n      flag = false\r\n      return ''\r\n    }\r\n    return arg\r\n  })\r\n  return flag ? str : ''\r\n}\r\n\r\n// 转换字符串，undefined,null等转化为\"\"\r\nexport function praseStrEmpty(str) {\r\n  if (!str || str === 'undefined' || str === 'null') {\r\n    return ''\r\n  }\r\n  return str\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AACA;AACA,OAAO,SAASA,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,IAAI,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAIA,IAAI,CAACI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;IACjC,OAAO,GAAG;EACZ;EACA,IAAMC,MAAM,GAAGJ,OAAO,IAAI,yBAAyB;EACnD,IAAIK,IAAI;EACR,IAAIC,OAAA,CAAOP,IAAI,MAAK,QAAQ,EAAE;IAC5BM,IAAI,GAAGN,IAAI;EACb,CAAC,MAAM;IACL,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAM,UAAU,CAACQ,IAAI,CAACR,IAAI,CAAE,EAAE;MACzDA,IAAI,GAAGS,QAAQ,CAACT,IAAI,CAAC;IACvB;IACA,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAMA,IAAI,CAACU,QAAQ,CAAC,CAAC,CAACP,MAAM,KAAK,EAAG,EAAE;MACjEH,IAAI,GAAGA,IAAI,GAAG,IAAI;IACpB;IACAM,IAAI,GAAG,IAAIK,IAAI,CAACX,IAAI,CAAC;EACvB;EACA,IAAMY,SAAS,GAAG;IAChBC,CAAC,EAAEP,IAAI,CAACQ,WAAW,CAAC,CAAC;IACrBC,CAAC,EAAET,IAAI,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC;IACtBC,CAAC,EAAEX,IAAI,CAACY,OAAO,CAAC,CAAC;IACjBC,CAAC,EAAEb,IAAI,CAACc,QAAQ,CAAC,CAAC;IAClBC,CAAC,EAAEf,IAAI,CAACgB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEjB,IAAI,CAACkB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEnB,IAAI,CAACoB,MAAM,CAAC;EACjB,CAAC;EACD,IAAMC,QAAQ,GAAGtB,MAAM,CAACuB,OAAO,CAAC,qBAAqB,EAAE,UAACC,MAAM,EAAEC,GAAG,EAAK;IACtE,IAAIC,KAAK,GAAGnB,SAAS,CAACkB,GAAG,CAAC;IAC1B;IACA,IAAIA,GAAG,KAAK,GAAG,EAAE;MAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAC;IAAC;IACrE,IAAIF,MAAM,CAAC1B,MAAM,GAAG,CAAC,IAAI4B,KAAK,GAAG,EAAE,EAAE;MACnCA,KAAK,GAAG,GAAG,GAAGA,KAAK;IACrB;IACA,OAAOA,KAAK,IAAI,CAAC;EACnB,CAAC,CAAC;EACF,OAAOJ,QAAQ;AACjB;;AAEA;AACA,OAAO,SAASK,SAASA,CAACC,OAAO,EAAE;EACjC,IAAI,IAAI,CAACC,KAAK,CAACD,OAAO,CAAC,EAAE;IACvB,IAAI,CAACC,KAAK,CAACD,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EACnC;AACF;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAC9C,IAAIC,MAAM,GAAGF,MAAM;EACnBE,MAAM,CAACC,SAAS,GAAG,EAAE;EACrBD,MAAM,CAACE,OAAO,GAAG,EAAE;EACnB,IAAIH,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,EAAE,EAAE;IACzCC,MAAM,CAACC,SAAS,GAAG,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;IACpCC,MAAM,CAACE,OAAO,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC;EACpC;EACA,OAAOC,MAAM;AACf;;AAEA;AACA,OAAO,SAASG,eAAeA,CAACC,KAAK,EAAEZ,KAAK,EAAE;EAC5C,IAAIa,OAAO,GAAG,EAAE;EAChBC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAAC,UAACjB,GAAG,EAAK;IAC9B,IAAIa,KAAK,CAACb,GAAG,CAAC,CAACC,KAAK,KAAM,EAAE,GAAGA,KAAM,EAAE;MACrCa,OAAO,CAACI,IAAI,CAACL,KAAK,CAACb,GAAG,CAAC,CAACmB,KAAK,CAAC;MAC9B,OAAO,KAAK;IACd;EACF,CAAC,CAAC;EACF,OAAOL,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC;AACzB;AAEA,OAAO,SAASC,gBAAgBA,CAACR,KAAK,EAAEZ,KAAK,EAAE;EAC7C,IAAIa,OAAO,GAAG,EAAE;EAChBC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAAC,UAACjB,GAAG,EAAK;IAC9B,IAAIa,KAAK,CAACb,GAAG,CAAC,CAACA,GAAG,KAAM,EAAE,GAAGC,KAAM,EAAE;MACnCa,OAAO,CAACI,IAAI,CAACL,KAAK,CAACb,GAAG,CAAC,CAACC,KAAK,CAAC;MAC9B,OAAO,KAAK;IACd;EACF,CAAC,CAAC;EACF,OAAOa,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC;AACzB;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAIC,IAAI,GAAGpD,SAAS;EAAE,IAAIqD,IAAI,GAAG,IAAI;EAAE,IAAIlC,CAAC,GAAG,CAAC;EAChDgC,GAAG,GAAGA,GAAG,CAACzB,OAAO,CAAC,KAAK,EAAE,YAAW;IAClC,IAAI4B,GAAG,GAAGF,IAAI,CAACjC,CAAC,EAAE,CAAC;IACnB,IAAI,OAAOmC,GAAG,KAAK,WAAW,EAAE;MAC9BD,IAAI,GAAG,KAAK;MACZ,OAAO,EAAE;IACX;IACA,OAAOC,GAAG;EACZ,CAAC,CAAC;EACF,OAAOD,IAAI,GAAGF,GAAG,GAAG,EAAE;AACxB;;AAEA;AACA,OAAO,SAASI,aAAaA,CAACJ,GAAG,EAAE;EACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,MAAM,EAAE;IACjD,OAAO,EAAE;EACX;EACA,OAAOA,GAAG;AACZ", "ignoreList": []}]}