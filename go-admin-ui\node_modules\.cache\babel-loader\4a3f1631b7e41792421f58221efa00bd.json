{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\main.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\main.js", "mtime": 1753924830220}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Cookies", "Element", "VueCodemirror", "use", "App", "store", "router", "permission", "getDicts", "getItems", "setItems", "getConfigKey", "parseTime", "resetForm", "addDateRange", "selectDictLabel", "selectItemsLabel", "Viser", "filters", "Pagination", "BasicLayout", "VueParticles", "prototype", "component", "msgSuccess", "msg", "$message", "showClose", "message", "type", "msgError", "msgInfo", "info", "size", "get", "VueDND", "console", "Object", "keys", "for<PERSON>ach", "key", "filter", "config", "productionTip", "el", "render", "h"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\n\r\nimport Cookies from 'js-cookie'\r\n\r\nimport 'normalize.css/normalize.css' // a modern alternative to CSS resets\r\n\r\nimport Element from 'element-ui'\r\nimport './styles/element-variables.scss'\r\n\r\nimport '@/styles/index.scss' // global css\r\nimport '@/styles/admin.scss'\r\n\r\nimport VueCodemirror from 'vue-codemirror'\r\nimport 'codemirror/lib/codemirror.css'\r\nVue.use(VueCodemirror)\r\n\r\nimport App from './App'\r\nimport store from './store'\r\nimport router from './router'\r\nimport permission from './directive/permission'\r\n\r\nimport { getDicts } from '@/api/admin/dict/data'\r\nimport { getItems, setItems } from '@/api/table'\r\nimport { getConfigKey } from '@/api/admin/sys-config'\r\nimport { parseTime, resetForm, addDateRange, selectDictLabel, /* download,*/ selectItemsLabel } from '@/utils/costum'\r\n\r\nimport './icons' // icon\r\nimport './permission' // permission control\r\nimport './utils/error-log' // error log\r\n\r\nimport Viser from 'viser-vue'\r\nVue.use(Viser)\r\n\r\nimport * as filters from './filters' // global filters\r\n\r\nimport Pagination from '@/components/Pagination'\r\nimport BasicLayout from '@/layout/BasicLayout'\r\n\r\nimport VueParticles from 'vue-particles'\r\nVue.use(VueParticles)\r\n\r\nimport '@/utils/dialog'\r\n\r\n// 全局方法挂载\r\nVue.prototype.getDicts = getDicts\r\nVue.prototype.getItems = getItems\r\nVue.prototype.setItems = setItems\r\nVue.prototype.getConfigKey = getConfigKey\r\nVue.prototype.parseTime = parseTime\r\nVue.prototype.resetForm = resetForm\r\nVue.prototype.addDateRange = addDateRange\r\nVue.prototype.selectDictLabel = selectDictLabel\r\nVue.prototype.selectItemsLabel = selectItemsLabel\r\n// Vue.prototype.download = download\r\n\r\n// 全局组件挂载\r\nVue.component('Pagination', Pagination)\r\nVue.component('BasicLayout', BasicLayout)\r\n\r\nVue.prototype.msgSuccess = function(msg) {\r\n  this.$message({ showClose: true, message: msg, type: 'success' })\r\n}\r\n\r\nVue.prototype.msgError = function(msg) {\r\n  this.$message({ showClose: true, message: msg, type: 'error' })\r\n}\r\n\r\nVue.prototype.msgInfo = function(msg) {\r\n  this.$message.info(msg)\r\n}\r\n\r\nVue.use(permission)\r\n\r\nVue.use(Element, {\r\n  size: Cookies.get('size') || 'medium' // set element-ui default size\r\n})\r\n\r\nimport VueDND from 'awe-dnd'\r\nVue.use(VueDND)\r\n\r\nimport 'remixicon/fonts/remixicon.css'\r\n\r\nconsole.info(`欢迎使用go-admin，谢谢您对我们的支持，在使用过程中如果有什么问题，\r\n请访问https://github.com/go-admin-team/go-admin 或者\r\n https://github.com/go-admin-team/go-admin-ui 向我们反馈，\r\n 谢谢！`)\r\n\r\n// register global utility filters\r\nObject.keys(filters).forEach(key => {\r\n  Vue.filter(key, filters[key])\r\n})\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  el: '#app',\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n})\r\n"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AAErB,OAAOC,OAAO,MAAM,WAAW;AAE/B,OAAO,6BAA6B,EAAC;;AAErC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAO,iCAAiC;AAExC,OAAO,qBAAqB,EAAC;AAC7B,OAAO,qBAAqB;AAE5B,OAAOC,aAAa,MAAM,gBAAgB;AAC1C,OAAO,+BAA+B;AACtCH,GAAG,CAACI,GAAG,CAACD,aAAa,CAAC;AAEtB,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,wBAAwB;AAE/C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,aAAa;AAChD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,eAAe,EAAE,cAAeC,gBAAgB,QAAQ,gBAAgB;AAErH,OAAO,SAAS,EAAC;AACjB,OAAO,cAAc,EAAC;AACtB,OAAO,mBAAmB,EAAC;;AAE3B,OAAOC,KAAK,MAAM,WAAW;AAC7BlB,GAAG,CAACI,GAAG,CAACc,KAAK,CAAC;AAEd,OAAO,KAAKC,OAAO,MAAM,WAAW,EAAC;;AAErC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,sBAAsB;AAE9C,OAAOC,YAAY,MAAM,eAAe;AACxCtB,GAAG,CAACI,GAAG,CAACkB,YAAY,CAAC;AAErB,OAAO,gBAAgB;;AAEvB;AACAtB,GAAG,CAACuB,SAAS,CAACd,QAAQ,GAAGA,QAAQ;AACjCT,GAAG,CAACuB,SAAS,CAACb,QAAQ,GAAGA,QAAQ;AACjCV,GAAG,CAACuB,SAAS,CAACZ,QAAQ,GAAGA,QAAQ;AACjCX,GAAG,CAACuB,SAAS,CAACX,YAAY,GAAGA,YAAY;AACzCZ,GAAG,CAACuB,SAAS,CAACV,SAAS,GAAGA,SAAS;AACnCb,GAAG,CAACuB,SAAS,CAACT,SAAS,GAAGA,SAAS;AACnCd,GAAG,CAACuB,SAAS,CAACR,YAAY,GAAGA,YAAY;AACzCf,GAAG,CAACuB,SAAS,CAACP,eAAe,GAAGA,eAAe;AAC/ChB,GAAG,CAACuB,SAAS,CAACN,gBAAgB,GAAGA,gBAAgB;AACjD;;AAEA;AACAjB,GAAG,CAACwB,SAAS,CAAC,YAAY,EAAEJ,UAAU,CAAC;AACvCpB,GAAG,CAACwB,SAAS,CAAC,aAAa,EAAEH,WAAW,CAAC;AAEzCrB,GAAG,CAACuB,SAAS,CAACE,UAAU,GAAG,UAASC,GAAG,EAAE;EACvC,IAAI,CAACC,QAAQ,CAAC;IAAEC,SAAS,EAAE,IAAI;IAAEC,OAAO,EAAEH,GAAG;IAAEI,IAAI,EAAE;EAAU,CAAC,CAAC;AACnE,CAAC;AAED9B,GAAG,CAACuB,SAAS,CAACQ,QAAQ,GAAG,UAASL,GAAG,EAAE;EACrC,IAAI,CAACC,QAAQ,CAAC;IAAEC,SAAS,EAAE,IAAI;IAAEC,OAAO,EAAEH,GAAG;IAAEI,IAAI,EAAE;EAAQ,CAAC,CAAC;AACjE,CAAC;AAED9B,GAAG,CAACuB,SAAS,CAACS,OAAO,GAAG,UAASN,GAAG,EAAE;EACpC,IAAI,CAACC,QAAQ,CAACM,IAAI,CAACP,GAAG,CAAC;AACzB,CAAC;AAED1B,GAAG,CAACI,GAAG,CAACI,UAAU,CAAC;AAEnBR,GAAG,CAACI,GAAG,CAACF,OAAO,EAAE;EACfgC,IAAI,EAAEjC,OAAO,CAACkC,GAAG,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC;AACxC,CAAC,CAAC;AAEF,OAAOC,MAAM,MAAM,SAAS;AAC5BpC,GAAG,CAACI,GAAG,CAACgC,MAAM,CAAC;AAEf,OAAO,+BAA+B;AAEtCC,OAAO,CAACJ,IAAI,4WAGP,CAAC;;AAEN;AACAK,MAAM,CAACC,IAAI,CAACpB,OAAO,CAAC,CAACqB,OAAO,CAAC,UAAAC,GAAG,EAAI;EAClCzC,GAAG,CAAC0C,MAAM,CAACD,GAAG,EAAEtB,OAAO,CAACsB,GAAG,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEFzC,GAAG,CAAC2C,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAI5C,GAAG,CAAC;EACN6C,EAAE,EAAE,MAAM;EACVtC,MAAM,EAANA,MAAM;EACND,KAAK,EAALA,KAAK;EACLwC,MAAM,EAAE,SAARA,MAAMA,CAAEC,CAAC;IAAA,OAAIA,CAAC,CAAC1C,GAAG,CAAC;EAAA;AACrB,CAAC,CAAC", "ignoreList": []}]}