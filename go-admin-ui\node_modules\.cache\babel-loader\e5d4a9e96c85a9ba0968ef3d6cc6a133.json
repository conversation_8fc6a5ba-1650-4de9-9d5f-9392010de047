{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Navbar.vue", "mtime": 1753924830206}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mapGetters", "Breadcrumb", "TopNav", "<PERSON><PERSON>", "Screenfull", "Search", "components", "computed", "_objectSpread", "setting", "get", "$store", "state", "settings", "showSettings", "set", "val", "dispatch", "key", "value", "topNav", "methods", "toggleSideBar", "logout", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "location", "reload", "a"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Navbar.vue"], "sourcesContent": ["<template>\r\n  <div class=\"navbar\">\r\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\r\n\r\n    <breadcrumb v-if=\"!topNav\" id=\"breadcrumb-container\" class=\"breadcrumb-container\" />\r\n    <top-nav v-if=\"topNav\" id=\"topmenu-container\" class=\"breadcrumb-container\" />\r\n\r\n    <div class=\"right-menu\">\r\n      <template v-if=\"device!=='mobile'\">\r\n        <search id=\"header-search\" class=\"right-menu-item\" />\r\n\r\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\r\n\r\n      </template>\r\n\r\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"hover\">\r\n        <div class=\"avatar-wrapper\">\r\n          <img :src=\"avatar+'?imageView2/1/w/80/h/80'\" class=\"user-avatar\">\r\n          <i class=\"el-icon-caret-bottom\" />\r\n        </div>\r\n        <el-dropdown-menu slot=\"dropdown\">\r\n          <router-link to=\"/profile/index\">\r\n            <el-dropdown-item>个人中心</el-dropdown-item>\r\n          </router-link>\r\n          <el-dropdown-item divided>\r\n            <span style=\"display:block;\" @click=\"logout\">退出登录</span>\r\n          </el-dropdown-item>\r\n        </el-dropdown-menu>\r\n      </el-dropdown>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport Search from '@/components/HeaderSearch'\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    Search\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    }\r\n\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    async logout() {\r\n      this.$confirm('确定注销并退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('user/LogOut').then(() => {\r\n          location.reload()\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color:transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 30px;\r\n\r\n      .avatar-wrapper {\r\n        margin-top: 5px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 15px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;AAkCA,SAASA,UAAS,QAAS,MAAK;AAChC,OAAOC,UAAS,MAAO,yBAAwB;AAC/C,OAAOC,MAAK,MAAO,qBAAoB;AACvC,OAAOC,SAAQ,MAAO,wBAAuB;AAC7C,OAAOC,UAAS,MAAO,yBAAwB;AAC/C,OAAOC,MAAK,MAAO,2BAA0B;AAE7C,eAAe;EACbC,UAAU,EAAE;IACVL,UAAU,EAAVA,UAAU;IACVC,MAAM,EAANA,MAAM;IACNC,SAAS,EAATA,SAAS;IACTC,UAAU,EAAVA,UAAU;IACVC,MAAK,EAALA;EACF,CAAC;EACDE,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHR,UAAU,CAAC,CACZ,SAAS,EACT,QAAQ,EACR,QAAO,CACR,CAAC;IACFS,OAAO,EAAE;MACPC,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACC,YAAW;MAC/C,CAAC;MACDC,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;QACP,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,wBAAwB,EAAE;UAC7CC,GAAG,EAAE,cAAc;UACnBC,KAAK,EAAEH;QACT,CAAC;MACH;IACF,CAAC;IACDI,MAAM,EAAE;MACNV,GAAG,WAAHA,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACO,MAAK;MACzC;IACF;EAAA,EAED;EACDC,OAAO,EAAE;IACPC,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAACX,MAAM,CAACM,QAAQ,CAAC,mBAAmB;IAC1C,CAAC;IACKM,MAAM,WAANA,MAAMA,CAAA,EAAG;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACbP,KAAI,CAACQ,QAAQ,CAAC,aAAa,EAAE,IAAI,EAAE;gBACjCC,iBAAiB,EAAE,IAAI;gBACvBC,gBAAgB,EAAE,IAAI;gBACtBC,IAAI,EAAE;cACR,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;gBACZZ,KAAI,CAACb,MAAM,CAACM,QAAQ,CAAC,aAAa,CAAC,CAACmB,IAAI,CAAC,YAAM;kBAC7CC,QAAQ,CAACC,MAAM,CAAC;gBAClB,CAAC;cACH,CAAC;YAAA;cAAA,OAAAR,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IACH;EACF;AACF", "ignoreList": []}]}