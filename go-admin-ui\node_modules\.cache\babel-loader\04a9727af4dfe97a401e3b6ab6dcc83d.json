{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1753924830291}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBDaGFydENhcmQgZnJvbSAnQC9jb21wb25lbnRzL0NoYXJ0Q2FyZCc7CmltcG9ydCBUcmVuZCBmcm9tICdAL2NvbXBvbmVudHMvVHJlbmQnOwppbXBvcnQgTWluaUFyZWEgZnJvbSAnQC9jb21wb25lbnRzL01pbmlBcmVhJzsKaW1wb3J0IE1pbmlCYXIgZnJvbSAnQC9jb21wb25lbnRzL01pbmlCYXInOwppbXBvcnQgTWluaVByb2dyZXNzIGZyb20gJ0AvY29tcG9uZW50cy9NaW5pUHJvZ3Jlc3MnOwppbXBvcnQgUmFua0xpc3QgZnJvbSAnQC9jb21wb25lbnRzL1JhbmtMaXN0L2luZGV4JzsKaW1wb3J0IEJhciBmcm9tICdAL2NvbXBvbmVudHMvQmFyLnZ1ZSc7CnZhciBiYXJEYXRhID0gW107CnZhciBiYXJEYXRhMiA9IFtdOwpmb3IgKHZhciBpID0gMDsgaSA8IDEyOyBpICs9IDEpIHsKICBiYXJEYXRhLnB1c2goewogICAgeDogIiIuY29uY2F0KGkgKyAxLCAiXHU2NzA4IiksCiAgICB5OiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMDAwKSArIDIwMAogIH0pOwogIGJhckRhdGEyLnB1c2goewogICAgeDogIiIuY29uY2F0KGkgKyAxLCAiXHU2NzA4IiksCiAgICB5OiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMDAwKSArIDIwMAogIH0pOwp9CnZhciByYW5rTGlzdCA9IFtdOwpmb3IgKHZhciBfaSA9IDA7IF9pIDwgNzsgX2krKykgewogIHJhbmtMaXN0LnB1c2goewogICAgbmFtZTogJ+eZvem5reWymyAnICsgKF9pICsgMSkgKyAnIOWPt+W6lycsCiAgICB0b3RhbDogMTIzNC41NiAtIF9pICogMTAwCiAgfSk7Cn0KZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEYXNoYm9hcmRBZG1pbicsCiAgY29tcG9uZW50czogewogICAgQ2hhcnRDYXJkOiBDaGFydENhcmQsCiAgICBUcmVuZDogVHJlbmQsCiAgICBNaW5pQXJlYTogTWluaUFyZWEsCiAgICBNaW5pQmFyOiBNaW5pQmFyLAogICAgTWluaVByb2dyZXNzOiBNaW5pUHJvZ3Jlc3MsCiAgICBSYW5rTGlzdDogUmFua0xpc3QsCiAgICBCYXI6IEJhcgogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGJhckRhdGE6IGJhckRhdGEsCiAgICAgIGJhckRhdGEyOiBiYXJEYXRhMiwKICAgICAgcmFua0xpc3Q6IHJhbmtMaXN0CiAgICB9OwogIH0sCiAgbWV0aG9kczoge30KfTs="}, {"version": 3, "names": ["ChartCard", "Trend", "MiniArea", "MiniBar", "MiniProgress", "RankList", "Bar", "barData", "barData2", "i", "push", "x", "concat", "y", "Math", "floor", "random", "rankList", "name", "total", "components", "data", "methods"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <el-row :gutter=\"12\">\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"总销售额\" total=\"￥126,560\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <trend flag=\"top\" style=\"margin-right: 16px;\" rate=\"12\">\r\n              <span slot=\"term\">周同比</span>\r\n            </trend>\r\n            <trend flag=\"bottom\" rate=\"11\">\r\n              <span slot=\"term\">日同比</span>\r\n            </trend>\r\n          </div>\r\n          <template slot=\"footer\">日均销售额<span>￥ 234.56</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"访问量\" :total=\"8846\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-area />\r\n          </div>\r\n          <template slot=\"footer\">日访问量<span> {{ '1234' }}</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"支付笔数\" :total=\"6560\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-bar />\r\n          </div>\r\n          <template slot=\"footer\">转化率 <span>60%</span></template>\r\n        </chart-card>\r\n      </el-col>\r\n      <el-col :sm=\"24\" :xs=\"24\" :md=\"6\" :xl=\"6\" :lg=\"6\" :style=\"{ marginBottom: '12px' }\">\r\n        <chart-card title=\"运营活动效果\" total=\"78%\">\r\n          <el-tooltip slot=\"action\" class=\"item\" effect=\"dark\" content=\"指标说明\" placement=\"top-start\">\r\n            <i class=\"el-icon-warning-outline\" />\r\n          </el-tooltip>\r\n          <div>\r\n            <mini-progress color=\"rgb(19, 194, 194)\" :target=\"80\" :percentage=\"78\" height=\"8px\" />\r\n          </div>\r\n          <template slot=\"footer\">\r\n            <trend flag=\"top\" style=\"margin-right: 16px;\" rate=\"12\">\r\n              <span slot=\"term\">同周比</span>\r\n            </trend>\r\n            <trend flag=\"bottom\" rate=\"80\">\r\n              <span slot=\"term\">日环比</span>\r\n            </trend>\r\n          </template>\r\n        </chart-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-card :bordered=\"false\" :body-style=\"{padding: '0'}\">\r\n      <div class=\"salesCard\">\r\n        <el-tabs>\r\n          <el-tab-pane label=\"销售额\">\r\n            <el-row>\r\n              <el-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <bar :list=\"barData\" title=\"销售额排行\" />\r\n              </el-col>\r\n              <el-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\" />\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"访问量\">\r\n            <el-row>\r\n              <el-col :xl=\"16\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <bar :list=\"barData2\" title=\"销售额趋势\" />\r\n              </el-col>\r\n              <el-col :xl=\"8\" :lg=\"12\" :md=\"12\" :sm=\"24\" :xs=\"24\">\r\n                <rank-list title=\"门店销售排行榜\" :list=\"rankList\" />\r\n              </el-col>\r\n            </el-row>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-card>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ChartCard from '@/components/ChartCard'\r\nimport Trend from '@/components/Trend'\r\nimport MiniArea from '@/components/MiniArea'\r\nimport MiniBar from '@/components/MiniBar'\r\nimport MiniProgress from '@/components/MiniProgress'\r\nimport RankList from '@/components/RankList/index'\r\nimport Bar from '@/components/Bar.vue'\r\n\r\nconst barData = []\r\nconst barData2 = []\r\nfor (let i = 0; i < 12; i += 1) {\r\n  barData.push({\r\n    x: `${i + 1}月`,\r\n    y: Math.floor(Math.random() * 1000) + 200\r\n  })\r\n  barData2.push({\r\n    x: `${i + 1}月`,\r\n    y: Math.floor(Math.random() * 1000) + 200\r\n  })\r\n}\r\n\r\nconst rankList = []\r\nfor (let i = 0; i < 7; i++) {\r\n  rankList.push({\r\n    name: '白鹭岛 ' + (i + 1) + ' 号店',\r\n    total: 1234.56 - i * 100\r\n  })\r\n}\r\n\r\nexport default {\r\n  name: 'DashboardAdmin',\r\n  components: {\r\n    ChartCard,\r\n    Trend,\r\n    MiniArea,\r\n    MiniBar,\r\n    MiniProgress,\r\n    RankList,\r\n    Bar\r\n  },\r\n  data() {\r\n    return {\r\n      barData,\r\n      barData2,\r\n      rankList\r\n    }\r\n  },\r\n  methods: {\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 12px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .github-corner {\r\n    position: absolute;\r\n    top: 0;\r\n    border: 0;\r\n    right: 0;\r\n  }\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n::v-deep .el-tabs__item{\r\n   padding-left: 16px!important;\r\n   height: 50px;\r\n   line-height: 50px;\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AA4FA,OAAOA,SAAQ,MAAO,wBAAuB;AAC7C,OAAOC,KAAI,MAAO,oBAAmB;AACrC,OAAOC,QAAO,MAAO,uBAAsB;AAC3C,OAAOC,OAAM,MAAO,sBAAqB;AACzC,OAAOC,YAAW,MAAO,2BAA0B;AACnD,OAAOC,QAAO,MAAO,6BAA4B;AACjD,OAAOC,GAAE,MAAO,sBAAqB;AAErC,IAAMC,OAAM,GAAI,EAAC;AACjB,IAAMC,QAAO,GAAI,EAAC;AAClB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,EAAE,EAAEA,CAAA,IAAK,CAAC,EAAE;EAC9BF,OAAO,CAACG,IAAI,CAAC;IACXC,CAAC,KAAAC,MAAA,CAAKH,CAAA,GAAI,CAAC,WAAG;IACdI,CAAC,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,IAAI,IAAI;EACxC,CAAC;EACDR,QAAQ,CAACE,IAAI,CAAC;IACZC,CAAC,KAAAC,MAAA,CAAKH,CAAA,GAAI,CAAC,WAAG;IACdI,CAAC,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,IAAI,IAAI;EACxC,CAAC;AACH;AAEA,IAAMC,QAAO,GAAI,EAAC;AAClB,KAAK,IAAIR,EAAA,GAAI,CAAC,EAAEA,EAAA,GAAI,CAAC,EAAEA,EAAC,EAAE,EAAE;EAC1BQ,QAAQ,CAACP,IAAI,CAAC;IACZQ,IAAI,EAAE,MAAK,IAAKT,EAAA,GAAI,CAAC,IAAI,KAAK;IAC9BU,KAAK,EAAE,OAAM,GAAIV,EAAA,GAAI;EACvB,CAAC;AACH;AAEA,eAAe;EACbS,IAAI,EAAE,gBAAgB;EACtBE,UAAU,EAAE;IACVpB,SAAS,EAATA,SAAS;IACTC,KAAK,EAALA,KAAK;IACLC,QAAQ,EAARA,QAAQ;IACRC,OAAO,EAAPA,OAAO;IACPC,YAAY,EAAZA,YAAY;IACZC,QAAQ,EAARA,QAAQ;IACRC,GAAE,EAAFA;EACF,CAAC;EACDe,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLd,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA,QAAQ;MACRS,QAAO,EAAPA;IACF;EACF,CAAC;EACDK,OAAO,EAAE,CACT;AACF", "ignoreList": []}]}