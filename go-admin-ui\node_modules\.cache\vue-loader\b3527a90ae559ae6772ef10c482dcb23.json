{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue?vue&type=style&index=0&id=cbc28918&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue", "mtime": 1753924830307}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5lcnJQYWdlLWNvbnRhaW5lciB7DQogICAgd2lkdGg6IDgwMHB4Ow0KICAgIG1heC13aWR0aDogMTAwJTsNCiAgICBtYXJnaW46IDEwMHB4IGF1dG87DQogICAgLnBhbi1iYWNrLWJ0biB7DQogICAgICBiYWNrZ3JvdW5kOiAjMDA4NDg5Ow0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBib3JkZXI6IG5vbmUhaW1wb3J0YW50Ow0KICAgIH0NCiAgICAucGFuLWdpZiB7DQogICAgICBtYXJnaW46IDAgYXV0bzsNCiAgICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgIH0NCiAgICAucGFuLWltZyB7DQogICAgICBkaXNwbGF5OiBibG9jazsNCiAgICAgIG1hcmdpbjogMCBhdXRvOw0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgfQ0KICAgIC50ZXh0LWp1bWJvIHsNCiAgICAgIGZvbnQtc2l6ZTogNjBweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICBjb2xvcjogIzQ4NDg0ODsNCiAgICB9DQogICAgLmxpc3QtdW5zdHlsZWQgew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgbGkgew0KICAgICAgICBwYWRkaW5nLWJvdHRvbTogNXB4Ow0KICAgICAgfQ0KICAgICAgYSB7DQogICAgICAgIGNvbG9yOiAjMDA4NDg5Ow0KICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7DQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue"], "names": [], "mappings": ";EA6DE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrB;MACA,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACF;IACF;EACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/error-page/401.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"errPage-container\">\r\n    <el-button icon=\"el-icon-arrow-left\" class=\"pan-back-btn\" @click=\"back\">\r\n      返回\r\n    </el-button>\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <h1 class=\"text-jumbo text-ginormous\">\r\n          Oops!\r\n        </h1>\r\n        gif来源<a href=\"https://zh.airbnb.com/\" target=\"_blank\">airbnb</a> 页面\r\n        <h2>你没有权限去该页面</h2>\r\n        <h6>如有不满请联系你领导</h6>\r\n        <ul class=\"list-unstyled\">\r\n          <li>或者你可以去:</li>\r\n          <li class=\"link-type\">\r\n            <router-link to=\"/dashboard\">\r\n              回首页\r\n            </router-link>\r\n          </li>\r\n          <li class=\"link-type\">\r\n            <a href=\"https://www.taobao.com/\">随便看看</a>\r\n          </li>\r\n          <li><a href=\"#\" @click.prevent=\"dialogVisible=true\">点我看图</a></li>\r\n        </ul>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"随便看\" :close-on-click-modal=\"false\">\r\n      <img :src=\"ewizardClap\" class=\"pan-img\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport errGif from '@/assets/401_images/401.gif'\r\n\r\nexport default {\r\n  name: 'Page401',\r\n  data() {\r\n    return {\r\n      errGif: errGif + '?' + +new Date(),\r\n      ewizardClap: 'https://wpimg.wallstcn.com/007ef517-bafd-4066-aae4-6883632d9646',\r\n      dialogVisible: false\r\n    }\r\n  },\r\n  methods: {\r\n    back() {\r\n      if (this.$route.query.noGoBack) {\r\n        this.$router.push({ path: '/dashboard' })\r\n      } else {\r\n        this.$router.go(-1)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .errPage-container {\r\n    width: 800px;\r\n    max-width: 100%;\r\n    margin: 100px auto;\r\n    .pan-back-btn {\r\n      background: #008489;\r\n      color: #fff;\r\n      border: none!important;\r\n    }\r\n    .pan-gif {\r\n      margin: 0 auto;\r\n      display: block;\r\n    }\r\n    .pan-img {\r\n      display: block;\r\n      margin: 0 auto;\r\n      width: 100%;\r\n    }\r\n    .text-jumbo {\r\n      font-size: 60px;\r\n      font-weight: 700;\r\n      color: #484848;\r\n    }\r\n    .list-unstyled {\r\n      font-size: 14px;\r\n      li {\r\n        padding-bottom: 5px;\r\n      }\r\n      a {\r\n        color: #008489;\r\n        text-decoration: none;\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}