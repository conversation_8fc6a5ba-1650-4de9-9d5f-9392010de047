{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Account.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Account.vue", "mtime": *************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICB1c2VyOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG5hbWU6ICcnLAogICAgICAgICAgZW1haWw6ICcnCiAgICAgICAgfTsKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgc3VibWl0OiBmdW5jdGlvbiBzdWJtaXQoKSB7CiAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgIG1lc3NhZ2U6ICdVc2VyIGluZm9ybWF0aW9uIGhhcyBiZWVuIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JywKICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgZHVyYXRpb246IDUgKiAxMDAwCiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["props", "user", "type", "Object", "default", "name", "email", "methods", "submit", "$message", "message", "duration"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Account.vue"], "sourcesContent": ["<template>\r\n  <el-form>\r\n    <el-form-item label=\"Name\">\r\n      <el-input v-model.trim=\"user.name\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"Email\">\r\n      <el-input v-model.trim=\"user.email\" />\r\n    </el-form-item>\r\n    <el-form-item>\r\n      <el-button type=\"primary\" @click=\"submit\">Update</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    user: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          name: '',\r\n          email: ''\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$message({\r\n        message: 'User information has been updated successfully',\r\n        type: 'success',\r\n        duration: 5 * 1000\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAeA,eAAe;EACbA,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,SAATA,QAAOA,CAAA,EAAQ;QACb,OAAO;UACLC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,QAAQ,CAAC;QACZC,OAAO,EAAE,gDAAgD;QACzDR,IAAI,EAAE,SAAS;QACfS,QAAQ,EAAE,IAAI;MAChB,CAAC;IACH;EACF;AACF", "ignoreList": []}]}