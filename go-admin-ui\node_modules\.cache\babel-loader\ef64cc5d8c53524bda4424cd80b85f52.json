{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue", "mtime": 1753924830296}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdCYXNpY0luZm9Gb3JtJywKICBwcm9wczogewogICAgaW5mbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBydWxlczogewogICAgICAgIHRhYmxlTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeihqOWQjeensCcsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9LCB7CiAgICAgICAgICBwYXR0ZXJuOiAvXlthLXpcLl9dKiQvZywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJywKICAgICAgICAgIG1lc3NhZ2U6ICflj6rlhYHorrjlsI/lhpnlrZfmr40s5L6L5aaCIHN5c19kZW1vIOagvOW8jycKICAgICAgICB9XSwKICAgICAgICB0YWJsZUNvbW1lbnQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXoj5zljZXlkI3np7AnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgY2xhc3NOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5qih5Z6L5ZCN56ewJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH0sIHsKICAgICAgICAgIHBhdHRlcm46IC9eW0EtWl1bQS16MC05XSokL2csCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicsCiAgICAgICAgICBtZXNzYWdlOiAn5b+F6aG75Lul5aSn5YaZ5a2X5q+N5byA5aS0LOS+i+WmgiBTeXNEZW1vIOagvOW8jycKICAgICAgICB9XSwKICAgICAgICBmdW5jdGlvbkF1dGhvcjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeS9nOiAhScsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9LCB7CiAgICAgICAgICBwYXR0ZXJuOiAvXltBLVphLXpdKyQvLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInLAogICAgICAgICAgbWVzc2FnZTogJ+agoemqjOinhOWImTogIOWPquWFgeiuuOi+k+WFpeWtl+avjSBhLXog5oiW5aSn5YaZIEEtWicKICAgICAgICB9XQogICAgICB9CiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["name", "props", "info", "type", "Object", "default", "data", "rules", "tableName", "required", "message", "trigger", "pattern", "tableComment", "className", "function<PERSON><PERSON>or"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue"], "sourcesContent": ["<template>\r\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tableName\">\r\n          <span slot=\"label\">\r\n            数据表名称\r\n            <el-tooltip content=\"数据库表名称，针对gorm对应的table()使用，⚠️这里必须是蛇形结构\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.tableName\" placeholder=\"请输入表名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tableComment\">\r\n          <span slot=\"label\">\r\n            菜单名称\r\n            <el-tooltip content=\"同步的数据库表名称，生成配置数据时，用作菜单名称\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.tableComment\" placeholder=\"请输入菜单名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"className\">\r\n          <span slot=\"label\">\r\n            结构体模型名称\r\n            <el-tooltip content=\"结构体模型名称，代码中的struct名称定义使用\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.className\" placeholder=\"请输入\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"作者名称\" prop=\"functionAuthor\">\r\n          <el-input v-model=\"info.functionAuthor\" placeholder=\"请输入作者名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isLogicalDelete\">\r\n          <span slot=\"label\">\r\n            是否逻辑删除\r\n            <el-tooltip content=\"目前只支持逻辑删除\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-radio-group v-model=\"info.isLogicalDelete\">\r\n            <el-radio label=\"1\">是</el-radio>\r\n            <el-radio label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item v-if=\"info.isLogicalDelete == '1'\" label=\"逻辑删除字段\" prop=\"logicalDeleteColumn\">\r\n          <el-input v-model=\"info.logicalDeleteColumn\" placeholder=\"请输入\" />\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <el-col :span=\"24\">\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"info.remark\" type=\"textarea\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoForm',\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tableName: [\r\n          { required: true, message: '请输入表名称', trigger: 'blur' },\r\n          { pattern: /^[a-z\\._]*$/g, trigger: 'blur', message: '只允许小写字母,例如 sys_demo 格式' }\r\n        ],\r\n        tableComment: [\r\n          { required: true, message: '请输入菜单名称', trigger: 'blur' }\r\n        ],\r\n        className: [\r\n          { required: true, message: '请输入模型名称', trigger: 'blur' },\r\n          { pattern: /^[A-Z][A-z0-9]*$/g, trigger: 'blur', message: '必须以大写字母开头,例如 SysDemo 格式' }\r\n        ],\r\n        functionAuthor: [\r\n          { required: true, message: '请输入作者', trigger: 'blur' },\r\n          { pattern: /^[A-Za-z]+$/, trigger: 'blur', message: '校验规则:  只允许输入字母 a-z 或大写 A-Z' }\r\n        ]\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAsEA,eAAe;EACbA,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;QACLC,SAAS,EAAE,CACT;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAC,EACtD;UAAEC,OAAO,EAAE,cAAc;UAAED,OAAO,EAAE,MAAM;UAAED,OAAO,EAAE;QAAyB,EAC/E;QACDG,YAAY,EAAE,CACZ;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,EACvD;QACDG,SAAS,EAAE,CACT;UAAEL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC,EACvD;UAAEC,OAAO,EAAE,mBAAmB;UAAED,OAAO,EAAE,MAAM;UAAED,OAAO,EAAE;QAA0B,EACrF;QACDK,cAAc,EAAE,CACd;UAAEN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAO,CAAC,EACrD;UAAEC,OAAO,EAAE,aAAa;UAAED,OAAO,EAAE,MAAM;UAAED,OAAO,EAAE;QAA6B;MAErF;IACF;EACF;AACF", "ignoreList": []}]}