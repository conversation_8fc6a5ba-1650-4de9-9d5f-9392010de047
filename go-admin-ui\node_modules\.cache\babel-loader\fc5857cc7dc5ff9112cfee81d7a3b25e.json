{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\user.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\user.js", "mtime": 1753924829941}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIGxvZ2luIOeZu+mZhgpleHBvcnQgZnVuY3Rpb24gbG9naW4oZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvbG9naW4nLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIGxvZ291dCDpgIDlh7oKZXhwb3J0IGZ1bmN0aW9uIGxvZ291dCgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2xvZ291dCcsCiAgICBtZXRob2Q6ICdwb3N0JwogIH0pOwp9CgovLyByZWZyZXNodG9rZW4g5Yi35pawdG9rZW4KZXhwb3J0IGZ1bmN0aW9uIHJlZnJlc2h0b2tlbihkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL3JlZnJlc2h0b2tlbicsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8gZ2V0SW5mbyDojrflj5bnlKjmiLfln7rmnKzkv6Hmga8KZXhwb3J0IGZ1bmN0aW9uIGdldEluZm8oKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9nZXRpbmZvJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "login", "data", "url", "method", "logout", "refreshtoken", "getInfo"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/user.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// login 登陆\r\nexport function login(data) {\r\n  return request({\r\n    url: '/api/v1/login',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// logout 退出\r\nexport function logout() {\r\n  return request({\r\n    url: '/api/v1/logout',\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// refreshtoken 刷新token\r\nexport function refreshtoken(data) {\r\n  return request({\r\n    url: '/refreshtoken',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// getInfo 获取用户基本信息\r\nexport function getInfo() {\r\n  return request({\r\n    url: '/api/v1/getinfo',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,MAAMA,CAAA,EAAG;EACvB,OAAOL,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,YAAYA,CAACJ,IAAI,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdF,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,OAAOA,CAAA,EAAG;EACxB,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}