{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\Todo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\Todo.vue", "mtime": 1753924830288}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1RvZG8nLAogIGRpcmVjdGl2ZXM6IHsKICAgIGZvY3VzOiBmdW5jdGlvbiBmb2N1cyhlbCwgX3JlZiwgX3JlZjIpIHsKICAgICAgdmFyIHZhbHVlID0gX3JlZi52YWx1ZTsKICAgICAgdmFyIGNvbnRleHQgPSBfcmVmMi5jb250ZXh0OwogICAgICBpZiAodmFsdWUpIHsKICAgICAgICBjb250ZXh0LiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICBlbC5mb2N1cygpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9CiAgfSwKICBwcm9wczogewogICAgdG9kbzogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7fTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGVkaXRpbmc6IGZhbHNlCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgZGVsZXRlVG9kbzogZnVuY3Rpb24gZGVsZXRlVG9kbyh0b2RvKSB7CiAgICAgIHRoaXMuJGVtaXQoJ2RlbGV0ZVRvZG8nLCB0b2RvKTsKICAgIH0sCiAgICBlZGl0VG9kbzogZnVuY3Rpb24gZWRpdFRvZG8oX3JlZjMpIHsKICAgICAgdmFyIHRvZG8gPSBfcmVmMy50b2RvLAogICAgICAgIHZhbHVlID0gX3JlZjMudmFsdWU7CiAgICAgIHRoaXMuJGVtaXQoJ2VkaXRUb2RvJywgewogICAgICAgIHRvZG86IHRvZG8sCiAgICAgICAgdmFsdWU6IHZhbHVlCiAgICAgIH0pOwogICAgfSwKICAgIHRvZ2dsZVRvZG86IGZ1bmN0aW9uIHRvZ2dsZVRvZG8odG9kbykgewogICAgICB0aGlzLiRlbWl0KCd0b2dnbGVUb2RvJywgdG9kbyk7CiAgICB9LAogICAgZG9uZUVkaXQ6IGZ1bmN0aW9uIGRvbmVFZGl0KGUpIHsKICAgICAgdmFyIHZhbHVlID0gZS50YXJnZXQudmFsdWUudHJpbSgpOwogICAgICB2YXIgdG9kbyA9IHRoaXMudG9kbzsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIHRoaXMuZGVsZXRlVG9kbyh7CiAgICAgICAgICB0b2RvOiB0b2RvCiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5lZGl0aW5nKSB7CiAgICAgICAgdGhpcy5lZGl0VG9kbyh7CiAgICAgICAgICB0b2RvOiB0b2RvLAogICAgICAgICAgdmFsdWU6IHZhbHVlCiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5lZGl0aW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBjYW5jZWxFZGl0OiBmdW5jdGlvbiBjYW5jZWxFZGl0KGUpIHsKICAgICAgZS50YXJnZXQudmFsdWUgPSB0aGlzLnRvZG8udGV4dDsKICAgICAgdGhpcy5lZGl0aW5nID0gZmFsc2U7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "directives", "focus", "el", "_ref", "_ref2", "value", "context", "$nextTick", "props", "todo", "type", "Object", "default", "data", "editing", "methods", "deleteTodo", "$emit", "editTodo", "_ref3", "toggleTodo", "doneEdit", "e", "target", "trim", "cancelEdit", "text"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\Todo.vue"], "sourcesContent": ["<template>\r\n  <li :class=\"{ completed: todo.done, editing: editing }\" class=\"todo\">\r\n    <div class=\"view\">\r\n      <input\r\n        :checked=\"todo.done\"\r\n        class=\"toggle\"\r\n        type=\"checkbox\"\r\n        @change=\"toggleTodo( todo)\"\r\n      >\r\n      <label @dblclick=\"editing = true\" v-text=\"todo.text\" />\r\n      <button class=\"destroy\" @click=\"deleteTodo( todo )\" />\r\n    </div>\r\n    <input\r\n      v-show=\"editing\"\r\n      v-focus=\"editing\"\r\n      :value=\"todo.text\"\r\n      class=\"edit\"\r\n      @keyup.enter=\"doneEdit\"\r\n      @keyup.esc=\"cancelEdit\"\r\n      @blur=\"doneEdit\"\r\n    >\r\n  </li>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Todo',\r\n  directives: {\r\n    focus(el, { value }, { context }) {\r\n      if (value) {\r\n        context.$nextTick(() => {\r\n          el.focus()\r\n        })\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    todo: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      editing: false\r\n    }\r\n  },\r\n  methods: {\r\n    deleteTodo(todo) {\r\n      this.$emit('deleteTodo', todo)\r\n    },\r\n    editTodo({ todo, value }) {\r\n      this.$emit('editTodo', { todo, value })\r\n    },\r\n    toggleTodo(todo) {\r\n      this.$emit('toggleTodo', todo)\r\n    },\r\n    doneEdit(e) {\r\n      const value = e.target.value.trim()\r\n      const { todo } = this\r\n      if (!value) {\r\n        this.deleteTodo({\r\n          todo\r\n        })\r\n      } else if (this.editing) {\r\n        this.editTodo({\r\n          todo,\r\n          value\r\n        })\r\n        this.editing = false\r\n      }\r\n    },\r\n    cancelEdit(e) {\r\n      e.target.value = this.todo.text\r\n      this.editing = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";AAyBA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE;IACVC,KAAK,WAALA,KAAKA,CAACC,EAAE,EAAAC,IAAA,EAAAC,KAAA,EAA0B;MAAA,IAAtBC,KAAI,GAAAF,IAAA,CAAJE,KAAI;MAAA,IAAOC,OAAM,GAAAF,KAAA,CAANE,OAAM;MAC3B,IAAID,KAAK,EAAE;QACTC,OAAO,CAACC,SAAS,CAAC,YAAM;UACtBL,EAAE,CAACD,KAAK,CAAC;QACX,CAAC;MACH;IACF;EACF,CAAC;EACDO,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,SAATA,QAAOA,CAAA,EAAa;QAClB,OAAO,CAAC;MACV;IACF;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAACP,IAAI,EAAE;MACf,IAAI,CAACQ,KAAK,CAAC,YAAY,EAAER,IAAI;IAC/B,CAAC;IACDS,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAAkB;MAAA,IAAfV,IAAI,GAAAU,KAAA,CAAJV,IAAI;QAAEJ,KAAI,GAAAc,KAAA,CAAJd,KAAI;MACnB,IAAI,CAACY,KAAK,CAAC,UAAU,EAAE;QAAER,IAAI,EAAJA,IAAI;QAAEJ,KAAI,EAAJA;MAAM,CAAC;IACxC,CAAC;IACDe,UAAU,WAAVA,UAAUA,CAACX,IAAI,EAAE;MACf,IAAI,CAACQ,KAAK,CAAC,YAAY,EAAER,IAAI;IAC/B,CAAC;IACDY,QAAQ,WAARA,QAAQA,CAACC,CAAC,EAAE;MACV,IAAMjB,KAAI,GAAIiB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAACmB,IAAI,CAAC;MAClC,IAAQf,IAAG,GAAM,IAAG,CAAZA,IAAG;MACX,IAAI,CAACJ,KAAK,EAAE;QACV,IAAI,CAACW,UAAU,CAAC;UACdP,IAAG,EAAHA;QACF,CAAC;MACH,OAAO,IAAI,IAAI,CAACK,OAAO,EAAE;QACvB,IAAI,CAACI,QAAQ,CAAC;UACZT,IAAI,EAAJA,IAAI;UACJJ,KAAI,EAAJA;QACF,CAAC;QACD,IAAI,CAACS,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IACDW,UAAU,WAAVA,UAAUA,CAACH,CAAC,EAAE;MACZA,CAAC,CAACC,MAAM,CAAClB,KAAI,GAAI,IAAI,CAACI,IAAI,CAACiB,IAAG;MAC9B,IAAI,CAACZ,OAAM,GAAI,KAAI;IACrB;EACF;AACF", "ignoreList": []}]}