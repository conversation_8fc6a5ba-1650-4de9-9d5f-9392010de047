{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\dialog.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\dialog.js", "mtime": 1753924830247}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "directive", "bind", "el", "binding", "vnode", "oldVnode", "dialogHeaderEl", "querySelector", "dragDom", "style", "cssText", "sty", "window", "document", "currentStyle", "dom", "attr", "getComputedStyle", "onmousedown", "e", "disX", "clientX", "offsetLeft", "disY", "clientY", "offsetTop", "screenWidth", "body", "clientWidth", "screenHeight", "documentElement", "clientHeight", "dragD<PERSON><PERSON><PERSON>th", "offsetWidth", "dragDomheight", "offsetHeight", "minDragDomLeft", "maxDragDomLeft", "minDragDomTop", "maxDragDomTop", "styL", "styT", "includes", "replace", "<PERSON><PERSON><PERSON><PERSON>", "left", "top", "concat", "onmouseup"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/dialog.js"], "sourcesContent": ["import Vue from 'vue'\r\n// v-dialogDrag: 弹窗拖拽属性\r\nVue.directive('dialogDrag', {\r\n  bind(el, binding, vnode, oldVnode) {\r\n    const dialogHeaderEl = el.querySelector('.el-dialog__header')\r\n    const dragDom = el.querySelector('.el-dialog')\r\n    dialogHeaderEl.style.cssText += ';cursor:move;'\r\n    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\r\n    const sty = (function() {\r\n      if (window.document.currentStyle) {\r\n        return (dom, attr) => dom.currentStyle[attr]\r\n      } else {\r\n        return (dom, attr) => getComputedStyle(dom, false)[attr]\r\n      }\r\n    })()\r\n\r\n    dialogHeaderEl.onmousedown = (e) => {\r\n      // 鼠标按下，计算当前元素距离可视区的距离\r\n      const disX = e.clientX - dialogHeaderEl.offsetLeft\r\n      const disY = e.clientY - dialogHeaderEl.offsetTop\r\n\r\n      const screenWidth = document.body.clientWidth // body当前宽度\r\n      const screenHeight = document.documentElement.clientHeight // 可见区域高度(应为body高度，可某些环境下无法获取)\r\n\r\n      const dragDomWidth = dragDom.offsetWidth // 对话框宽度\r\n      const dragDomheight = dragDom.offsetHeight // 对话框高度\r\n\r\n      const minDragDomLeft = dragDom.offsetLeft\r\n      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth\r\n\r\n      const minDragDomTop = dragDom.offsetTop\r\n      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight\r\n\r\n      // 获取到的值带px 正则匹配替换\r\n      let styL = sty(dragDom, 'left')\r\n      let styT = sty(dragDom, 'top')\r\n\r\n      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\r\n      if (styL.includes('%')) {\r\n        styL = +document.body.clientWidth * (+styL.replace(/\\%/g, '') / 100)\r\n        styT = +document.body.clientHeight * (+styT.replace(/\\%/g, '') / 100)\r\n      } else {\r\n        styL = +styL.replace(/\\px/g, '')\r\n        styT = +styT.replace(/\\px/g, '')\r\n      }\r\n\r\n      document.onmousemove = function(e) {\r\n        // 通过事件委托，计算移动的距离\r\n        let left = e.clientX - disX\r\n        let top = e.clientY - disY\r\n\r\n        // 边界处理\r\n        if (-(left) > minDragDomLeft) {\r\n          left = -(minDragDomLeft)\r\n        } else if (left > maxDragDomLeft) {\r\n          left = maxDragDomLeft\r\n        }\r\n\r\n        if (-(top) > minDragDomTop) {\r\n          top = -(minDragDomTop)\r\n        } else if (top > maxDragDomTop) {\r\n          top = maxDragDomTop\r\n        }\r\n\r\n        // 移动当前元素\r\n        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`\r\n      }\r\n\r\n      document.onmouseup = function(e) {\r\n        document.onmousemove = null\r\n        document.onmouseup = null\r\n      }\r\n      return false\r\n    }\r\n  }\r\n})\r\n"], "mappings": ";;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB;AACAA,GAAG,CAACC,SAAS,CAAC,YAAY,EAAE;EAC1BC,IAAI,WAAJA,IAAIA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACjC,IAAMC,cAAc,GAAGJ,EAAE,CAACK,aAAa,CAAC,oBAAoB,CAAC;IAC7D,IAAMC,OAAO,GAAGN,EAAE,CAACK,aAAa,CAAC,YAAY,CAAC;IAC9CD,cAAc,CAACG,KAAK,CAACC,OAAO,IAAI,eAAe;IAC/C;IACA,IAAMC,GAAG,GAAI,YAAW;MACtB,IAAIC,MAAM,CAACC,QAAQ,CAACC,YAAY,EAAE;QAChC,OAAO,UAACC,GAAG,EAAEC,IAAI;UAAA,OAAKD,GAAG,CAACD,YAAY,CAACE,IAAI,CAAC;QAAA;MAC9C,CAAC,MAAM;QACL,OAAO,UAACD,GAAG,EAAEC,IAAI;UAAA,OAAKC,gBAAgB,CAACF,GAAG,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;QAAA;MAC1D;IACF,CAAC,CAAE,CAAC;IAEJV,cAAc,CAACY,WAAW,GAAG,UAACC,CAAC,EAAK;MAClC;MACA,IAAMC,IAAI,GAAGD,CAAC,CAACE,OAAO,GAAGf,cAAc,CAACgB,UAAU;MAClD,IAAMC,IAAI,GAAGJ,CAAC,CAACK,OAAO,GAAGlB,cAAc,CAACmB,SAAS;MAEjD,IAAMC,WAAW,GAAGb,QAAQ,CAACc,IAAI,CAACC,WAAW,EAAC;MAC9C,IAAMC,YAAY,GAAGhB,QAAQ,CAACiB,eAAe,CAACC,YAAY,EAAC;;MAE3D,IAAMC,YAAY,GAAGxB,OAAO,CAACyB,WAAW,EAAC;MACzC,IAAMC,aAAa,GAAG1B,OAAO,CAAC2B,YAAY,EAAC;;MAE3C,IAAMC,cAAc,GAAG5B,OAAO,CAACc,UAAU;MACzC,IAAMe,cAAc,GAAGX,WAAW,GAAGlB,OAAO,CAACc,UAAU,GAAGU,YAAY;MAEtE,IAAMM,aAAa,GAAG9B,OAAO,CAACiB,SAAS;MACvC,IAAMc,aAAa,GAAGV,YAAY,GAAGrB,OAAO,CAACiB,SAAS,GAAGS,aAAa;;MAEtE;MACA,IAAIM,IAAI,GAAG7B,GAAG,CAACH,OAAO,EAAE,MAAM,CAAC;MAC/B,IAAIiC,IAAI,GAAG9B,GAAG,CAACH,OAAO,EAAE,KAAK,CAAC;;MAE9B;MACA,IAAIgC,IAAI,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtBF,IAAI,GAAG,CAAC3B,QAAQ,CAACc,IAAI,CAACC,WAAW,IAAI,CAACY,IAAI,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;QACpEF,IAAI,GAAG,CAAC5B,QAAQ,CAACc,IAAI,CAACI,YAAY,IAAI,CAACU,IAAI,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;MACvE,CAAC,MAAM;QACLH,IAAI,GAAG,CAACA,IAAI,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QAChCF,IAAI,GAAG,CAACA,IAAI,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MAClC;MAEA9B,QAAQ,CAAC+B,WAAW,GAAG,UAASzB,CAAC,EAAE;QACjC;QACA,IAAI0B,IAAI,GAAG1B,CAAC,CAACE,OAAO,GAAGD,IAAI;QAC3B,IAAI0B,GAAG,GAAG3B,CAAC,CAACK,OAAO,GAAGD,IAAI;;QAE1B;QACA,IAAI,CAAEsB,IAAK,GAAGT,cAAc,EAAE;UAC5BS,IAAI,GAAG,CAAET,cAAe;QAC1B,CAAC,MAAM,IAAIS,IAAI,GAAGR,cAAc,EAAE;UAChCQ,IAAI,GAAGR,cAAc;QACvB;QAEA,IAAI,CAAES,GAAI,GAAGR,aAAa,EAAE;UAC1BQ,GAAG,GAAG,CAAER,aAAc;QACxB,CAAC,MAAM,IAAIQ,GAAG,GAAGP,aAAa,EAAE;UAC9BO,GAAG,GAAGP,aAAa;QACrB;;QAEA;QACA/B,OAAO,CAACC,KAAK,CAACC,OAAO,aAAAqC,MAAA,CAAaF,IAAI,GAAGL,IAAI,aAAAO,MAAA,CAAUD,GAAG,GAAGL,IAAI,QAAK;MACxE,CAAC;MAED5B,QAAQ,CAACmC,SAAS,GAAG,UAAS7B,CAAC,EAAE;QAC/BN,QAAQ,CAAC+B,WAAW,GAAG,IAAI;QAC3B/B,QAAQ,CAACmC,SAAS,GAAG,IAAI;MAC3B,CAAC;MACD,OAAO,KAAK;IACd,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}]}