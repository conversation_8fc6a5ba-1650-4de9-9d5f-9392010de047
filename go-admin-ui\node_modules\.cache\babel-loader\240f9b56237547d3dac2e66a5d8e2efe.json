{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue", "mtime": 1753924830053}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNaW5pUHJvZ3Jlc3MnLAogIHByb3BzOiB7CiAgICB0YXJnZXQ6IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiAwCiAgICB9LAogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzEwcHgnCiAgICB9LAogICAgY29sb3I6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnIzEzQzJDMicKICAgIH0sCiAgICBwZXJjZW50YWdlOiB7CiAgICAgIHR5cGU6IE51bWJlciwKICAgICAgZGVmYXVsdDogMAogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "props", "target", "type", "Number", "default", "height", "String", "color", "percentage"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chart-mini-progress\">\r\n    <div class=\"target\" :style=\"{ left: target + '%'}\">\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n    </div>\r\n    <div class=\"progress-wrapper\">\r\n      <div class=\"progress\" :style=\"{ backgroundColor: color, width: percentage + '%', height: height }\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MiniProgress',\r\n  props: {\r\n    target: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '10px'\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: '#13C2C2'\r\n    },\r\n    percentage: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-mini-progress {\r\n    padding: 5px 0;\r\n    position: relative;\r\n    width: 100%;\r\n    .target {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      span {\r\n        border-radius: 100px;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        height: 4px;\r\n        width: 2px;\r\n        &:last-child {\r\n          top: auto;\r\n          bottom: 0;\r\n        }\r\n      }\r\n    }\r\n    .progress-wrapper {\r\n      background-color: #f5f5f5;\r\n      position: relative;\r\n      .progress {\r\n        transition: all .4s cubic-bezier(.08,.82,.17,1) 0s;\r\n        border-radius: 1px 0 0 1px;\r\n        background-color: #1890ff;\r\n        width: 0;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";AAaA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE;MACNH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,KAAK,EAAE;MACLL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDI,UAAU,EAAE;MACVN,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF;AACF", "ignoreList": []}]}