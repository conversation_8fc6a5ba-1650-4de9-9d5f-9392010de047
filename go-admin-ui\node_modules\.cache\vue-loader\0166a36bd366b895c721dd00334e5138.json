{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-particles@1.0.9\\node_modules\\vue-particles\\src\\vue-particles\\vue-particles.vue?vue&type=template&id=53315554", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-particles@1.0.9\\node_modules\\vue-particles\\src\\vue-particles\\vue-particles.vue", "mtime": 1496175041000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgPGRpdgogICAgaWQ9InBhcnRpY2xlcy1qcyIKICAgIDpjb2xvcj0iY29sb3IiCiAgICA6cGFydGljbGVPcGFjaXR5PSJwYXJ0aWNsZU9wYWNpdHkiCiAgICA6bGluZXNDb2xvcj0ibGluZXNDb2xvciIKICAgIDpwYXJ0aWNsZXNOdW1iZXI9InBhcnRpY2xlc051bWJlciIKICAgIDpzaGFwZVR5cGU9InNoYXBlVHlwZSIKICAgIDpwYXJ0aWNsZVNpemU9InBhcnRpY2xlU2l6ZSIKICAgIDpsaW5lc1dpZHRoPSJsaW5lc1dpZHRoIgogICAgOmxpbmVMaW5rZWQ9ImxpbmVMaW5rZWQiCiAgICA6bGluZU9wYWNpdHk9ImxpbmVPcGFjaXR5IgogICAgOmxpbmVzRGlzdGFuY2U9ImxpbmVzRGlzdGFuY2UiCiAgICA6bW92ZVNwZWVkPSJtb3ZlU3BlZWQiCiAgICA6aG92ZXJFZmZlY3Q9ImhvdmVyRWZmZWN0IgogICAgOmhvdmVyTW9kZT0iaG92ZXJNb2RlIgogICAgOmNsaWNrRWZmZWN0PSJjbGlja0VmZmVjdCIKICAgIDpjbGlja01vZGU9ImNsaWNrTW9kZSIKICA+PC9kaXY+Cg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-particles@1.0.9\\node_modules\\vue-particles\\src\\vue-particles\\vue-particles.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/node_modules/.store/vue-particles@1.0.9/node_modules/vue-particles/src/vue-particles/vue-particles.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div\n    id=\"particles-js\"\n    :color=\"color\"\n    :particleOpacity=\"particleOpacity\"\n    :linesColor=\"linesColor\"\n    :particlesNumber=\"particlesNumber\"\n    :shapeType=\"shapeType\"\n    :particleSize=\"particleSize\"\n    :linesWidth=\"linesWidth\"\n    :lineLinked=\"lineLinked\"\n    :lineOpacity=\"lineOpacity\"\n    :linesDistance=\"linesDistance\"\n    :moveSpeed=\"moveSpeed\"\n    :hoverEffect=\"hoverEffect\"\n    :hoverMode=\"hoverMode\"\n    :clickEffect=\"clickEffect\"\n    :clickMode=\"clickMode\"\n  ></div>\n</template>\n<script>\n  /* eslint-disable */\n  export default {\n    name: 'vue-particles',\n    props: {\n      color: {\n        type: String,\n        default: '#dedede'\n      },\n      particleOpacity: {\n        type: Number,\n        default: 0.7\n      },\n      particlesNumber: {\n        type: Number,\n        default: 80\n      },\n      shapeType: {\n        type: String,\n        default: 'circle'\n      },\n      particleSize: {\n        type: Number,\n        default: 4\n      },\n      linesColor: {\n        type: String,\n        default: '#dedede'\n      },\n      linesWidth: {\n        type: Number,\n        default: 1\n      },\n      lineLinked: {\n        type: Boolean,\n        default: true\n      },\n      lineOpacity: {\n        type: Number,\n        default: 0.4\n      },\n      linesDistance: {\n        type: Number,\n        default: 150\n      },\n      moveSpeed: {\n        type: Number,\n        default: 3\n      },\n      hoverEffect: {\n        type: Boolean,\n        default: true\n      },\n      hoverMode: {\n        type: String,\n        default: 'grab'\n      },\n      clickEffect: {\n        type: Boolean,\n        default: true\n      },\n      clickMode: {\n        type: String,\n        default: 'push'\n      }\n    },\n    mounted () {\n      // import particle.js only on client-side\n      require('particles.js')\n      this.$nextTick(() => {\n        this.initParticleJS(\n          this.color,\n          this.particleOpacity,\n          this.particlesNumber,\n          this.shapeType,\n          this.particleSize,\n          this.linesColor,\n          this.linesWidth,\n          this.lineLinked,\n          this.lineOpacity,\n          this.linesDistance,\n          this.moveSpeed,\n          this.hoverEffect,\n          this.hoverMode,\n          this.clickEffect,\n          this.clickMode\n        )\n      })\n    },\n    methods: {\n      initParticleJS (\n        color,\n        particleOpacity,\n        particlesNumber,\n        shapeType,\n        particleSize,\n        linesColor,\n        linesWidth,\n        lineLinked,\n        lineOpacity,\n        linesDistance,\n        moveSpeed,\n        hoverEffect,\n        hoverMode,\n        clickEffect,\n        clickMode\n      ) {\n        particlesJS('particles-js', {\n          \"particles\": {\n            \"number\": {\n              \"value\": particlesNumber,\n              \"density\": {\n                \"enable\": true,\n                \"value_area\": 800\n              }\n            },\n            \"color\": {\n              \"value\": color\n            },\n            \"shape\": {\n              // circle, edge, triangle, polygon, star, image\n              \"type\": shapeType,\n              \"stroke\": {\n                \"width\": 0,\n                \"color\": \"#192231\"\n              },\n              \"polygon\": {\n                \"nb_sides\": 5\n              }\n            },\n            \"opacity\": {\n              \"value\": particleOpacity,\n              \"random\": false,\n              \"anim\": {\n                \"enable\": false,\n                \"speed\": 1,\n                \"opacity_min\": 0.1,\n                \"sync\": false\n              }\n            },\n            \"size\": {\n              \"value\": particleSize,\n              \"random\": true,\n              \"anim\": {\n                \"enable\": false,\n                \"speed\": 40,\n                \"size_min\": 0.1,\n                \"sync\": false\n              }\n            },\n            \"line_linked\": {\n              \"enable\": lineLinked,\n              \"distance\": linesDistance,\n              \"color\": linesColor,\n              \"opacity\": lineOpacity,\n              \"width\": linesWidth\n            },\n            \"move\": {\n              \"enable\": true,\n              \"speed\": moveSpeed,\n              \"direction\": \"none\",\n              \"random\": false,\n              \"straight\": false,\n              \"out_mode\": \"out\",\n              \"bounce\": false,\n              \"attract\": {\n                \"enable\": false,\n                \"rotateX\": 600,\n                \"rotateY\": 1200\n              }\n            }\n          },\n          \"interactivity\": {\n            \"detect_on\": \"canvas\",\n            \"events\": {\n              \"onhover\": {\n                \"enable\": hoverEffect,\n                \"mode\": hoverMode\n              },\n              \"onclick\": {\n                \"enable\": clickEffect,\n                \"mode\": clickMode\n              },\n              \"onresize\": {\n\n                \"enable\": true,\n                \"density_auto\": true,\n                \"density_area\": 400\n\n              }\n            },\n            \"modes\": {\n              \"grab\": {\n                \"distance\": 140,\n                \"line_linked\": {\n                  \"opacity\": 1\n                }\n              },\n              \"bubble\": {\n                \"distance\": 400,\n                \"size\": 40,\n                \"duration\": 2,\n                \"opacity\": 8,\n                \"speed\": 3\n              },\n              \"repulse\": {\n                \"distance\": 200,\n                \"duration\": 0.4\n              },\n              \"push\": {\n                \"particles_nb\": 4\n              },\n              \"remove\": {\n                \"particles_nb\": 2\n              }\n            }\n          },\n          \"retina_detect\": true\n        });\n      }\n\n    }\n  }\n  /* eslint-disable */\n</script>\n"]}]}