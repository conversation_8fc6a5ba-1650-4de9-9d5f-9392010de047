{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\PanThumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\PanThumb\\index.vue", "mtime": 1753924830056}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQYW5UaHVtYicsDQogIHByb3BzOiB7DQogICAgaW1hZ2U6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICB6SW5kZXg6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDENCiAgICB9LA0KICAgIHdpZHRoOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnMTUwcHgnDQogICAgfSwNCiAgICBoZWlnaHQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcxNTBweCcNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\PanThumb\\index.vue"], "names": [], "mappings": ";AAaA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/PanThumb/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :style=\"{zIndex:zIndex,height:height,width:width}\" class=\"pan-item\">\r\n    <div class=\"pan-info\">\r\n      <div class=\"pan-info-roles-container\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n    <!-- eslint-disable-next-line -->\r\n    <div :style=\"{backgroundImage: `url(${image})`}\" class=\"pan-thumb\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PanThumb',\r\n  props: {\r\n    image: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    zIndex: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '150px'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '150px'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.pan-item {\r\n  width: 200px;\r\n  height: 200px;\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n  position: relative;\r\n  cursor: default;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.pan-info-roles-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.pan-thumb {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-position: center center;\r\n  background-size: cover;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  transform-origin: 95% 40%;\r\n  transition: all 0.3s ease-in-out;\r\n}\r\n\r\n/* .pan-thumb:after {\r\n  content: '';\r\n  width: 8px;\r\n  height: 8px;\r\n  position: absolute;\r\n  border-radius: 50%;\r\n  top: 40%;\r\n  left: 95%;\r\n  margin: -4px 0 0 -4px;\r\n  background: radial-gradient(ellipse at center, rgba(14, 14, 14, 1) 0%, rgba(125, 126, 125, 1) 100%);\r\n  box-shadow: 0 0 1px rgba(255, 255, 255, 0.9);\r\n} */\r\n\r\n.pan-info {\r\n  position: absolute;\r\n  width: inherit;\r\n  height: inherit;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  box-shadow: inset 0 0 0 5px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.pan-info h3 {\r\n  color: #fff;\r\n  text-transform: uppercase;\r\n  position: relative;\r\n  letter-spacing: 2px;\r\n  font-size: 18px;\r\n  margin: 0 60px;\r\n  padding: 22px 0 0 0;\r\n  height: 85px;\r\n  font-family: 'Open Sans', Arial, sans-serif;\r\n  text-shadow: 0 0 1px #fff, 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.pan-info p {\r\n  color: #fff;\r\n  padding: 10px 5px;\r\n  font-style: italic;\r\n  margin: 0 30px;\r\n  font-size: 12px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.pan-info p a {\r\n  display: block;\r\n  color: #333;\r\n  width: 80px;\r\n  height: 80px;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-style: normal;\r\n  font-weight: 700;\r\n  text-transform: uppercase;\r\n  font-size: 9px;\r\n  letter-spacing: 1px;\r\n  padding-top: 24px;\r\n  margin: 7px auto 0;\r\n  font-family: 'Open Sans', Arial, sans-serif;\r\n  opacity: 0;\r\n  transition: transform 0.3s ease-in-out 0.2s, opacity 0.3s ease-in-out 0.2s, background 0.2s linear 0s;\r\n  transform: translateX(60px) rotate(90deg);\r\n}\r\n\r\n.pan-info p a:hover {\r\n  background: rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.pan-item:hover .pan-thumb {\r\n  transform: rotate(-110deg);\r\n}\r\n\r\n.pan-item:hover .pan-info p a {\r\n  opacity: 1;\r\n  transform: translateX(0px) rotate(0deg);\r\n}\r\n</style>\r\n"]}]}