{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1753924830211}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovRGVza3RvcC9Hb0FkbWluL2dvLWFkbWluLXVpL25vZGVfbW9kdWxlcy8uc3RvcmUvQGJhYmVsK3J1bnRpbWVANy4yOC4yL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF92YXJpYWJsZXMgZnJvbSAnQC9zdHlsZXMvdmFyaWFibGVzLnNjc3MnOwppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU2lkZWJhckxvZ28nLAogIHByb3BzOiB7CiAgICBjb2xsYXBzZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgbWFwR2V0dGVycyhbJ2FwcEluZm8nXSkpLCB7fSwgewogICAgdmFyaWFibGVzOiBmdW5jdGlvbiB2YXJpYWJsZXMoKSB7CiAgICAgIHJldHVybiBfdmFyaWFibGVzOwogICAgfQogIH0pCn07"}, {"version": 3, "names": ["variables", "mapGetters", "name", "props", "collapse", "type", "Boolean", "required", "computed", "_objectSpread"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Logo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: $store.state.settings.themeStyle === 'dark' ? variables.menuBg : variables.menuLightBg }\">\r\n    <transition name=\"sidebarLogoFade\">\r\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"appInfo.sys_app_logo\" :src=\"appInfo.sys_app_logo\" class=\"sidebar-logo\">\r\n        <h1 v-else class=\"sidebar-title\" :style=\"{ color: $store.state.settings.themeStyle === 'dark' ? variables.sidebarTitle : variables.sidebarLightTitle }\">{{ appInfo.sys_app_name }} </h1>\r\n      </router-link>\r\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"appInfo.sys_app_logo\" :src=\"appInfo.sys_app_logo\" class=\"sidebar-logo\">\r\n        <h1 class=\"sidebar-title\" :style=\"{ color: $store.state.settings.themeStyle === 'dark' ? variables.sidebarTitle : variables.sidebarLightTitle }\">{{ appInfo.sys_app_name }} </h1>\r\n      </router-link>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport variables from '@/styles/variables.scss'\r\nimport { mapGetters } from 'vuex'\r\n\r\nexport default {\r\n  name: 'SidebarLogo',\r\n  props: {\r\n    collapse: {\r\n      type: Boolean,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'appInfo'\r\n    ]),\r\n    variables() {\r\n      return variables\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sidebarLogoFade-enter-active {\r\n  transition: opacity 1.5s;\r\n}\r\n\r\n.sidebarLogoFade-enter,\r\n.sidebarLogoFade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.sidebar-logo-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 64px;\r\n  line-height: 64px;\r\n  background: #001529;\r\n  text-align: center;\r\n  overflow: hidden;\r\n\r\n  & .sidebar-logo-link {\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    & .sidebar-logo {\r\n      width: 32px;\r\n      height: 32px;\r\n      vertical-align: middle;\r\n      margin-right: 12px;\r\n      border-radius: 3px;\r\n    }\r\n\r\n    & .sidebar-title {\r\n      display: inline-block;\r\n      margin: 0;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.collapse {\r\n    .sidebar-logo {\r\n      margin-right: 0;\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAiBA,OAAOA,UAAQ,MAAO,yBAAwB;AAC9C,SAASC,UAAS,QAAS,MAAK;AAEhC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,OAAO;MACbC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHR,UAAU,CAAC,CACZ,SAAQ,CACT,CAAC;IACFD,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,OAAOA,UAAQ;IACjB;EAAA;AAEJ", "ignoreList": []}]}