{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\getters.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\getters.js", "mtime": 1753924830223}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyI7CnZhciBnZXR0ZXJzID0gewogIHNpZGViYXI6IGZ1bmN0aW9uIHNpZGViYXIoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuc2lkZWJhcjsKICB9LAogIHNpemU6IGZ1bmN0aW9uIHNpemUoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuc2l6ZTsKICB9LAogIGRldmljZTogZnVuY3Rpb24gZGV2aWNlKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUuYXBwLmRldmljZTsKICB9LAogIHZpc2l0ZWRWaWV3czogZnVuY3Rpb24gdmlzaXRlZFZpZXdzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudGFnc1ZpZXcudmlzaXRlZFZpZXdzOwogIH0sCiAgY2FjaGVkVmlld3M6IGZ1bmN0aW9uIGNhY2hlZFZpZXdzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudGFnc1ZpZXcuY2FjaGVkVmlld3M7CiAgfSwKICB0b2tlbjogZnVuY3Rpb24gdG9rZW4oc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLnRva2VuOwogIH0sCiAgYXZhdGFyOiBmdW5jdGlvbiBhdmF0YXIoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLmF2YXRhcjsKICB9LAogIG5hbWU6IGZ1bmN0aW9uIG5hbWUoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLm5hbWU7CiAgfSwKICBpbnRyb2R1Y3Rpb246IGZ1bmN0aW9uIGludHJvZHVjdGlvbihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIuaW50cm9kdWN0aW9uOwogIH0sCiAgcm9sZXM6IGZ1bmN0aW9uIHJvbGVzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5yb2xlczsKICB9LAogIHBlcm1pc2FjdGlvbjogZnVuY3Rpb24gcGVybWlzYWN0aW9uKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5wZXJtaXNhY3Rpb247CiAgfSwKICBwZXJtaXNzaW9uX3JvdXRlczogZnVuY3Rpb24gcGVybWlzc2lvbl9yb3V0ZXMoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5wZXJtaXNzaW9uLnJvdXRlczsKICB9LAogIHRvcGJhclJvdXRlcnM6IGZ1bmN0aW9uIHRvcGJhclJvdXRlcnMoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5wZXJtaXNzaW9uLnRvcGJhclJvdXRlcnM7CiAgfSwKICBkZWZhdWx0Um91dGVzOiBmdW5jdGlvbiBkZWZhdWx0Um91dGVzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUucGVybWlzc2lvbi5kZWZhdWx0Um91dGVzOwogIH0sCiAgc2lkZWJhclJvdXRlcnM6IGZ1bmN0aW9uIHNpZGViYXJSb3V0ZXJzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUucGVybWlzc2lvbi5zaWRlYmFyUm91dGVyczsKICB9LAogIGVycm9yTG9nczogZnVuY3Rpb24gZXJyb3JMb2dzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUuZXJyb3JMb2cubG9nczsKICB9LAogIGFwcEluZm86IGZ1bmN0aW9uIGFwcEluZm8oc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5zeXN0ZW0uaW5mbzsKICB9Cn07CmV4cG9ydCBkZWZhdWx0IGdldHRlcnM7"}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "size", "device", "visitedViews", "tagsView", "cachedViews", "token", "user", "avatar", "name", "introduction", "roles", "permisaction", "permission_routes", "permission", "routes", "topbarRouters", "defaultRoutes", "sidebarRouters", "errorLogs", "errorLog", "logs", "appInfo", "system", "info"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/getters.js"], "sourcesContent": ["const getters = {\r\n  sidebar: state => state.app.sidebar,\r\n  size: state => state.app.size,\r\n  device: state => state.app.device,\r\n  visitedViews: state => state.tagsView.visitedViews,\r\n  cachedViews: state => state.tagsView.cachedViews,\r\n  token: state => state.user.token,\r\n  avatar: state => state.user.avatar,\r\n  name: state => state.user.name,\r\n  introduction: state => state.user.introduction,\r\n  roles: state => state.user.roles,\r\n  permisaction: state => state.user.permisaction,\r\n  permission_routes: state => state.permission.routes,\r\n  topbarRouters: state => state.permission.topbarRouters,\r\n  defaultRoutes: state => state.permission.defaultRoutes,\r\n  sidebarRouters: state => state.permission.sidebarRouters,\r\n  errorLogs: state => state.errorLog.logs,\r\n  appInfo: state => state.system.info\r\n}\r\nexport default getters\r\n"], "mappings": ";AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,SAATA,OAAOA,CAAEC,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACnCG,IAAI,EAAE,SAANA,IAAIA,CAAEF,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACC,IAAI;EAAA;EAC7BC,MAAM,EAAE,SAARA,MAAMA,CAAEH,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACE,MAAM;EAAA;EACjCC,YAAY,EAAE,SAAdA,YAAYA,CAAEJ,KAAK;IAAA,OAAIA,KAAK,CAACK,QAAQ,CAACD,YAAY;EAAA;EAClDE,WAAW,EAAE,SAAbA,WAAWA,CAAEN,KAAK;IAAA,OAAIA,KAAK,CAACK,QAAQ,CAACC,WAAW;EAAA;EAChDC,KAAK,EAAE,SAAPA,KAAKA,CAAEP,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACD,KAAK;EAAA;EAChCE,MAAM,EAAE,SAARA,MAAMA,CAAET,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACC,MAAM;EAAA;EAClCC,IAAI,EAAE,SAANA,IAAIA,CAAEV,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACE,IAAI;EAAA;EAC9BC,YAAY,EAAE,SAAdA,YAAYA,CAAEX,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACG,YAAY;EAAA;EAC9CC,KAAK,EAAE,SAAPA,KAAKA,CAAEZ,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACI,KAAK;EAAA;EAChCC,YAAY,EAAE,SAAdA,YAAYA,CAAEb,KAAK;IAAA,OAAIA,KAAK,CAACQ,IAAI,CAACK,YAAY;EAAA;EAC9CC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAEd,KAAK;IAAA,OAAIA,KAAK,CAACe,UAAU,CAACC,MAAM;EAAA;EACnDC,aAAa,EAAE,SAAfA,aAAaA,CAAEjB,KAAK;IAAA,OAAIA,KAAK,CAACe,UAAU,CAACE,aAAa;EAAA;EACtDC,aAAa,EAAE,SAAfA,aAAaA,CAAElB,KAAK;IAAA,OAAIA,KAAK,CAACe,UAAU,CAACG,aAAa;EAAA;EACtDC,cAAc,EAAE,SAAhBA,cAAcA,CAAEnB,KAAK;IAAA,OAAIA,KAAK,CAACe,UAAU,CAACI,cAAc;EAAA;EACxDC,SAAS,EAAE,SAAXA,SAASA,CAAEpB,KAAK;IAAA,OAAIA,KAAK,CAACqB,QAAQ,CAACC,IAAI;EAAA;EACvCC,OAAO,EAAE,SAATA,OAAOA,CAAEvB,KAAK;IAAA,OAAIA,KAAK,CAACwB,MAAM,CAACC,IAAI;EAAA;AACrC,CAAC;AACD,eAAe3B,OAAO", "ignoreList": []}]}