{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue", "mtime": 1753924830290}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Todo", "STORAGE_KEY", "filters", "all", "todos", "active", "filter", "todo", "done", "completed", "defalutList", "text", "components", "pluralize", "n", "w", "capitalize", "s", "char<PERSON>t", "toUpperCase", "slice", "data", "visibility", "computed", "allChecked", "every", "filteredTodos", "remaining", "length", "methods", "setLocalStorage", "window", "localStorage", "setItem", "JSON", "stringify", "addTodo", "e", "target", "value", "trim", "push", "toggleTodo", "val", "deleteTodo", "splice", "indexOf", "editTodo", "_ref", "clearCompleted", "toggleAll", "_ref2", "_this", "for<PERSON>ach"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue"], "sourcesContent": ["<template>\r\n  <section class=\"todoapp\">\r\n    <!-- header -->\r\n    <header class=\"header\">\r\n      <input class=\"new-todo\" autocomplete=\"off\" placeholder=\"Todo List\" @keyup.enter=\"addTodo\">\r\n    </header>\r\n    <!-- main section -->\r\n    <section v-show=\"todos.length\" class=\"main\">\r\n      <input id=\"toggle-all\" :checked=\"allChecked\" class=\"toggle-all\" type=\"checkbox\" @change=\"toggleAll({ done: !allChecked })\">\r\n      <label for=\"toggle-all\" />\r\n      <ul class=\"todo-list\">\r\n        <todo\r\n          v-for=\"(todo, index) in filteredTodos\"\r\n          :key=\"index\"\r\n          :todo=\"todo\"\r\n          @toggleTodo=\"toggleTodo\"\r\n          @editTodo=\"editTodo\"\r\n          @deleteTodo=\"deleteTodo\"\r\n        />\r\n      </ul>\r\n    </section>\r\n    <!-- footer -->\r\n    <footer v-show=\"todos.length\" class=\"footer\">\r\n      <span class=\"todo-count\">\r\n        <strong>{{ remaining }}</strong>\r\n        {{ remaining | pluralize('item') }} left\r\n      </span>\r\n      <ul class=\"filters\">\r\n        <li v-for=\"(val, key) in filters\" :key=\"key\">\r\n          <a :class=\"{ selected: visibility === key }\" @click.prevent=\"visibility = key\">{{ key | capitalize }}</a>\r\n        </li>\r\n      </ul>\r\n      <!-- <button class=\"clear-completed\" v-show=\"todos.length > remaining\" @click=\"clearCompleted\">\r\n        Clear completed\r\n      </button> -->\r\n    </footer>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport Todo from './Todo.vue'\r\n\r\nconst STORAGE_KEY = 'todos'\r\nconst filters = {\r\n  all: todos => todos,\r\n  active: todos => todos.filter(todo => !todo.done),\r\n  completed: todos => todos.filter(todo => todo.done)\r\n}\r\nconst defalutList = [\r\n  { text: 'star this repository', done: false },\r\n  { text: 'fork this repository', done: false },\r\n  { text: 'follow author', done: false },\r\n  { text: 'vue-element-admin', done: true },\r\n  { text: 'vue', done: true },\r\n  { text: 'element-ui', done: true },\r\n  { text: 'axios', done: true },\r\n  { text: 'webpack', done: true }\r\n]\r\nexport default {\r\n  components: { Todo },\r\n  filters: {\r\n    pluralize: (n, w) => n === 1 ? w : w + 's',\r\n    capitalize: s => s.charAt(0).toUpperCase() + s.slice(1)\r\n  },\r\n  data() {\r\n    return {\r\n      visibility: 'all',\r\n      filters,\r\n      // todos: JSON.parse(window.localStorage.getItem(STORAGE_KEY)) || defalutList\r\n      todos: defalutList\r\n    }\r\n  },\r\n  computed: {\r\n    allChecked() {\r\n      return this.todos.every(todo => todo.done)\r\n    },\r\n    filteredTodos() {\r\n      return filters[this.visibility](this.todos)\r\n    },\r\n    remaining() {\r\n      return this.todos.filter(todo => !todo.done).length\r\n    }\r\n  },\r\n  methods: {\r\n    setLocalStorage() {\r\n      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(this.todos))\r\n    },\r\n    addTodo(e) {\r\n      const text = e.target.value\r\n      if (text.trim()) {\r\n        this.todos.push({\r\n          text,\r\n          done: false\r\n        })\r\n        this.setLocalStorage()\r\n      }\r\n      e.target.value = ''\r\n    },\r\n    toggleTodo(val) {\r\n      val.done = !val.done\r\n      this.setLocalStorage()\r\n    },\r\n    deleteTodo(todo) {\r\n      this.todos.splice(this.todos.indexOf(todo), 1)\r\n      this.setLocalStorage()\r\n    },\r\n    editTodo({ todo, value }) {\r\n      todo.text = value\r\n      this.setLocalStorage()\r\n    },\r\n    clearCompleted() {\r\n      this.todos = this.todos.filter(todo => !todo.done)\r\n      this.setLocalStorage()\r\n    },\r\n    toggleAll({ done }) {\r\n      this.todos.forEach(todo => {\r\n        todo.done = done\r\n        this.setLocalStorage()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  @import './index.scss';\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAwCA,OAAOA,IAAG,MAAO,YAAW;AAE5B,IAAMC,WAAU,GAAI,OAAM;AAC1B,IAAMC,OAAM,GAAI;EACdC,GAAG,EAAE,SAALA,GAAGA,CAAEC,KAAI;IAAA,OAAKA,KAAK;EAAA;EACnBC,MAAM,EAAE,SAARA,MAAMA,CAAED,KAAI;IAAA,OAAKA,KAAK,CAACE,MAAM,CAAC,UAAAC,IAAG;MAAA,OAAK,CAACA,IAAI,CAACC,IAAI;IAAA,EAAC;EAAA;EACjDC,SAAS,EAAE,SAAXA,SAASA,CAAEL,KAAI;IAAA,OAAKA,KAAK,CAACE,MAAM,CAAC,UAAAC,IAAG;MAAA,OAAKA,IAAI,CAACC,IAAI;IAAA;EAAA;AACpD;AACA,IAAME,WAAU,GAAI,CAClB;EAAEC,IAAI,EAAE,sBAAsB;EAAEH,IAAI,EAAE;AAAM,CAAC,EAC7C;EAAEG,IAAI,EAAE,sBAAsB;EAAEH,IAAI,EAAE;AAAM,CAAC,EAC7C;EAAEG,IAAI,EAAE,eAAe;EAAEH,IAAI,EAAE;AAAM,CAAC,EACtC;EAAEG,IAAI,EAAE,mBAAmB;EAAEH,IAAI,EAAE;AAAK,CAAC,EACzC;EAAEG,IAAI,EAAE,KAAK;EAAEH,IAAI,EAAE;AAAK,CAAC,EAC3B;EAAEG,IAAI,EAAE,YAAY;EAAEH,IAAI,EAAE;AAAK,CAAC,EAClC;EAAEG,IAAI,EAAE,OAAO;EAAEH,IAAI,EAAE;AAAK,CAAC,EAC7B;EAAEG,IAAI,EAAE,SAAS;EAAEH,IAAI,EAAE;AAAK,EAChC;AACA,eAAe;EACbI,UAAU,EAAE;IAAEZ,IAAG,EAAHA;EAAK,CAAC;EACpBE,OAAO,EAAE;IACPW,SAAS,EAAE,SAAXA,SAASA,CAAGC,CAAC,EAAEC,CAAC;MAAA,OAAKD,CAAA,KAAM,IAAIC,CAAA,GAAIA,CAAA,GAAI,GAAG;IAAA;IAC1CC,UAAU,EAAE,SAAZA,UAAUA,CAAEC,CAAA;MAAA,OAAKA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,IAAIF,CAAC,CAACG,KAAK,CAAC,CAAC;IAAA;EACxD,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,KAAK;MACjBpB,OAAO,EAAPA,OAAO;MACP;MACAE,KAAK,EAAEM;IACT;EACF,CAAC;EACDa,QAAQ,EAAE;IACRC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACpB,KAAK,CAACqB,KAAK,CAAC,UAAAlB,IAAG;QAAA,OAAKA,IAAI,CAACC,IAAI;MAAA;IAC3C,CAAC;IACDkB,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,OAAOxB,OAAO,CAAC,IAAI,CAACoB,UAAU,CAAC,CAAC,IAAI,CAAClB,KAAK;IAC5C,CAAC;IACDuB,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,OAAO,IAAI,CAACvB,KAAK,CAACE,MAAM,CAAC,UAAAC,IAAG;QAAA,OAAK,CAACA,IAAI,CAACC,IAAI;MAAA,EAAC,CAACoB,MAAK;IACpD;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChBC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAChC,WAAW,EAAEiC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/B,KAAK,CAAC;IACrE,CAAC;IACDgC,OAAO,WAAPA,OAAOA,CAACC,CAAC,EAAE;MACT,IAAM1B,IAAG,GAAI0B,CAAC,CAACC,MAAM,CAACC,KAAI;MAC1B,IAAI5B,IAAI,CAAC6B,IAAI,CAAC,CAAC,EAAE;QACf,IAAI,CAACpC,KAAK,CAACqC,IAAI,CAAC;UACd9B,IAAI,EAAJA,IAAI;UACJH,IAAI,EAAE;QACR,CAAC;QACD,IAAI,CAACsB,eAAe,CAAC;MACvB;MACAO,CAAC,CAACC,MAAM,CAACC,KAAI,GAAI,EAAC;IACpB,CAAC;IACDG,UAAU,WAAVA,UAAUA,CAACC,GAAG,EAAE;MACdA,GAAG,CAACnC,IAAG,GAAI,CAACmC,GAAG,CAACnC,IAAG;MACnB,IAAI,CAACsB,eAAe,CAAC;IACvB,CAAC;IACDc,UAAU,WAAVA,UAAUA,CAACrC,IAAI,EAAE;MACf,IAAI,CAACH,KAAK,CAACyC,MAAM,CAAC,IAAI,CAACzC,KAAK,CAAC0C,OAAO,CAACvC,IAAI,CAAC,EAAE,CAAC;MAC7C,IAAI,CAACuB,eAAe,CAAC;IACvB,CAAC;IACDiB,QAAQ,WAARA,QAAQA,CAAAC,IAAA,EAAkB;MAAA,IAAfzC,IAAI,GAAAyC,IAAA,CAAJzC,IAAI;QAAEgC,KAAI,GAAAS,IAAA,CAAJT,KAAI;MACnBhC,IAAI,CAACI,IAAG,GAAI4B,KAAI;MAChB,IAAI,CAACT,eAAe,CAAC;IACvB,CAAC;IACDmB,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC7C,KAAI,GAAI,IAAI,CAACA,KAAK,CAACE,MAAM,CAAC,UAAAC,IAAG;QAAA,OAAK,CAACA,IAAI,CAACC,IAAI;MAAA;MACjD,IAAI,CAACsB,eAAe,CAAC;IACvB,CAAC;IACDoB,SAAS,WAATA,SAASA,CAAAC,KAAA,EAAW;MAAA,IAAAC,KAAA;MAAA,IAAR5C,IAAG,GAAA2C,KAAA,CAAH3C,IAAG;MACb,IAAI,CAACJ,KAAK,CAACiD,OAAO,CAAC,UAAA9C,IAAG,EAAK;QACzBA,IAAI,CAACC,IAAG,GAAIA,IAAG;QACf4C,KAAI,CAACtB,eAAe,CAAC;MACvB,CAAC;IACH;EACF;AACF", "ignoreList": []}]}