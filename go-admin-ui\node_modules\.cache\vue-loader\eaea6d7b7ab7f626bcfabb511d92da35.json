{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue?vue&type=style&index=0&id=4666284a&lang=scss", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue", "mtime": 1753924830290}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIEBpbXBvcnQgJy4vaW5kZXguc2Nzcyc7DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue"], "names": [], "mappings": ";EA6HE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/components/TodoList/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <section class=\"todoapp\">\r\n    <!-- header -->\r\n    <header class=\"header\">\r\n      <input class=\"new-todo\" autocomplete=\"off\" placeholder=\"Todo List\" @keyup.enter=\"addTodo\">\r\n    </header>\r\n    <!-- main section -->\r\n    <section v-show=\"todos.length\" class=\"main\">\r\n      <input id=\"toggle-all\" :checked=\"allChecked\" class=\"toggle-all\" type=\"checkbox\" @change=\"toggleAll({ done: !allChecked })\">\r\n      <label for=\"toggle-all\" />\r\n      <ul class=\"todo-list\">\r\n        <todo\r\n          v-for=\"(todo, index) in filteredTodos\"\r\n          :key=\"index\"\r\n          :todo=\"todo\"\r\n          @toggleTodo=\"toggleTodo\"\r\n          @editTodo=\"editTodo\"\r\n          @deleteTodo=\"deleteTodo\"\r\n        />\r\n      </ul>\r\n    </section>\r\n    <!-- footer -->\r\n    <footer v-show=\"todos.length\" class=\"footer\">\r\n      <span class=\"todo-count\">\r\n        <strong>{{ remaining }}</strong>\r\n        {{ remaining | pluralize('item') }} left\r\n      </span>\r\n      <ul class=\"filters\">\r\n        <li v-for=\"(val, key) in filters\" :key=\"key\">\r\n          <a :class=\"{ selected: visibility === key }\" @click.prevent=\"visibility = key\">{{ key | capitalize }}</a>\r\n        </li>\r\n      </ul>\r\n      <!-- <button class=\"clear-completed\" v-show=\"todos.length > remaining\" @click=\"clearCompleted\">\r\n        Clear completed\r\n      </button> -->\r\n    </footer>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport Todo from './Todo.vue'\r\n\r\nconst STORAGE_KEY = 'todos'\r\nconst filters = {\r\n  all: todos => todos,\r\n  active: todos => todos.filter(todo => !todo.done),\r\n  completed: todos => todos.filter(todo => todo.done)\r\n}\r\nconst defalutList = [\r\n  { text: 'star this repository', done: false },\r\n  { text: 'fork this repository', done: false },\r\n  { text: 'follow author', done: false },\r\n  { text: 'vue-element-admin', done: true },\r\n  { text: 'vue', done: true },\r\n  { text: 'element-ui', done: true },\r\n  { text: 'axios', done: true },\r\n  { text: 'webpack', done: true }\r\n]\r\nexport default {\r\n  components: { Todo },\r\n  filters: {\r\n    pluralize: (n, w) => n === 1 ? w : w + 's',\r\n    capitalize: s => s.charAt(0).toUpperCase() + s.slice(1)\r\n  },\r\n  data() {\r\n    return {\r\n      visibility: 'all',\r\n      filters,\r\n      // todos: JSON.parse(window.localStorage.getItem(STORAGE_KEY)) || defalutList\r\n      todos: defalutList\r\n    }\r\n  },\r\n  computed: {\r\n    allChecked() {\r\n      return this.todos.every(todo => todo.done)\r\n    },\r\n    filteredTodos() {\r\n      return filters[this.visibility](this.todos)\r\n    },\r\n    remaining() {\r\n      return this.todos.filter(todo => !todo.done).length\r\n    }\r\n  },\r\n  methods: {\r\n    setLocalStorage() {\r\n      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(this.todos))\r\n    },\r\n    addTodo(e) {\r\n      const text = e.target.value\r\n      if (text.trim()) {\r\n        this.todos.push({\r\n          text,\r\n          done: false\r\n        })\r\n        this.setLocalStorage()\r\n      }\r\n      e.target.value = ''\r\n    },\r\n    toggleTodo(val) {\r\n      val.done = !val.done\r\n      this.setLocalStorage()\r\n    },\r\n    deleteTodo(todo) {\r\n      this.todos.splice(this.todos.indexOf(todo), 1)\r\n      this.setLocalStorage()\r\n    },\r\n    editTodo({ todo, value }) {\r\n      todo.text = value\r\n      this.setLocalStorage()\r\n    },\r\n    clearCompleted() {\r\n      this.todos = this.todos.filter(todo => !todo.done)\r\n      this.setLocalStorage()\r\n    },\r\n    toggleAll({ done }) {\r\n      this.todos.forEach(todo => {\r\n        todo.done = done\r\n        this.setLocalStorage()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  @import './index.scss';\r\n</style>\r\n"]}]}