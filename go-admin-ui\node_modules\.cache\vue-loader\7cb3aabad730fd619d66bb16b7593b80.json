{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue?vue&type=template&id=a24d5234", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue", "mtime": 1753924830481}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxCYXNpY0xheW91dD4NCiAgICA8dGVtcGxhdGUgI3dyYXBwZXI+DQogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPg0KICAgICAgICA8ZWwtZm9ybT4NCiAgICAgICAgICA8ZWwtZm9ybS1pdGVtPg0KICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJzdWNjZXNzIiBpY29uPSJlbC1pY29uLXNlYXJjaCIgc2l6ZT0ibWluaSI+54q25oCBPC9lbC1idXR0b24+DQogICAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIGljb249ImVsLWljb24tc2VhcmNoIiBzaXplPSJtaW5pIj7muIXnqbo8L2VsLWJ1dHRvbj4NCiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4NCiAgICAgICAgPC9lbC1mb3JtPg0KICAgICAgICA8ZWwtcm93IHJlZj0ibG9nIiA6Z3V0dGVyPSIxMCIgY2xhc3M9Im1iOCI+DQogICAgICAgICAgPGVsLXNjcm9sbGJhciBzdHlsZT0iaGVpZ2h0OjUwMHB4O2JhY2tncm91bmQtY29sb3I6IGJsYWNrO2NvbG9yOiBjb3JuZmxvd2VyYmx1ZTsiPg0KICAgICAgICAgICAgPHVsDQogICAgICAgICAgICAgIHN0eWxlPSJsaW5lLWhlaWdodDogMjVweDtwYWRkaW5nLXRvcDogMTVweDtwYWRkaW5nLWJvdHRvbTogMTVweDttaW4taGVpZ2h0OiA1MDBweDsgbWFyZ2luOiAwO2xpc3Qtc3R5bGUtdHlwZTogbm9uZTsiDQogICAgICAgICAgICA+DQogICAgICAgICAgICAgIDxsaSB2LWZvcj0iKGl0ZW0saW5kZXgpIGluIGFycnMiIDprZXk9ImluZGV4Ij4NCg0KICAgICAgICAgICAgICAgIHt7IGl0ZW0gfX0NCiAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgIDwvdWw+DQogICAgICAgICAgPC9lbC1zY3JvbGxiYXI+DQogICAgICAgIDwvZWwtcm93Pg0KICAgICAgPC9lbC1jYXJkPg0KICAgIDwvdGVtcGxhdGU+DQogIDwvQmFzaWNMYXlvdXQ+DQoNCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue"], "names": [], "mappings": ";EAEE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC,CAAC;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrH;cACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAE3C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;cACX,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/schedule/log.vue", "sourceRoot": "", "sourcesContent": ["\r\n<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form>\r\n          <el-form-item>\r\n            <el-button type=\"success\" icon=\"el-icon-search\" size=\"mini\">状态</el-button>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\">清空</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row ref=\"log\" :gutter=\"10\" class=\"mb8\">\r\n          <el-scrollbar style=\"height:500px;background-color: black;color: cornflowerblue;\">\r\n            <ul\r\n              style=\"line-height: 25px;padding-top: 15px;padding-bottom: 15px;min-height: 500px; margin: 0;list-style-type: none;\"\r\n            >\r\n              <li v-for=\"(item,index) in arrs\" :key=\"index\">\r\n\r\n                {{ item }}\r\n              </li>\r\n            </ul>\r\n          </el-scrollbar>\r\n        </el-row>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\nimport { unWsLogout } from '@/api/ws'\r\nexport default {\r\n  name: 'SysJobLogManage',\r\n  data() {\r\n    return {\r\n      websock: null,\r\n      arrs: [],\r\n      id: undefined,\r\n      group: undefined\r\n    }\r\n  },\r\n  created() {\r\n    this.id = this.guid()\r\n    this.group = 'log'\r\n    this.initWebSocket()\r\n  },\r\n  destroyed() {\r\n    console.log('断开websocket连接')\r\n    this.websock.close() // 离开路由之后断开websocket连接\r\n    unWsLogout(this.id, this.group).then(response => {\r\n      console.log(response.data)\r\n    }\r\n    )\r\n  },\r\n  methods: {\r\n    initWebSocket() { // 初始化weosocket\r\n      console.log(this.$store.state.user.token)\r\n      const wsuri = 'ws://127.0.0.1:8000/ws/' + this.id + '/' + this.group + '?token=' + this.$store.state.user.token\r\n      this.websock = new WebSocket(wsuri)\r\n      this.websock.onmessage = this.websocketonmessage\r\n      this.websock.onopen = this.websocketonopen\r\n      this.websock.onerror = this.websocketonerror\r\n      this.websock.onclose = this.websocketclose\r\n    },\r\n    websocketonopen() { // 连接建立之后执行send方法发送数据\r\n      console.log('连接打开')\r\n    //   const actions = { 'test': '12345' }\r\n    //   this.websocketsend(JSON.stringify(actions))\r\n    },\r\n    websocketonerror() { // 连接建立失败重连\r\n      this.initWebSocket()\r\n    },\r\n    websocketonmessage(e) { // 数据接收\r\n      console.log(e.data)\r\n      //   console.log(this.binaryAgent(e))\r\n      //   const redata = JSON.parse(e.data)\r\n      //   console.log(redata)\r\n      //   this.$refs.log.innerText = e.data + '\\n' + this.$refs.log.innerText\r\n      this.arrs.unshift(e.data)\r\n    },\r\n    websocketsend(Data) { // 数据发送\r\n    //   this.websock.send(Data)\r\n    },\r\n    websocketclose(e) { // 关闭\r\n      unWsLogout(this.id, this.group).then(response => {\r\n        console.log(response.data)\r\n      }\r\n      )\r\n      console.log('断开连接', e)\r\n    },\r\n    guid() {\r\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n        var r = Math.random() * 16 | 0; var v = c === 'x' ? r : (r & 0x3 | 0x8)\r\n        return v.toString(16)\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n</script>\r\n"]}]}