{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue", "mtime": 1753924830436}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getCodeImg", "moment", "SocialSign", "name", "components", "data", "codeUrl", "cookiePassword", "refreshParticles", "loginForm", "username", "password", "rememberMe", "code", "uuid", "loginRules", "required", "trigger", "message", "passwordType", "capsTooltip", "loading", "showDialog", "redirect", "undefined", "other<PERSON><PERSON>y", "currentTime", "sysInfo", "watch", "$route", "handler", "route", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immediate", "created", "getCode", "getCurrentTime", "getSystemSetting", "mounted", "_this", "$refs", "focus", "window", "addEventListener", "$nextTick", "destroyed", "clearInterval", "timer", "removeEventListener", "methods", "_this2", "$store", "dispatch", "then", "ret", "document", "title", "sys_app_name", "_this3", "setInterval", "_", "format", "_this4", "res", "id", "checkCapslock", "_ref", "arguments", "length", "shift<PERSON>ey", "key", "showPwd", "_this5", "handleLogin", "_this6", "validate", "valid", "$router", "push", "path", "catch", "console", "log", "Object", "keys", "reduce", "acc", "cur"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div id=\"particles-js\">\r\n      <!-- <vue-particles\r\n        v-if=\"refreshParticles\"\r\n        color=\"#dedede\"\r\n        :particle-opacity=\"0.7\"\r\n        :particles-number=\"80\"\r\n        shape-type=\"circle\"\r\n        :particle-size=\"4\"\r\n        lines-color=\"#dedede\"\r\n        :lines-width=\"1\"\r\n        :line-linked=\"true\"\r\n        :line-opacity=\"0.4\"\r\n        :lines-distance=\"150\"\r\n        :move-speed=\"3\"\r\n        :hover-effect=\"true\"\r\n        hover-mode=\"grab\"\r\n        :click-effect=\"true\"\r\n        click-mode=\"push\"\r\n      /> -->\r\n    </div>\r\n\r\n    <div class=\"login-weaper animated bounceInDown\">\r\n      <div class=\"login-left\">\r\n        <div class=\"login-time\" v-text=\"currentTime\" />\r\n        <img :src=\"sysInfo.sys_app_logo\" alt=\"\" class=\"img\">\r\n        <p class=\"title\" v-text=\"sysInfo.sys_app_name\" />\r\n      </div>\r\n      <div class=\"login-border\">\r\n        <div class=\"login-main\">\r\n          <div class=\"login-title\">用户登录</div>\r\n          <el-form\r\n            ref=\"loginForm\"\r\n            :model=\"loginForm\"\r\n            :rules=\"loginRules\"\r\n            class=\"login-form\"\r\n            autocomplete=\"on\"\r\n            label-position=\"left\"\r\n          >\r\n            <el-form-item prop=\"username\">\r\n              <span class=\"svg-container\">\r\n                <i class=\"el-icon-user\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.username\"\r\n                placeholder=\"用户名\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"1\"\r\n                autocomplete=\"on\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-tooltip\r\n              v-model=\"capsTooltip\"\r\n              content=\"Caps lock is On\"\r\n              placement=\"right\"\r\n              manual\r\n            >\r\n              <el-form-item prop=\"password\">\r\n                <span class=\"svg-container\">\r\n                  <svg-icon icon-class=\"password\" />\r\n                </span>\r\n                <el-input\r\n                  :key=\"passwordType\"\r\n                  ref=\"password\"\r\n                  v-model=\"loginForm.password\"\r\n                  :type=\"passwordType\"\r\n                  placeholder=\"密码\"\r\n                  name=\"password\"\r\n                  tabindex=\"2\"\r\n                  autocomplete=\"on\"\r\n                  @keyup.native=\"checkCapslock\"\r\n                  @blur=\"capsTooltip = false\"\r\n                  @keyup.enter.native=\"handleLogin\"\r\n                />\r\n                <span class=\"show-pwd\" @click=\"showPwd\">\r\n                  <svg-icon\r\n                    :icon-class=\"\r\n                      passwordType === 'password' ? 'eye' : 'eye-open'\r\n                    \"\r\n                  />\r\n                </span>\r\n              </el-form-item>\r\n            </el-tooltip>\r\n            <el-form-item prop=\"code\" style=\"width: 66%; float: left\">\r\n              <span class=\"svg-container\">\r\n                <svg-icon icon-class=\"validCode\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.code\"\r\n                placeholder=\"验证码\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"3\"\r\n                maxlength=\"5\"\r\n                autocomplete=\"off\"\r\n                style=\"width: 75%\"\r\n                @keyup.enter.native=\"handleLogin\"\r\n              />\r\n            </el-form-item>\r\n            <div\r\n              class=\"login-code\"\r\n              style=\"\r\n                cursor: pointer;\r\n                width: 30%;\r\n                height: 48px;\r\n                float: right;\r\n                background-color: #f0f1f5;\r\n              \"\r\n            >\r\n              <img\r\n                style=\"\r\n                  height: 48px;\r\n                  width: 100%;\r\n                  border: 1px solid rgba(0, 0, 0, 0.1);\r\n                  border-radius: 5px;\r\n                \"\r\n                :src=\"codeUrl\"\r\n                @click=\"getCode\"\r\n              >\r\n            </div>\r\n\r\n            <el-button\r\n              :loading=\"loading\"\r\n              type=\"primary\"\r\n              style=\"width: 100%; padding: 12px 20px; margin-bottom: 30px\"\r\n              @click.native.prevent=\"handleLogin\"\r\n            >\r\n              <span v-if=\"!loading\">登 录</span>\r\n              <span v-else>登 录 中...</span>\r\n            </el-button>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\" :close-on-click-modal=\"false\">\r\n      Can not be simulated on local, so please combine you own business\r\n      simulation! ! !\r\n      <br>\r\n      <br>\r\n      <br>\r\n      <social-sign />\r\n    </el-dialog>\r\n    <div\r\n      id=\"bottom_layer\"\r\n      class=\"s-bottom-layer s-isindex-wrap\"\r\n      style=\"visibility: visible; width: 100%\"\r\n    >\r\n      <div class=\"s-bottom-layer-content\">\r\n\r\n        <div class=\"lh\">\r\n          <a class=\"text-color\" href=\"https://beian.miit.gov.cn\" target=\"_blank\">\r\n            沪ICP备XXXXXXXXX号-1\r\n          </a>\r\n        </div>\r\n        <div class=\"open-content-info\">\r\n          <div class=\"tip-hover-panel\" style=\"top: -18px; right: -12px\">\r\n            <div class=\"rest_info_tip\">\r\n              <div class=\"tip-wrapper\">\r\n                <div class=\"lh tip-item\" style=\"display: none\">\r\n                  <a\r\n                    class=\"text-color\"\r\n                    href=\"https://beian.miit.gov.cn\"\r\n                    target=\"_blank\"\r\n                  >\r\n                    沪ICP备XXXXXXXXX号-1\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from '@/api/login'\r\nimport moment from 'moment'\r\nimport SocialSign from './components/SocialSignin'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: { SocialSign },\r\n  data() {\r\n    return {\r\n      codeUrl: '',\r\n      cookiePassword: '',\r\n      refreshParticles: true,\r\n      loginForm: {\r\n        username: 'admin',\r\n        password: '123456',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: 'blur', message: '用户名不能为空' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: 'blur', message: '密码不能为空' }\r\n        ],\r\n        code: [\r\n          { required: true, trigger: 'change', message: '验证码不能为空' }\r\n        ]\r\n      },\r\n      passwordType: 'password',\r\n      capsTooltip: false,\r\n      loading: false,\r\n      showDialog: false,\r\n      redirect: undefined,\r\n      otherQuery: {},\r\n      currentTime: null,\r\n      sysInfo: ''\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        const query = route.query\r\n        if (query) {\r\n          this.redirect = query.redirect\r\n          this.otherQuery = this.getOtherQuery(query)\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    // window.addEventListener('storage', this.afterQRScan)\r\n    this.getCurrentTime()\r\n    this.getSystemSetting()\r\n  },\r\n  mounted() {\r\n    if (this.loginForm.username === '') {\r\n      this.$refs.username.focus()\r\n    } else if (this.loginForm.password === '') {\r\n      this.$refs.password.focus()\r\n    }\r\n    window.addEventListener('resize', () => {\r\n      this.refreshParticles = false\r\n      this.$nextTick(() => (this.refreshParticles = true))\r\n    })\r\n  },\r\n  destroyed() {\r\n    clearInterval(this.timer)\r\n    window.removeEventListener('resize', () => {})\r\n    // window.removeEventListener('storage', this.afterQRScan)\r\n  },\r\n  methods: {\r\n    getSystemSetting() {\r\n      this.$store.dispatch('system/settingDetail').then((ret) => {\r\n        this.sysInfo = ret\r\n        document.title = ret.sys_app_name\r\n      })\r\n    },\r\n    getCurrentTime() {\r\n      this.timer = setInterval((_) => {\r\n        this.currentTime = moment().format('YYYY-MM-DD HH时mm分ss秒')\r\n      }, 1000)\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        if (res !== undefined) {\r\n          this.codeUrl = res.data\r\n          this.loginForm.uuid = res.id\r\n        }\r\n      })\r\n    },\r\n    checkCapslock({ shiftKey, key } = {}) {\r\n      if (key && key.length === 1) {\r\n        if (\r\n          (shiftKey && key >= 'a' && key <= 'z') ||\r\n          (!shiftKey && key >= 'A' && key <= 'Z')\r\n        ) {\r\n          this.capsTooltip = true\r\n        } else {\r\n          this.capsTooltip = false\r\n        }\r\n      }\r\n      if (key === 'CapsLock' && this.capsTooltip === true) {\r\n        this.capsTooltip = false\r\n      }\r\n    },\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n          this.$store\r\n            .dispatch('user/login', this.loginForm)\r\n            .then(() => {\r\n              this.$router\r\n                .push({ path: this.redirect || '/', query: this.otherQuery })\r\n                .catch(() => {})\r\n            })\r\n            .catch(() => {\r\n              this.loading = false\r\n              this.getCode()\r\n            })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    getOtherQuery(query) {\r\n      return Object.keys(query).reduce((acc, cur) => {\r\n        if (cur !== 'redirect') {\r\n          acc[cur] = query[cur]\r\n        }\r\n        return acc\r\n      }, {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 修复input 背景不协调 和光标变色 */\r\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\r\n\r\n$bg: #283443;\r\n$light_gray: #fff;\r\n$cursor: #fff;\r\n\r\n#bottom_layer {\r\n  visibility: hidden;\r\n  width: 3000px;\r\n  position: fixed;\r\n  z-index: 302;\r\n  bottom: 0;\r\n  left: 0;\r\n  height: 39px;\r\n  padding-top: 1px;\r\n  zoom: 1;\r\n  margin: 0;\r\n  line-height: 39px;\r\n  // background: #0e6cff;\r\n}\r\n#bottom_layer .lh {\r\n  display: inline-block;\r\n  margin-right: 14px;\r\n}\r\n#bottom_layer .lh .emphasize {\r\n  text-decoration: underline;\r\n  font-weight: 700;\r\n}\r\n#bottom_layer .lh:last-child {\r\n  margin-left: -2px;\r\n  margin-right: 0;\r\n}\r\n#bottom_layer .lh.activity {\r\n  font-weight: 700;\r\n  text-decoration: underline;\r\n}\r\n#bottom_layer a {\r\n  font-size: 12px;\r\n  text-decoration: none;\r\n}\r\n#bottom_layer .text-color {\r\n  color: #bbb;\r\n}\r\n#bottom_layer .aria-img {\r\n  width: 49px;\r\n  height: 20px;\r\n  margin-bottom: -5px;\r\n}\r\n#bottom_layer a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .s-bottom-layer-content {\r\n  margin: 0 17px;\r\n  text-align: center;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line {\r\n  display: inline;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line:first-child {\r\n  margin-right: 14px;\r\n}\r\n.s-bottom-space {\r\n  position: static;\r\n  width: 100%;\r\n  height: 40px;\r\n  margin: 23px auto 12px;\r\n}\r\n#bottom_layer .open-content-info a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .open-content-info .text-color {\r\n  color: #626675;\r\n}\r\n.open-content-info {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 20px;\r\n}\r\n.open-content-info > span {\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n.open-content-info > span:hover {\r\n  color: #fff;\r\n}\r\n.open-content-info .tip-hover-panel {\r\n  position: absolute;\r\n  display: none;\r\n  padding-bottom: 18px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip {\r\n  max-width: 560px;\r\n  padding: 8px 12px 8px 12px;\r\n  background: #fff;\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);\r\n  text-align: left;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper {\r\n  white-space: nowrap;\r\n  line-height: 20px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper .tip-item {\r\n  height: 20px;\r\n  line-height: 20px;\r\n}\r\n.open-content-info\r\n  .tip-hover-panel\r\n  .rest_info_tip\r\n  .tip-wrapper\r\n  .tip-item:last-child {\r\n  margin-right: 0;\r\n}\r\n@media screen and (max-width: 515px) {\r\n  .open-content-info {\r\n    width: 16px;\r\n  }\r\n  .open-content-info .tip-hover-panel {\r\n    right: -16px !important;\r\n  }\r\n}\r\n.footer {\r\n  background-color: #0e6cff;\r\n  margin-bottom: -20px;\r\n}\r\n\r\n.login-container {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  margin: 0 auto;\r\n  background: url(\"../../assets/login.png\") no-repeat;\r\n  background-color: #0e6cff;\r\n  position: relative;\r\n  background-size: cover;\r\n  height: 100vh;\r\n  background-position: 50%;\r\n}\r\n\r\n#particles-js {\r\n  z-index: 1;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n}\r\n\r\n.login-weaper {\r\n  margin: 0 auto;\r\n  width: 1000px;\r\n  -webkit-box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  z-index: 1000;\r\n}\r\n\r\n.login-left {\r\n  border-top-left-radius: 5px;\r\n  border-bottom-left-radius: 5px;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-box-direction: normal;\r\n  -ms-flex-direction: column;\r\n  flex-direction: column;\r\n  background-color: rgba(64, 158, 255, 0);\r\n  color: #fff;\r\n  float: left;\r\n  width: 50%;\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  .login-time {\r\n    position: absolute;\r\n    left: 25px;\r\n    top: 25px;\r\n    width: 100%;\r\n    color: #fff;\r\n    opacity: 0.9;\r\n    font-size: 18px;\r\n    overflow: hidden;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.login-left .img {\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 3px;\r\n}\r\n\r\n.login-left .title {\r\n  text-align: center;\r\n  color: #fff;\r\n  letter-spacing: 2px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.login-border {\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  border-left: none;\r\n  border-top-right-radius: 5px;\r\n  border-bottom-right-radius: 5px;\r\n  color: #fff;\r\n  background-color: hsla(0, 0%, 100%, 0.9);\r\n  width: 50%;\r\n  float: left;\r\n}\r\n\r\n.login-main {\r\n  margin: 0 auto;\r\n  width: 65%;\r\n}\r\n\r\n.login-title {\r\n  color: #333;\r\n  margin-bottom: 40px;\r\n  font-weight: 500;\r\n  font-size: 22px;\r\n  text-align: center;\r\n  letter-spacing: 4px;\r\n}\r\n\r\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\r\n  .login-container .el-input input {\r\n    color: $cursor;\r\n  }\r\n}\r\n\r\n/* reset element-ui css */\r\n.login-container {\r\n  ::v-deep .el-input {\r\n    display: inline-block;\r\n    height: 47px;\r\n    width: 85%;\r\n\r\n    input {\r\n      background: transparent;\r\n      border: 0px;\r\n      -webkit-appearance: none;\r\n      border-radius: 0px;\r\n      padding: 12px 5px 12px 15px;\r\n      color: #333;\r\n      height: 47px;\r\n      caret-color: #333;\r\n\r\n      &:-webkit-autofill {\r\n        box-shadow: 0 0 0px 1000px $bg inset !important;\r\n        -webkit-text-fill-color: $cursor !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-form-item {\r\n    border: 1px solid rgba(0, 0, 0, 0.1);\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border-radius: 5px;\r\n    color: #454545;\r\n  }\r\n}\r\n$bg: #2d3a4b;\r\n$dark_gray: #889aa4;\r\n$light_gray: #eee;\r\n\r\n.login-container {\r\n  .tips {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    margin-bottom: 10px;\r\n\r\n    span {\r\n      &:first-of-type {\r\n        margin-right: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .svg-container {\r\n    padding: 6px 5px 6px 15px;\r\n    color: $dark_gray;\r\n    vertical-align: middle;\r\n    width: 30px;\r\n    display: inline-block;\r\n  }\r\n\r\n  .title-container {\r\n    position: relative;\r\n\r\n    .title {\r\n      font-size: 26px;\r\n      color: $light_gray;\r\n      margin: 0px auto 40px auto;\r\n      text-align: center;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .show-pwd {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 7px;\r\n    font-size: 16px;\r\n    color: $dark_gray;\r\n    cursor: pointer;\r\n    user-select: none;\r\n  }\r\n\r\n  .thirdparty-button {\r\n    position: absolute;\r\n    right: 0;\r\n    bottom: 6px;\r\n  }\r\n\r\n  @media only screen and (max-width: 470px) {\r\n    .thirdparty-button {\r\n      display: none;\r\n    }\r\n    .login-weaper {\r\n      width: 100%;\r\n      padding: 0 30px;\r\n      box-sizing: border-box;\r\n      box-shadow: none;\r\n    }\r\n    .login-main {\r\n      width: 80%;\r\n    }\r\n    .login-left {\r\n      display: none !important;\r\n    }\r\n    .login-border {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAuLA,SAASA,UAAS,QAAS,aAAY;AACvC,OAAOC,MAAK,MAAO,QAAO;AAC1B,OAAOC,UAAS,MAAO,2BAA0B;AAEjD,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,UAAU,EAAE;IAAEF,UAAS,EAATA;EAAW,CAAC;EAC1BG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,EAAE;MACXC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,SAAS,EAAE;QACTC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,KAAK;QACjBC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR,CAAC;MACDC,UAAU,EAAE;QACVL,QAAQ,EAAE,CACR;UAAEM,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAU,EACvD;QACDP,QAAQ,EAAE,CACR;UAAEK,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAS,EACtD;QACDL,IAAI,EAAE,CACJ;UAAEG,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAU;MAE5D,CAAC;MACDC,YAAY,EAAE,UAAU;MACxBC,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAEC,SAAS;MACnBC,UAAU,EAAE,CAAC,CAAC;MACdC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,OAAO,EAAE,SAATA,OAAOA,CAAWC,KAAK,EAAE;QACvB,IAAMC,KAAI,GAAID,KAAK,CAACC,KAAI;QACxB,IAAIA,KAAK,EAAE;UACT,IAAI,CAACT,QAAO,GAAIS,KAAK,CAACT,QAAO;UAC7B,IAAI,CAACE,UAAS,GAAI,IAAI,CAACQ,aAAa,CAACD,KAAK;QAC5C;MACF,CAAC;MACDE,SAAS,EAAE;IACb;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC;IACb;IACA,IAAI,CAACC,cAAc,CAAC;IACpB,IAAI,CAACC,gBAAgB,CAAC;EACxB,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,IAAI,CAAC/B,SAAS,CAACC,QAAO,KAAM,EAAE,EAAE;MAClC,IAAI,CAAC+B,KAAK,CAAC/B,QAAQ,CAACgC,KAAK,CAAC;IAC5B,OAAO,IAAI,IAAI,CAACjC,SAAS,CAACE,QAAO,KAAM,EAAE,EAAE;MACzC,IAAI,CAAC8B,KAAK,CAAC9B,QAAQ,CAAC+B,KAAK,CAAC;IAC5B;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,YAAM;MACtCJ,KAAI,CAAChC,gBAAe,GAAI,KAAI;MAC5BgC,KAAI,CAACK,SAAS,CAAC;QAAA,OAAOL,KAAI,CAAChC,gBAAe,GAAI,IAAI;MAAA,CAAC;IACrD,CAAC;EACH,CAAC;EACDsC,SAAS,WAATA,SAASA,CAAA,EAAG;IACVC,aAAa,CAAC,IAAI,CAACC,KAAK;IACxBL,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAE,YAAM,CAAC,CAAC;IAC7C;EACF,CAAC;EACDC,OAAO,EAAE;IACPZ,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MAAA,IAAAa,MAAA;MACjB,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAC,UAACC,GAAG,EAAK;QACzDJ,MAAI,CAACxB,OAAM,GAAI4B,GAAE;QACjBC,QAAQ,CAACC,KAAI,GAAIF,GAAG,CAACG,YAAW;MAClC,CAAC;IACH,CAAC;IACDrB,cAAc,WAAdA,cAAcA,CAAA,EAAG;MAAA,IAAAsB,MAAA;MACf,IAAI,CAACX,KAAI,GAAIY,WAAW,CAAC,UAACC,CAAC,EAAK;QAC9BF,MAAI,CAACjC,WAAU,GAAIzB,MAAM,CAAC,CAAC,CAAC6D,MAAM,CAAC,sBAAsB;MAC3D,CAAC,EAAE,IAAI;IACT,CAAC;IACD1B,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAA2B,MAAA;MACR/D,UAAU,CAAC,CAAC,CAACsD,IAAI,CAAC,UAACU,GAAG,EAAK;QACzB,IAAIA,GAAE,KAAMxC,SAAS,EAAE;UACrBuC,MAAI,CAACzD,OAAM,GAAI0D,GAAG,CAAC3D,IAAG;UACtB0D,MAAI,CAACtD,SAAS,CAACK,IAAG,GAAIkD,GAAG,CAACC,EAAC;QAC7B;MACF,CAAC;IACH,CAAC;IACDC,aAAa,WAAbA,aAAaA,CAAA,EAAyB;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA5C,SAAA,GAAA4C,SAAA,MAAJ,CAAC,CAAC;QAApBE,QAAQ,GAAAH,IAAA,CAARG,QAAQ;QAAEC,GAAE,GAAAJ,IAAA,CAAFI,GAAE;MAC1B,IAAIA,GAAE,IAAKA,GAAG,CAACF,MAAK,KAAM,CAAC,EAAE;QAC3B,IACGC,QAAO,IAAKC,GAAE,IAAK,GAAE,IAAKA,GAAE,IAAK,GAAG,IACpC,CAACD,QAAO,IAAKC,GAAE,IAAK,GAAE,IAAKA,GAAE,IAAK,GAAG,EACtC;UACA,IAAI,CAACnD,WAAU,GAAI,IAAG;QACxB,OAAO;UACL,IAAI,CAACA,WAAU,GAAI,KAAI;QACzB;MACF;MACA,IAAImD,GAAE,KAAM,UAAS,IAAK,IAAI,CAACnD,WAAU,KAAM,IAAI,EAAE;QACnD,IAAI,CAACA,WAAU,GAAI,KAAI;MACzB;IACF,CAAC;IACDoD,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACR,IAAI,IAAI,CAACtD,YAAW,KAAM,UAAU,EAAE;QACpC,IAAI,CAACA,YAAW,GAAI,EAAC;MACvB,OAAO;QACL,IAAI,CAACA,YAAW,GAAI,UAAS;MAC/B;MACA,IAAI,CAAC0B,SAAS,CAAC,YAAM;QACnB4B,MAAI,CAAChC,KAAK,CAAC9B,QAAQ,CAAC+B,KAAK,CAAC;MAC5B,CAAC;IACH,CAAC;IACDgC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACZ,IAAI,CAAClC,KAAK,CAAChC,SAAS,CAACmE,QAAQ,CAAC,UAACC,KAAK,EAAK;QACvC,IAAIA,KAAK,EAAE;UACTF,MAAI,CAACtD,OAAM,GAAI,IAAG;UAClBsD,MAAI,CAACvB,MAAK,CACPC,QAAQ,CAAC,YAAY,EAAEsB,MAAI,CAAClE,SAAS,EACrC6C,IAAI,CAAC,YAAM;YACVqB,MAAI,CAACG,OAAM,CACRC,IAAI,CAAC;cAAEC,IAAI,EAAEL,MAAI,CAACpD,QAAO,IAAK,GAAG;cAAES,KAAK,EAAE2C,MAAI,CAAClD;YAAW,CAAC,EAC3DwD,KAAK,CAAC,YAAM,CAAC,CAAC;UACnB,CAAC,EACAA,KAAK,CAAC,YAAM;YACXN,MAAI,CAACtD,OAAM,GAAI,KAAI;YACnBsD,MAAI,CAACvC,OAAO,CAAC;UACf,CAAC;QACL,OAAO;UACL8C,OAAO,CAACC,GAAG,CAAC,gBAAgB;UAC5B,OAAO,KAAI;QACb;MACF,CAAC;IACH,CAAC;IACDlD,aAAa,WAAbA,aAAaA,CAACD,KAAK,EAAE;MACnB,OAAOoD,MAAM,CAACC,IAAI,CAACrD,KAAK,CAAC,CAACsD,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;QAC7C,IAAIA,GAAE,KAAM,UAAU,EAAE;UACtBD,GAAG,CAACC,GAAG,IAAIxD,KAAK,CAACwD,GAAG;QACtB;QACA,OAAOD,GAAE;MACX,CAAC,EAAE,CAAC,CAAC;IACP;EACF;AACF", "ignoreList": []}]}