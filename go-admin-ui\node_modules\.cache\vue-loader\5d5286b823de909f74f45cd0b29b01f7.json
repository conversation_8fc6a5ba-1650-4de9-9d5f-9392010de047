{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue?vue&type=template&id=4666284a", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue", "mtime": 1753924830290}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}