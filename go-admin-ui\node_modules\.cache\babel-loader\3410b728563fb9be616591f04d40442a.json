{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\dict\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\dict\\index.vue", "mtime": 1753924830269}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["listType", "getType", "delType", "addType", "updateType", "formatJson", "name", "data", "loading", "ids", "single", "multiple", "total", "typeList", "title", "isEdit", "open", "statusOptions", "date<PERSON><PERSON><PERSON>", "queryParams", "pageIndex", "pageSize", "dictName", "undefined", "dictType", "status", "form", "rules", "required", "message", "trigger", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "list", "count", "statusFormat", "row", "column", "selectDictLabel", "parseInt", "cancel", "reset", "id", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "_this3", "dictId", "String", "submitForm", "_this4", "$refs", "validate", "valid", "code", "msgSuccess", "msg", "msgError", "handleDelete", "_this5", "dictIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "catch", "handleExport", "_this6", "downloadLoading", "Promise", "resolve", "_interopRequireWildcard", "require", "excel", "tHeader", "filterVal", "export_json_to_excel", "header", "filename", "autoWidth", "bookType"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\dict\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-width=\"68px\">\r\n          <el-form-item label=\"字典名称\" prop=\"dictName\">\r\n            <el-input\r\n              v-model=\"queryParams.dictName\"\r\n              placeholder=\"请输入字典名称\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"字典类型\" prop=\"dictType\">\r\n            <el-input\r\n              v-model=\"queryParams.dictType\"\r\n              placeholder=\"请输入字典类型\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"字典状态\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in statusOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysDictType:add']\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n            >新增</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysDictType:edit']\"\r\n              type=\"success\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n            >修改</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysDictType:remove']\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysDictType:export']\"\r\n              type=\"warning\"\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n            >导出</el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"typeList\" border @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"字典编号\" width=\"80\" align=\"center\" prop=\"id\" />\r\n          <el-table-column label=\"字典名称\" align=\"center\" prop=\"dictName\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"字典类型\" align=\"center\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <router-link :to=\"{name:'SysDictDataManage', params: {dictId:scope.row.id}}\" class=\"link-type\">\r\n                <span>{{ scope.row.dictType }}</span>\r\n              </router-link>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"status\" :formatter=\"statusFormat\" />\r\n          <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysDictType:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysDictType:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改参数配置对话框 -->\r\n        <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" :close-on-click-modal=\"false\">\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n            <el-form-item label=\"字典名称\" prop=\"dictName\">\r\n              <el-input v-model=\"form.dictName\" placeholder=\"请输入字典名称\" :disabled=\"isEdit\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"字典类型\" prop=\"dictType\">\r\n              <el-input v-model=\"form.dictType\" placeholder=\"请输入字典类型\" :disabled=\"isEdit\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\" prop=\"status\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{ dict.label }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n            </el-form-item>\r\n          </el-form>\r\n          <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n            <el-button @click=\"cancel\">取 消</el-button>\r\n          </div>\r\n        </el-dialog>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listType, getType, delType, addType, updateType } from '@/api/admin/dict/type'\r\nimport { formatJson } from '@/utils'\r\n\r\nexport default {\r\n  name: 'SysDictTypeManage',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 字典表格数据\r\n      typeList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      isEdit: false,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        dictName: undefined,\r\n        dictType: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        dictName: [\r\n          { required: true, message: '字典名称不能为空', trigger: 'blur' }\r\n        ],\r\n        dictType: [\r\n          { required: true, message: '字典类型不能为空', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getDicts('sys_normal_disable').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询字典类型列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listType(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.typeList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 字典状态字典翻译\r\n    statusFormat(row, column) {\r\n      return this.selectDictLabel(this.statusOptions, parseInt(row.status))\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        dictName: undefined,\r\n        dictType: undefined,\r\n        status: '2',\r\n        remark: undefined\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加字典类型'\r\n      this.isEdit = false\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const dictId = row.id || this.ids\r\n      getType(dictId).then(response => {\r\n        this.form = response.data\r\n        this.form.status = String(this.form.status)\r\n        this.open = true\r\n        this.title = '修改字典类型'\r\n        this.isEdit = true\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          this.form.status = parseInt(this.form.status)\r\n          if (this.form.id !== undefined) {\r\n            updateType(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addType(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const dictIds = (row.id && [row.id]) || this.ids\r\n      this.$confirm('是否确认删除字典编号为\"' + dictIds + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delType({ 'ids': dictIds })\r\n      }).then(() => {\r\n        this.getList()\r\n        this.msgSuccess('删除成功')\r\n      }).catch(function() {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // const queryParams = this.queryParams\r\n      this.$confirm('是否确认导出所有类型数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.downloadLoading = true\r\n        import('@/vendor/Export2Excel').then(excel => {\r\n          const tHeader = ['字典编号', '字典名称', '字典类型', '状态', '备注']\r\n          const filterVal = ['id', 'dictName', 'dictType', 'status', 'remark']\r\n          const list = this.typeList\r\n          const data = formatJson(filterVal, list)\r\n          excel.export_json_to_excel({\r\n            header: tHeader,\r\n            data,\r\n            filename: '字典管理',\r\n            autoWidth: true, // Optional\r\n            bookType: 'xlsx' // Optional\r\n          })\r\n          this.downloadLoading = false\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AAwKA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAS,QAAS,uBAAsB;AACtF,SAASC,UAAS,QAAS,SAAQ;AAEnC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,GAAG,EAAE,EAAE;MACP;MACAC,MAAM,EAAE,IAAI;MACZ;MACAC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,CAAC;MACR;MACAC,QAAQ,EAAE,EAAE;MACZ;MACAC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,KAAK;MACb;MACAC,IAAI,EAAE,KAAK;MACX;MACAC,aAAa,EAAE,EAAE;MACjB;MACAC,SAAS,EAAE,EAAE;MACb;MACAC,WAAW,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAEC,SAAS;QACnBC,QAAQ,EAAED,SAAS;QACnBE,MAAM,EAAEF;MACV,CAAC;MACD;MACAG,IAAI,EAAE,CAAC,CAAC;MACR;MACAC,KAAK,EAAE;QACLL,QAAQ,EAAE,CACR;UAAEM,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDN,QAAQ,EAAE,CACR;UAAEI,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO;MAE3D;IACF;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,OAAO,CAAC;IACb,IAAI,CAACC,QAAQ,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAC,UAAAC,QAAO,EAAK;MACnDJ,KAAI,CAACf,aAAY,GAAImB,QAAQ,CAAC7B,IAAG;IACnC,CAAC;EACH,CAAC;EACD8B,OAAO,EAAE;IACP,eACAJ,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAK,MAAA;MACR,IAAI,CAAC9B,OAAM,GAAI,IAAG;MAClBR,QAAQ,CAAC,IAAI,CAACuC,YAAY,CAAC,IAAI,CAACpB,WAAW,EAAE,IAAI,CAACD,SAAS,CAAC,CAAC,CAACiB,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC7EE,MAAI,CAACzB,QAAO,GAAIuB,QAAQ,CAAC7B,IAAI,CAACiC,IAAG;QACjCF,MAAI,CAAC1B,KAAI,GAAIwB,QAAQ,CAAC7B,IAAI,CAACkC,KAAI;QAC/BH,MAAI,CAAC9B,OAAM,GAAI,KAAI;MACrB,CAAC;IACH,CAAC;IACD;IACAkC,YAAY,WAAZA,YAAYA,CAACC,GAAG,EAAEC,MAAM,EAAE;MACxB,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC5B,aAAa,EAAE6B,QAAQ,CAACH,GAAG,CAAClB,MAAM,CAAC;IACtE,CAAC;IACD;IACAsB,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC/B,IAAG,GAAI,KAAI;MAChB,IAAI,CAACgC,KAAK,CAAC;IACb,CAAC;IACD;IACAA,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACtB,IAAG,GAAI;QACVuB,EAAE,EAAE1B,SAAS;QACbD,QAAQ,EAAEC,SAAS;QACnBC,QAAQ,EAAED,SAAS;QACnBE,MAAM,EAAE,GAAG;QACXyB,MAAM,EAAE3B;MACV;MACA,IAAI,CAAC4B,SAAS,CAAC,MAAM;IACvB,CAAC;IACD,aACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACjC,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAACa,OAAO,CAAC;IACf,CAAC;IACD,aACAoB,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAACnC,SAAQ,GAAI,EAAC;MAClB,IAAI,CAACiC,SAAS,CAAC,WAAW;MAC1B,IAAI,CAACC,WAAW,CAAC;IACnB,CAAC;IACD,aACAE,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACN,KAAK,CAAC;MACX,IAAI,CAAChC,IAAG,GAAI,IAAG;MACf,IAAI,CAACF,KAAI,GAAI,QAAO;MACpB,IAAI,CAACC,MAAK,GAAI,KAAI;IACpB,CAAC;IACD;IACAwC,qBAAqB,WAArBA,qBAAqBA,CAACC,SAAS,EAAE;MAC/B,IAAI,CAAC/C,GAAE,GAAI+C,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACT,EAAE;MAAA;MACxC,IAAI,CAACvC,MAAK,GAAI8C,SAAS,CAACG,MAAK,KAAM;MACnC,IAAI,CAAChD,QAAO,GAAI,CAAC6C,SAAS,CAACG,MAAK;IAClC,CAAC;IACD,aACAC,YAAY,WAAZA,YAAYA,CAACjB,GAAG,EAAE;MAAA,IAAAkB,MAAA;MAChB,IAAI,CAACb,KAAK,CAAC;MACX,IAAMc,MAAK,GAAInB,GAAG,CAACM,EAAC,IAAK,IAAI,CAACxC,GAAE;MAChCR,OAAO,CAAC6D,MAAM,CAAC,CAAC3B,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC/ByB,MAAI,CAACnC,IAAG,GAAIU,QAAQ,CAAC7B,IAAG;QACxBsD,MAAI,CAACnC,IAAI,CAACD,MAAK,GAAIsC,MAAM,CAACF,MAAI,CAACnC,IAAI,CAACD,MAAM;QAC1CoC,MAAI,CAAC7C,IAAG,GAAI,IAAG;QACf6C,MAAI,CAAC/C,KAAI,GAAI,QAAO;QACpB+C,MAAI,CAAC9C,MAAK,GAAI,IAAG;MACnB,CAAC;IACH,CAAC;IACD;IACAiD,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAa;MAAA,IAAAC,MAAA;MACrB,IAAI,CAACC,KAAK,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAIA,KAAK,EAAE;UACTH,MAAI,CAACvC,IAAI,CAACD,MAAK,GAAIqB,QAAQ,CAACmB,MAAI,CAACvC,IAAI,CAACD,MAAM;UAC5C,IAAIwC,MAAI,CAACvC,IAAI,CAACuB,EAAC,KAAM1B,SAAS,EAAE;YAC9BnB,UAAU,CAAC6D,MAAI,CAACvC,IAAI,CAAC,CAACS,IAAI,CAAC,UAAAC,QAAO,EAAK;cACrC,IAAIA,QAAQ,CAACiC,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAAClC,QAAQ,CAACmC,GAAG;gBAC5BN,MAAI,CAACjD,IAAG,GAAI,KAAI;gBAChBiD,MAAI,CAAChC,OAAO,CAAC;cACf,OAAO;gBACLgC,MAAI,CAACO,QAAQ,CAACpC,QAAQ,CAACmC,GAAG;cAC5B;YACF,CAAC;UACH,OAAO;YACLpE,OAAO,CAAC8D,MAAI,CAACvC,IAAI,CAAC,CAACS,IAAI,CAAC,UAAAC,QAAO,EAAK;cAClC,IAAIA,QAAQ,CAACiC,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAAClC,QAAQ,CAACmC,GAAG;gBAC5BN,MAAI,CAACjD,IAAG,GAAI,KAAI;gBAChBiD,MAAI,CAAChC,OAAO,CAAC;cACf,OAAO;gBACLgC,MAAI,CAACO,QAAQ,CAACpC,QAAQ,CAACmC,GAAG;cAC5B;YACF,CAAC;UACH;QACF;MACF,CAAC;IACH,CAAC;IACD,aACAE,YAAY,WAAZA,YAAYA,CAAC9B,GAAG,EAAE;MAAA,IAAA+B,MAAA;MAChB,IAAMC,OAAM,GAAKhC,GAAG,CAACM,EAAC,IAAK,CAACN,GAAG,CAACM,EAAE,CAAC,IAAK,IAAI,CAACxC,GAAE;MAC/C,IAAI,CAACmE,QAAQ,CAAC,cAAa,GAAID,OAAM,GAAI,QAAQ,EAAE,IAAI,EAAE;QACvDE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC5C,IAAI,CAAC,YAAW;QACjB,OAAOjC,OAAO,CAAC;UAAE,KAAK,EAAEyE;QAAQ,CAAC;MACnC,CAAC,CAAC,CAACxC,IAAI,CAAC,YAAM;QACZuC,MAAI,CAACzC,OAAO,CAAC;QACbyC,MAAI,CAACJ,UAAU,CAAC,MAAM;MACxB,CAAC,CAAC,CAACU,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACD,aACAC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACb;MACA,IAAI,CAACN,QAAQ,CAAC,gBAAgB,EAAE,IAAI,EAAE;QACpCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC5C,IAAI,CAAC,YAAM;QACZ+C,MAAI,CAACC,eAAc,GAAI,IAAG;QAC1BC,OAAA,CAAAC,OAAA,GAAAlD,IAAA;UAAA,OAAAmD,uBAAA,CAAAC,OAAA,CAAO,uBAAuB;QAAA,GAAEpD,IAAI,CAAC,UAAAqD,KAAI,EAAK;UAC5C,IAAMC,OAAM,GAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;UACnD,IAAMC,SAAQ,GAAI,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;UACnE,IAAMlD,IAAG,GAAI0C,MAAI,CAACrE,QAAO;UACzB,IAAMN,IAAG,GAAIF,UAAU,CAACqF,SAAS,EAAElD,IAAI;UACvCgD,KAAK,CAACG,oBAAoB,CAAC;YACzBC,MAAM,EAAEH,OAAO;YACflF,IAAI,EAAJA,IAAI;YACJsF,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,IAAI;YAAE;YACjBC,QAAQ,EAAE,MAAK,CAAE;UACnB,CAAC;UACDb,MAAI,CAACC,eAAc,GAAI,KAAI;QAC7B,CAAC;MACH,CAAC;IACH;EACF;AACF", "ignoreList": []}]}