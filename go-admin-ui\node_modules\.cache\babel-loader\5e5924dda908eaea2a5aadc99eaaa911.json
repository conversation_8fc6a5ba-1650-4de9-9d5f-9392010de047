{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-role\\index.vue", "mtime": 1753924830280}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkIGZyb20gIkQ6L0Rlc2t0b3AvR29BZG1pbi9nby1hZG1pbi11aS9ub2RlX21vZHVsZXMvLnN0b3JlL0BiYWJlbCtydW50aW1lQDcuMjguMi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vaW50ZXJvcFJlcXVpcmVXaWxkY2FyZC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IubWFwLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgeyBsaXN0Um9sZSwgZ2V0Um9sZSwgZGVsUm9sZSwgYWRkUm9sZSwgdXBkYXRlUm9sZSwgZGF0YVNjb3BlLCBjaGFuZ2VSb2xlU3RhdHVzIH0gZnJvbSAnQC9hcGkvYWRtaW4vc3lzLXJvbGUnOwppbXBvcnQgeyByb2xlTWVudVRyZWVzZWxlY3QgfSBmcm9tICdAL2FwaS9hZG1pbi9zeXMtbWVudSc7CmltcG9ydCB7IHRyZWVzZWxlY3QgYXMgZGVwdFRyZWVzZWxlY3QsIHJvbGVEZXB0VHJlZXNlbGVjdCB9IGZyb20gJ0AvYXBpL2FkbWluL3N5cy1kZXB0JzsKaW1wb3J0IHsgZm9ybWF0SnNvbiB9IGZyb20gJ0AvdXRpbHMnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1JvbGUnLAogIGNvbXBvbmVudHM6IHt9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDop5LoibLooajmoLzmlbDmja4KICAgICAgcm9sZUxpc3Q6IFtdLAogICAgICBtZW51SWRzQ2hlY2tlZDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogJycsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI5pWw5o2u5p2D6ZmQ77yJCiAgICAgIG9wZW5EYXRhU2NvcGU6IGZhbHNlLAogICAgICBpc0VkaXQ6IGZhbHNlLAogICAgICAvLyDml6XmnJ/ojIPlm7QKICAgICAgZGF0ZVJhbmdlOiBbXSwKICAgICAgLy8g54q25oCB5pWw5o2u5a2X5YW4CiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmlbDmja7ojIPlm7TpgInpobkKICAgICAgZGF0YVNjb3BlT3B0aW9uczogW3sKICAgICAgICB2YWx1ZTogJzEnLAogICAgICAgIGxhYmVsOiAn5YWo6YOo5pWw5o2u5p2D6ZmQJwogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICcyJywKICAgICAgICBsYWJlbDogJ+iHquWumuaVsOaNruadg+mZkCcKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAnMycsCiAgICAgICAgbGFiZWw6ICfmnKzpg6jpl6jmlbDmja7mnYPpmZAnCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogJzQnLAogICAgICAgIGxhYmVsOiAn5pys6YOo6Zeo5Y+K5Lul5LiL5pWw5o2u5p2D6ZmQJwogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICc1JywKICAgICAgICBsYWJlbDogJ+S7heacrOS6uuaVsOaNruadg+mZkCcKICAgICAgfV0sCiAgICAgIC8vIOiPnOWNleWIl+ihqAogICAgICBtZW51T3B0aW9uczogW10sCiAgICAgIG1lbnVMaXN0OiBbXSwKICAgICAgLy8g6YOo6Zeo5YiX6KGoCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgbWVudU9wdGlvbnNBbGVydDogJ+WKoOi9veS4re+8jOivt+eojeWQjicsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VJbmRleDogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgICByb2xlS2V5OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHsKICAgICAgICBzeXNNZW51OiBbXQogICAgICB9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywKICAgICAgICBsYWJlbDogJ2xhYmVsJwogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICByb2xlTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+inkuiJsuWQjeensOS4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICByb2xlS2V5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5p2D6ZmQ5a2X56ym5LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dLAogICAgICAgIHJvbGVTb3J0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6KeS6Imy6aG65bqP5LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRNZW51VHJlZXNlbGVjdCgpOwogICAgdGhpcy5nZXREaWN0cygnc3lzX25vcm1hbF9kaXNhYmxlJykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuc3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lop5LoibLliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFJvbGUodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5yb2xlTGlzdCA9IHJlc3BvbnNlLmRhdGEubGlzdDsKICAgICAgICBfdGhpczIudG90YWwgPSByZXNwb25zZS5kYXRhLmNvdW50OwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi9nZXRNZW51VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0TWVudVRyZWVzZWxlY3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByb2xlTWVudVRyZWVzZWxlY3QoMCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczMubWVudU9wdGlvbnMgPSByZXNwb25zZS5kYXRhLm1lbnVzOwogICAgICAgIF90aGlzMy5tZW51TGlzdCA9IF90aGlzMy5tZW51T3B0aW9uczsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeivoumDqOmXqOagkee7k+aehCAqL2dldERlcHRUcmVlc2VsZWN0OiBmdW5jdGlvbiBnZXREZXB0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIGRlcHRUcmVlc2VsZWN0KCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhLmxpc3Q7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaJgOacieiPnOWNleiKgueCueaVsOaNrgogICAgZ2V0TWVudUFsbENoZWNrZWRLZXlzOiBmdW5jdGlvbiBnZXRNZW51QWxsQ2hlY2tlZEtleXMoKSB7CiAgICAgIC8vIOebruWJjeiiq+mAieS4reeahOiPnOWNleiKgueCuQogICAgICB2YXIgY2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLm1lbnVUcmVlLmdldEhhbGZDaGVja2VkS2V5cygpOwogICAgICBjb25zb2xlLmxvZygn55uu5YmN6KKr6YCJ5Lit55qE6I+c5Y2V6IqC54K5JywgY2hlY2tlZEtleXMpOwogICAgICAvLyDljYrpgInkuK3nmoToj5zljZXoioLngrkKICAgICAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMubWVudVRyZWUuZ2V0Q2hlY2tlZEtleXMoKTsKICAgICAgY29uc29sZS5sb2coJ+WNiumAieS4reeahOiPnOWNleiKgueCuScsIGhhbGZDaGVja2VkS2V5cyk7CiAgICAgIC8vIGNoZWNrZWRLZXlzLnVuc2hpZnQuYXBwbHkoY2hlY2tlZEtleXMsIGhhbGZDaGVja2VkS2V5cykKICAgICAgcmV0dXJuIGhhbGZDaGVja2VkS2V5czsKICAgIH0sCiAgICAvLyDmiYDmnInpg6jpl6joioLngrnmlbDmja4KICAgIGdldERlcHRBbGxDaGVja2VkS2V5czogZnVuY3Rpb24gZ2V0RGVwdEFsbENoZWNrZWRLZXlzKCkgewogICAgICAvLyDnm67liY3ooqvpgInkuK3nmoTpg6jpl6joioLngrkKICAgICAgdmFyIGNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5kZXB0LmdldENoZWNrZWRLZXlzKCk7CiAgICAgIC8vIOWNiumAieS4reeahOmDqOmXqOiKgueCuQogICAgICAvLyBjb25zdCBoYWxmQ2hlY2tlZEtleXMgPSB0aGlzLiRyZWZzLmRlcHQuZ2V0Q2hlY2tlZEtleXMoKQogICAgICAvLyBjaGVja2VkS2V5cy51bnNoaWZ0LmFwcGx5KGNoZWNrZWRLZXlzLCBoYWxmQ2hlY2tlZEtleXMpCiAgICAgIHJldHVybiBjaGVja2VkS2V5czsKICAgIH0sCiAgICAvKiog5qC55o2u6KeS6ImySUTmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi9nZXRSb2xlTWVudVRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldFJvbGVNZW51VHJlZXNlbGVjdChyb3csIGNoZWNrZWRLZXlzKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBpZiAocm93LnJvbGVLZXkgPT09ICdhZG1pbicpIHsKICAgICAgICB0aGlzLm1lbnVPcHRpb25zQWxlcnQgPSAn57O757uf6LaF57qn566h55CG5ZGY5peg6ZyA5q2k5pON5L2cJzsKICAgICAgICB0aGlzLm1lbnVPcHRpb25zID0gW107CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM1LiRyZWZzLm1lbnVUcmVlLnNldENoZWNrZWRLZXlzKGNoZWNrZWRLZXlzKTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivoumDqOmXqOagkee7k+aehCAqL2dldFJvbGVEZXB0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0Um9sZURlcHRUcmVlc2VsZWN0KHJvbGVJZCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgcm9sZURlcHRUcmVlc2VsZWN0KHJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczYuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhLmRlcHRzOwogICAgICAgIF90aGlzNi4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM2LiRyZWZzLmRlcHQuc2V0Q2hlY2tlZEtleXMocmVzcG9uc2UuZGF0YS5jaGVja2VkS2V5cyk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOinkuiJsueKtuaAgeS/ruaUuQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICB2YXIgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICcyJyA/ICflkK/nlKgnIDogJ+WBnOeUqCc7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cucm9sZU5hbWUgKyAnIuinkuiJsuWQlz8nLCAn6K2m5ZGKJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGNoYW5nZVJvbGVTdGF0dXMocm93LnJvbGVJZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGNvbnNvbGUubG9nKCdyZXMnLCByZXMpOwogICAgICAgIF90aGlzNy5tc2dTdWNjZXNzKHJlcy5tc2cpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICcyJyA/ICcxJyA6ICcyJzsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq7vvIjmlbDmja7mnYPpmZDvvIkKICAgIGNhbmNlbERhdGFTY29wZTogZnVuY3Rpb24gY2FuY2VsRGF0YVNjb3BlKCkgewogICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLm1lbnVPcHRpb25zID0gdGhpcy5tZW51TGlzdDsKICAgICAgaWYgKHRoaXMuJHJlZnMubWVudVRyZWUgIT09IHVuZGVmaW5lZCkgewogICAgICAgIHRoaXMuJHJlZnMubWVudVRyZWUuc2V0Q2hlY2tlZEtleXMoW10pOwogICAgICB9CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICByb2xlSWQ6IHVuZGVmaW5lZCwKICAgICAgICByb2xlTmFtZTogdW5kZWZpbmVkLAogICAgICAgIHJvbGVLZXk6IHVuZGVmaW5lZCwKICAgICAgICByb2xlU29ydDogMCwKICAgICAgICBzdGF0dXM6ICcyJywKICAgICAgICBtZW51SWRzOiBbXSwKICAgICAgICBkZXB0SWRzOiBbXSwKICAgICAgICBzeXNNZW51OiBbXSwKICAgICAgICByZW1hcms6IHVuZGVmaW5lZAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgnZm9ybScpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZUluZGV4ID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqL3Jlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCdxdWVyeUZvcm0nKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnJvbGVJZDsKICAgICAgfSk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICAvLyB0aGlzLmdldE1lbnVUcmVlc2VsZWN0KDApCiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg6KeS6ImyJzsKICAgICAgdGhpcy5pc0VkaXQgPSBmYWxzZTsKICAgIH0sCiAgICBoYW5kbGVTb3J0Q2hhbmc6IGZ1bmN0aW9uIGhhbmRsZVNvcnRDaGFuZyhjb2x1bW4sIHByb3AsIG9yZGVyKSB7CiAgICAgIHByb3AgPSBjb2x1bW4ucHJvcDsKICAgICAgb3JkZXIgPSBjb2x1bW4ub3JkZXI7CiAgICAgIGlmIChvcmRlciA9PT0gJ2Rlc2NlbmRpbmcnKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1twcm9wICsgJ09yZGVyJ10gPSAnZGVzYyc7CiAgICAgIH0gZWxzZSBpZiAob3JkZXIgPT09ICdhc2NlbmRpbmcnKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1twcm9wICsgJ09yZGVyJ10gPSAnYXNjJzsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zW3Byb3AgKyAnT3JkZXInXSA9IHVuZGVmaW5lZDsKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICB0aGlzLm1lbnVJZHNDaGVja2VkID0gW107CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdmFyIHJvbGVJZCA9IHJvdy5yb2xlSWQgfHwgdGhpcy5pZHM7CiAgICAgIGdldFJvbGUocm9sZUlkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzOC5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICBfdGhpczgubWVudUlkc0NoZWNrZWQgPSByZXNwb25zZS5kYXRhLm1lbnVJZHM7CiAgICAgICAgX3RoaXM4LnRpdGxlID0gJ+S/ruaUueinkuiJsic7CiAgICAgICAgX3RoaXM4LmlzRWRpdCA9IHRydWU7CiAgICAgICAgX3RoaXM4Lm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzOC5nZXRSb2xlTWVudVRyZWVzZWxlY3Qocm93LCByZXNwb25zZS5kYXRhLm1lbnVJZHMpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5YiG6YWN5pWw5o2u5p2D6ZmQ5pON5L2cICovaGFuZGxlRGF0YVNjb3BlOiBmdW5jdGlvbiBoYW5kbGVEYXRhU2NvcGUocm93KSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGdldFJvbGUocm93LnJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczkuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM5Lm9wZW5EYXRhU2NvcGUgPSB0cnVlOwogICAgICAgIF90aGlzOS50aXRsZSA9ICfliIbphY3mlbDmja7mnYPpmZAnOwogICAgICAgIF90aGlzOS5nZXRSb2xlRGVwdFRyZWVzZWxlY3Qocm93LnJvbGVJZCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczAgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBpZiAoX3RoaXMwLmZvcm0ucm9sZUlkICE9PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgX3RoaXMwLmZvcm0ubWVudUlkcyA9IF90aGlzMC5nZXRNZW51QWxsQ2hlY2tlZEtleXMoKTsKICAgICAgICAgICAgdXBkYXRlUm9sZShfdGhpczAuZm9ybSwgX3RoaXMwLmZvcm0ucm9sZUlkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICAgIF90aGlzMC5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgICAgICBfdGhpczAub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgICAgX3RoaXMwLmdldExpc3QoKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXMwLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMC5mb3JtLm1lbnVJZHMgPSBfdGhpczAuZ2V0TWVudUFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgICAgIGFkZFJvbGUoX3RoaXMwLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgICAgICAgX3RoaXMwLm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgICAgICAgICAgIF90aGlzMC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgICBfdGhpczAuZ2V0TGlzdCgpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczAubXNnRXJyb3IocmVzcG9uc2UubXNnKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSru+8iOaVsOaNruadg+mZkO+8iSAqLwogICAgc3VibWl0RGF0YVNjb3BlOiBmdW5jdGlvbiBzdWJtaXREYXRhU2NvcGUoKSB7CiAgICAgIHZhciBfdGhpczEgPSB0aGlzOwogICAgICBpZiAodGhpcy5mb3JtLnJvbGVJZCAhPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy5mb3JtLmRlcHRJZHMgPSB0aGlzLmdldERlcHRBbGxDaGVja2VkS2V5cygpOwogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZ2V0RGVwdEFsbENoZWNrZWRLZXlzKCkpOwogICAgICAgIGRhdGFTY29wZSh0aGlzLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgIF90aGlzMS5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgIF90aGlzMS5vcGVuRGF0YVNjb3BlID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzMS5nZXRMaXN0KCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczEubXNnRXJyb3IocmVzcG9uc2UubXNnKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMTAgPSB0aGlzOwogICAgICB2YXIgcm9sZUlkcyA9IHJvdy5yb2xlSWQgJiYgW3Jvdy5yb2xlSWRdIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTop5LoibLnvJblj7fkuLoiJyArIHJvbGVJZHMgKyAnIueahOaVsOaNrumhuT8nLCAn6K2m5ZGKJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbFJvbGUoewogICAgICAgICAgJ2lkcyc6IHJvbGVJZHMKICAgICAgICB9KTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczEwLmdldExpc3QoKTsKICAgICAgICBfdGhpczEwLm1zZ1N1Y2Nlc3MocmVzcG9uc2UubXNnKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInop5LoibLmlbDmja7pobk/JywgJ+itpuWRiicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMTEuZG93bmxvYWRMb2FkaW5nID0gdHJ1ZTsKICAgICAgICBQcm9taXNlLnJlc29sdmUoKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHJldHVybiBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCdAL3ZlbmRvci9FeHBvcnQyRXhjZWwnKSk7CiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoZXhjZWwpIHsKICAgICAgICAgIHZhciB0SGVhZGVyID0gWyfop5LoibLnvJblj7cnLCAn6KeS6Imy5ZCN56ewJywgJ+adg+mZkOWtl+espicsICfmmL7npLrpobrluo8nLCAn54q25oCBJywgJ+WIm+W7uuaXtumXtCddOwogICAgICAgICAgdmFyIGZpbHRlclZhbCA9IFsncm9sZUlkJywgJ3JvbGVOYW1lJywgJ3JvbGVLZXknLCAncm9sZVNvcnQnLCAnc3RhdHVzJywgJ2NyZWF0ZWRBdCddOwogICAgICAgICAgdmFyIGxpc3QgPSBfdGhpczExLnJvbGVMaXN0OwogICAgICAgICAgdmFyIGRhdGEgPSBmb3JtYXRKc29uKGZpbHRlclZhbCwgbGlzdCk7CiAgICAgICAgICBleGNlbC5leHBvcnRfanNvbl90b19leGNlbCh7CiAgICAgICAgICAgIGhlYWRlcjogdEhlYWRlciwKICAgICAgICAgICAgZGF0YTogZGF0YSwKICAgICAgICAgICAgZmlsZW5hbWU6ICfop5LoibLnrqHnkIYnLAogICAgICAgICAgICBhdXRvV2lkdGg6IHRydWUsCiAgICAgICAgICAgIC8vIE9wdGlvbmFsCiAgICAgICAgICAgIGJvb2tUeXBlOiAneGxzeCcgLy8gT3B0aW9uYWwKICAgICAgICAgIH0pOwogICAgICAgICAgX3RoaXMxMS5kb3dubG9hZExvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["listRole", "getRole", "delRole", "addRole", "updateRole", "dataScope", "changeRoleStatus", "roleMenuTreeselect", "treeselect", "deptTreeselect", "roleDeptTreeselect", "formatJson", "name", "components", "data", "loading", "ids", "single", "multiple", "total", "roleList", "menuIdsChecked", "title", "open", "openDataScope", "isEdit", "date<PERSON><PERSON><PERSON>", "statusOptions", "dataScopeOptions", "value", "label", "menuOptions", "menuList", "deptOptions", "menuOptionsAlert", "queryParams", "pageIndex", "pageSize", "<PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "status", "form", "sysMenu", "defaultProps", "children", "rules", "required", "message", "trigger", "roleSort", "created", "_this", "getList", "getMenuTreeselect", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "list", "count", "_this3", "menus", "getDeptTreeselect", "_this4", "getMenuAllCheckedKeys", "checked<PERSON>eys", "$refs", "menuTree", "getHalfCheckedKeys", "console", "log", "halfC<PERSON>cked<PERSON>eys", "getChe<PERSON><PERSON>eys", "getDeptAllCheckedKeys", "dept", "getRoleMenuTreeselect", "row", "_this5", "$nextTick", "set<PERSON><PERSON><PERSON><PERSON>eys", "getRoleDeptTreeselect", "roleId", "_this6", "depts", "handleStatusChange", "_this7", "text", "$confirm", "confirmButtonText", "cancelButtonText", "type", "res", "msgSuccess", "msg", "catch", "cancel", "reset", "cancelDataScope", "menuIds", "deptIds", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleSortChang", "column", "prop", "order", "handleUpdate", "_this8", "handleDataScope", "_this9", "submitForm", "_this0", "validate", "valid", "code", "msgError", "submitDataScope", "_this1", "handleDelete", "_this10", "roleIds", "handleExport", "_this11", "downloadLoading", "Promise", "resolve", "_interopRequireWildcard", "require", "excel", "tHeader", "filterVal", "export_json_to_excel", "header", "filename", "autoWidth", "bookType"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-role\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\">\r\n          <el-form-item label=\"名称\" prop=\"roleName\">\r\n            <el-input\r\n              v-model=\"queryParams.roleName\"\r\n              placeholder=\"请输入角色名称\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"权限字符\" prop=\"roleKey\">\r\n            <el-input\r\n              v-model=\"queryParams.roleKey\"\r\n              placeholder=\"请输入权限字符\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 160px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"角色状态\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 160px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in statusOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              size=\"small\"\r\n              style=\"width: 240px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            />\r\n          </el-form-item> -->\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysRole:add']\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n            >新增</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysRole:update']\"\r\n              type=\"success\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n            >修改</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysRole:remove']\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysRole:export']\"\r\n              type=\"warning\"\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n            >导出</el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"roleList\"\r\n          border\r\n          @selection-change=\"handleSelectionChange\"\r\n          @sort-change=\"handleSortChang\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"编码\" sortable=\"custom\" prop=\"roleId\" width=\"80\" />\r\n          <el-table-column label=\"名称\" sortable=\"custom\" prop=\"roleName\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n          <el-table-column label=\"排序\" sortable=\"custom\" prop=\"roleSort\" width=\"80\" />\r\n          <el-table-column label=\"状态\" sortable=\"custom\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"2\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" sortable=\"custom\" prop=\"createdAt\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"left\"\r\n            class-name=\"small-padding fixed-width\"\r\n            width=\"220\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysRole:update']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysRole:update']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-circle-check\"\r\n                @click=\"handleDataScope(scope.row)\"\r\n              >数据权限</el-button>\r\n              <el-button\r\n                v-if=\"scope.row.roleKey!=='admin'\"\r\n                v-permisaction=\"['admin:sysRole:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改角色配置对话框 -->\r\n        <el-dialog v-if=\"open\" :title=\"title\" :visible.sync=\"open\" width=\"500px\" :close-on-click-modal=\"false\">\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n            <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n              <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" :disabled=\"isEdit\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"权限字符\" prop=\"roleKey\">\r\n              <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" :disabled=\"isEdit\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"角色顺序\" prop=\"roleSort\">\r\n              <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in statusOptions\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{ dict.label }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"菜单权限\">\r\n              <el-tree\r\n                ref=\"menuTree\"\r\n                :data=\"menuOptions\"\r\n                show-checkbox\r\n                node-key=\"id\"\r\n                :empty-text=\"menuOptionsAlert\"\r\n                style=\"height:171px;overflow-y:auto;overflow-x:hidden;\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"备注\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n            </el-form-item>\r\n          </el-form>\r\n          <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n            <el-button @click=\"cancel\">取 消</el-button>\r\n          </div>\r\n        </el-dialog>\r\n\r\n        <!-- 分配角色数据权限对话框 -->\r\n        <el-dialog v-if=\"openDataScope\" :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" :close-on-click-modal=\"false\">\r\n          <el-form :model=\"form\" label-width=\"80px\">\r\n            <el-form-item label=\"角色名称\">\r\n              <el-input v-model=\"form.roleName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"权限字符\">\r\n              <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"权限范围\">\r\n              <el-select v-model=\"form.dataScope\">\r\n                <el-option\r\n                  v-for=\"item in dataScopeOptions\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item v-show=\"form.dataScope == 2\" label=\"数据权限\">\r\n              <el-tree\r\n                ref=\"dept\"\r\n                :data=\"deptOptions\"\r\n                show-checkbox\r\n                default-expand-all\r\n                node-key=\"id\"\r\n                empty-text=\"加载中，请稍后\"\r\n                :props=\"defaultProps\"\r\n              />\r\n            </el-form-item>\r\n          </el-form>\r\n          <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\r\n            <el-button @click=\"cancelDataScope\">取 消</el-button>\r\n          </div>\r\n        </el-dialog>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus } from '@/api/admin/sys-role'\r\nimport { roleMenuTreeselect } from '@/api/admin/sys-menu'\r\nimport { treeselect as deptTreeselect, roleDeptTreeselect } from '@/api/admin/sys-dept'\r\nimport { formatJson } from '@/utils'\r\n\r\nexport default {\r\n  name: 'Role',\r\n  components: {\r\n\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 角色表格数据\r\n      roleList: [],\r\n      menuIdsChecked: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示弹出层（数据权限）\r\n      openDataScope: false,\r\n      isEdit: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 数据范围选项\r\n      dataScopeOptions: [\r\n        {\r\n          value: '1',\r\n          label: '全部数据权限'\r\n        },\r\n        {\r\n          value: '2',\r\n          label: '自定数据权限'\r\n        },\r\n        {\r\n          value: '3',\r\n          label: '本部门数据权限'\r\n        },\r\n        {\r\n          value: '4',\r\n          label: '本部门及以下数据权限'\r\n        },\r\n        {\r\n          value: '5',\r\n          label: '仅本人数据权限'\r\n        }\r\n      ],\r\n      // 菜单列表\r\n      menuOptions: [],\r\n      menuList: [],\r\n      // 部门列表\r\n      deptOptions: [],\r\n      menuOptionsAlert: '加载中，请稍后',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        sysMenu: []\r\n      },\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        roleName: [\r\n          { required: true, message: '角色名称不能为空', trigger: 'blur' }\r\n        ],\r\n        roleKey: [\r\n          { required: true, message: '权限字符不能为空', trigger: 'blur' }\r\n        ],\r\n        roleSort: [\r\n          { required: true, message: '角色顺序不能为空', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getMenuTreeselect()\r\n    this.getDicts('sys_normal_disable').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询角色列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(\r\n        response => {\r\n          this.roleList = response.data.list\r\n          this.total = response.data.count\r\n          this.loading = false\r\n        }\r\n      )\r\n    },\r\n    /** 查询菜单树结构 */\r\n    getMenuTreeselect() {\r\n      roleMenuTreeselect(0).then(response => {\r\n        this.menuOptions = response.data.menus\r\n        this.menuList = this.menuOptions\r\n      })\r\n    },\r\n    /** 查询部门树结构 */\r\n    getDeptTreeselect() {\r\n      deptTreeselect().then(response => {\r\n        this.deptOptions = response.data.list\r\n      })\r\n    },\r\n    // 所有菜单节点数据\r\n    getMenuAllCheckedKeys() {\r\n      // 目前被选中的菜单节点\r\n      const checkedKeys = this.$refs.menuTree.getHalfCheckedKeys()\r\n      console.log('目前被选中的菜单节点', checkedKeys)\r\n      // 半选中的菜单节点\r\n      const halfCheckedKeys = this.$refs.menuTree.getCheckedKeys()\r\n      console.log('半选中的菜单节点', halfCheckedKeys)\r\n      // checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)\r\n      return halfCheckedKeys\r\n    },\r\n    // 所有部门节点数据\r\n    getDeptAllCheckedKeys() {\r\n      // 目前被选中的部门节点\r\n      const checkedKeys = this.$refs.dept.getCheckedKeys()\r\n      // 半选中的部门节点\r\n      // const halfCheckedKeys = this.$refs.dept.getCheckedKeys()\r\n      // checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)\r\n      return checkedKeys\r\n    },\r\n    /** 根据角色ID查询菜单树结构 */\r\n    getRoleMenuTreeselect(row, checkedKeys) {\r\n      if (row.roleKey === 'admin') {\r\n        this.menuOptionsAlert = '系统超级管理员无需此操作'\r\n        this.menuOptions = []\r\n      } else {\r\n        this.$nextTick(() => {\r\n          this.$refs.menuTree.setCheckedKeys(checkedKeys)\r\n        })\r\n      }\r\n    },\r\n    /** 根据角色ID查询部门树结构 */\r\n    getRoleDeptTreeselect(roleId) {\r\n      roleDeptTreeselect(roleId).then(response => {\r\n        this.deptOptions = response.data.depts\r\n        this.$nextTick(() => {\r\n          this.$refs.dept.setCheckedKeys(response.data.checkedKeys)\r\n        })\r\n      })\r\n    },\r\n    // 角色状态修改\r\n    handleStatusChange(row) {\r\n      const text = row.status === '2' ? '启用' : '停用'\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return changeRoleStatus(row.roleId, row.status)\r\n      }).then((res) => {\r\n        console.log('res', res)\r\n        this.msgSuccess(res.msg)\r\n      }).catch(function() {\r\n        row.status = row.status === '2' ? '1' : '2'\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 取消按钮（数据权限）\r\n    cancelDataScope() {\r\n      this.openDataScope = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.menuOptions = this.menuList\r\n      if (this.$refs.menuTree !== undefined) {\r\n        this.$refs.menuTree.setCheckedKeys([])\r\n      }\r\n      this.form = {\r\n        roleId: undefined,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        roleSort: 0,\r\n        status: '2',\r\n        menuIds: [],\r\n        deptIds: [],\r\n        sysMenu: [],\r\n        remark: undefined\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.roleId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      // this.getMenuTreeselect(0)\r\n      this.open = true\r\n      this.title = '添加角色'\r\n      this.isEdit = false\r\n    },\r\n    handleSortChang(column, prop, order) {\r\n      prop = column.prop\r\n      order = column.order\r\n      if (order === 'descending') {\r\n        this.queryParams[prop + 'Order'] = 'desc'\r\n      } else if (order === 'ascending') {\r\n        this.queryParams[prop + 'Order'] = 'asc'\r\n      } else {\r\n        this.queryParams[prop + 'Order'] = undefined\r\n      }\r\n      this.getList()\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.menuIdsChecked = []\r\n      this.reset()\r\n      const roleId = row.roleId || this.ids\r\n      getRole(roleId).then(response => {\r\n        this.form = response.data\r\n        this.menuIdsChecked = response.data.menuIds\r\n        this.title = '修改角色'\r\n        this.isEdit = true\r\n        this.open = true\r\n        this.getRoleMenuTreeselect(row, response.data.menuIds)\r\n      })\r\n    },\r\n    /** 分配数据权限操作 */\r\n    handleDataScope(row) {\r\n      this.reset()\r\n      getRole(row.roleId).then(response => {\r\n        this.form = response.data\r\n        this.openDataScope = true\r\n        this.title = '分配数据权限'\r\n        this.getRoleDeptTreeselect(row.roleId)\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.roleId !== undefined) {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys()\r\n            updateRole(this.form, this.form.roleId).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys()\r\n            addRole(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 提交按钮（数据权限） */\r\n    submitDataScope: function() {\r\n      if (this.form.roleId !== undefined) {\r\n        this.form.deptIds = this.getDeptAllCheckedKeys()\r\n        console.log(this.getDeptAllCheckedKeys())\r\n        dataScope(this.form).then(response => {\r\n          if (response.code === 200) {\r\n            this.msgSuccess(response.msg)\r\n            this.openDataScope = false\r\n            this.getList()\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const roleIds = (row.roleId && [row.roleId]) || this.ids\r\n      this.$confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delRole({ 'ids': roleIds })\r\n      }).then((response) => {\r\n        this.getList()\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$confirm('是否确认导出所有角色数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.downloadLoading = true\r\n        import('@/vendor/Export2Excel').then(excel => {\r\n          const tHeader = ['角色编号', '角色名称', '权限字符', '显示顺序', '状态', '创建时间']\r\n          const filterVal = ['roleId', 'roleName', 'roleKey', 'roleSort', 'status', 'createdAt']\r\n          const list = this.roleList\r\n          const data = formatJson(filterVal, list)\r\n          excel.export_json_to_excel({\r\n            header: tHeader,\r\n            data,\r\n            filename: '角色管理',\r\n            autoWidth: true, // Optional\r\n            bookType: 'xlsx' // Optional\r\n          })\r\n          this.downloadLoading = false\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AA2PA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,gBAAe,QAAS,sBAAqB;AAClH,SAASC,kBAAiB,QAAS,sBAAqB;AACxD,SAASC,UAAS,IAAKC,cAAc,EAAEC,kBAAiB,QAAS,sBAAqB;AACtF,SAASC,UAAS,QAAS,SAAQ;AAEnC,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,CAEZ,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,GAAG,EAAE,EAAE;MACP;MACAC,MAAM,EAAE,IAAI;MACZ;MACAC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,CAAC;MACR;MACAC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClB;MACAC,KAAK,EAAE,EAAE;MACT;MACAC,IAAI,EAAE,KAAK;MACX;MACAC,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE,KAAK;MACb;MACAC,SAAS,EAAE,EAAE;MACb;MACAC,aAAa,EAAE,EAAE;MACjB;MACAC,gBAAgB,EAAE,CAChB;QACEC,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,GAAG;QACVC,KAAK,EAAE;MACT,EACD;MACD;MACAC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZ;MACAC,WAAW,EAAE,EAAE;MACfC,gBAAgB,EAAE,SAAS;MAC3B;MACAC,WAAW,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAEC,SAAS;QACnBC,OAAO,EAAED,SAAS;QAClBE,MAAM,EAAEF;MACV,CAAC;MACD;MACAG,IAAI,EAAE;QACJC,OAAO,EAAE;MACX,CAAC;MACDC,YAAY,EAAE;QACZC,QAAQ,EAAE,UAAU;QACpBf,KAAK,EAAE;MACT,CAAC;MACD;MACAgB,KAAK,EAAE;QACLR,QAAQ,EAAE,CACR;UAAES,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDT,OAAO,EAAE,CACP;UAAEO,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDC,QAAQ,EAAE,CACR;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO;MAE3D;IACF;EACF,CAAC;EACDE,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,OAAO,CAAC;IACb,IAAI,CAACC,iBAAiB,CAAC;IACvB,IAAI,CAACC,QAAQ,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAC,UAAAC,QAAO,EAAK;MACnDL,KAAI,CAACzB,aAAY,GAAI8B,QAAQ,CAAC3C,IAAG;IACnC,CAAC;EACH,CAAC;EACD4C,OAAO,EAAE;IACP,aACAL,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAM,MAAA;MACR,IAAI,CAAC5C,OAAM,GAAI,IAAG;MAClBf,QAAQ,CAAC,IAAI,CAAC4D,YAAY,CAAC,IAAI,CAACzB,WAAW,EAAE,IAAI,CAACT,SAAS,CAAC,CAAC,CAAC8B,IAAI,CAChE,UAAAC,QAAO,EAAK;QACVE,MAAI,CAACvC,QAAO,GAAIqC,QAAQ,CAAC3C,IAAI,CAAC+C,IAAG;QACjCF,MAAI,CAACxC,KAAI,GAAIsC,QAAQ,CAAC3C,IAAI,CAACgD,KAAI;QAC/BH,MAAI,CAAC5C,OAAM,GAAI,KAAI;MACrB,CACF;IACF,CAAC;IACD,cACAuC,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAAA,IAAAS,MAAA;MAClBxD,kBAAkB,CAAC,CAAC,CAAC,CAACiD,IAAI,CAAC,UAAAC,QAAO,EAAK;QACrCM,MAAI,CAAChC,WAAU,GAAI0B,QAAQ,CAAC3C,IAAI,CAACkD,KAAI;QACrCD,MAAI,CAAC/B,QAAO,GAAI+B,MAAI,CAAChC,WAAU;MACjC,CAAC;IACH,CAAC;IACD,cACAkC,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAClBzD,cAAc,CAAC,CAAC,CAAC+C,IAAI,CAAC,UAAAC,QAAO,EAAK;QAChCS,MAAI,CAACjC,WAAU,GAAIwB,QAAQ,CAAC3C,IAAI,CAAC+C,IAAG;MACtC,CAAC;IACH,CAAC;IACD;IACAM,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB;MACA,IAAMC,WAAU,GAAI,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,kBAAkB,CAAC;MAC3DC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEL,WAAW;MACrC;MACA,IAAMM,eAAc,GAAI,IAAI,CAACL,KAAK,CAACC,QAAQ,CAACK,cAAc,CAAC;MAC3DH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,eAAe;MACvC;MACA,OAAOA,eAAc;IACvB,CAAC;IACD;IACAE,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB;MACA,IAAMR,WAAU,GAAI,IAAI,CAACC,KAAK,CAACQ,IAAI,CAACF,cAAc,CAAC;MACnD;MACA;MACA;MACA,OAAOP,WAAU;IACnB,CAAC;IACD,oBACAU,qBAAqB,WAArBA,qBAAqBA,CAACC,GAAG,EAAEX,WAAW,EAAE;MAAA,IAAAY,MAAA;MACtC,IAAID,GAAG,CAACvC,OAAM,KAAM,OAAO,EAAE;QAC3B,IAAI,CAACN,gBAAe,GAAI,cAAa;QACrC,IAAI,CAACH,WAAU,GAAI,EAAC;MACtB,OAAO;QACL,IAAI,CAACkD,SAAS,CAAC,YAAM;UACnBD,MAAI,CAACX,KAAK,CAACC,QAAQ,CAACY,cAAc,CAACd,WAAW;QAChD,CAAC;MACH;IACF,CAAC;IACD,oBACAe,qBAAqB,WAArBA,qBAAqBA,CAACC,MAAM,EAAE;MAAA,IAAAC,MAAA;MAC5B3E,kBAAkB,CAAC0E,MAAM,CAAC,CAAC5B,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC1C4B,MAAI,CAACpD,WAAU,GAAIwB,QAAQ,CAAC3C,IAAI,CAACwE,KAAI;QACrCD,MAAI,CAACJ,SAAS,CAAC,YAAM;UACnBI,MAAI,CAAChB,KAAK,CAACQ,IAAI,CAACK,cAAc,CAACzB,QAAQ,CAAC3C,IAAI,CAACsD,WAAW;QAC1D,CAAC;MACH,CAAC;IACH,CAAC;IACD;IACAmB,kBAAkB,WAAlBA,kBAAkBA,CAACR,GAAG,EAAE;MAAA,IAAAS,MAAA;MACtB,IAAMC,IAAG,GAAIV,GAAG,CAACtC,MAAK,KAAM,GAAE,GAAI,IAAG,GAAI,IAAG;MAC5C,IAAI,CAACiD,QAAQ,CAAC,MAAK,GAAID,IAAG,GAAI,IAAG,GAAIV,GAAG,CAACzC,QAAO,GAAI,OAAO,EAAE,IAAI,EAAE;QACjEqD,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACrC,IAAI,CAAC,YAAW;QACjB,OAAOlD,gBAAgB,CAACyE,GAAG,CAACK,MAAM,EAAEL,GAAG,CAACtC,MAAM;MAChD,CAAC,CAAC,CAACe,IAAI,CAAC,UAACsC,GAAG,EAAK;QACftB,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEqB,GAAG;QACtBN,MAAI,CAACO,UAAU,CAACD,GAAG,CAACE,GAAG;MACzB,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW;QAClBlB,GAAG,CAACtC,MAAK,GAAIsC,GAAG,CAACtC,MAAK,KAAM,GAAE,GAAI,GAAE,GAAI,GAAE;MAC5C,CAAC;IACH,CAAC;IACD;IACAyD,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC3E,IAAG,GAAI,KAAI;MAChB,IAAI,CAAC4E,KAAK,CAAC;IACb,CAAC;IACD;IACAC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC5E,aAAY,GAAI,KAAI;MACzB,IAAI,CAAC2E,KAAK,CAAC;IACb,CAAC;IACD;IACAA,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACpE,WAAU,GAAI,IAAI,CAACC,QAAO;MAC/B,IAAI,IAAI,CAACqC,KAAK,CAACC,QAAO,KAAM/B,SAAS,EAAE;QACrC,IAAI,CAAC8B,KAAK,CAACC,QAAQ,CAACY,cAAc,CAAC,EAAE;MACvC;MACA,IAAI,CAACxC,IAAG,GAAI;QACV0C,MAAM,EAAE7C,SAAS;QACjBD,QAAQ,EAAEC,SAAS;QACnBC,OAAO,EAAED,SAAS;QAClBW,QAAQ,EAAE,CAAC;QACXT,MAAM,EAAE,GAAG;QACX4D,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE,EAAE;QACX3D,OAAO,EAAE,EAAE;QACX4D,MAAM,EAAEhE;MACV;MACA,IAAI,CAACiE,SAAS,CAAC,MAAM;IACvB,CAAC;IACD,aACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACtE,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAACiB,OAAO,CAAC;IACf,CAAC;IACD,aACAqD,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAAChF,SAAQ,GAAI,EAAC;MAClB,IAAI,CAAC8E,SAAS,CAAC,WAAW;MAC1B,IAAI,CAACC,WAAW,CAAC;IACnB,CAAC;IACD;IACAE,qBAAqB,WAArBA,qBAAqBA,CAACC,SAAS,EAAE;MAC/B,IAAI,CAAC5F,GAAE,GAAI4F,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAAC1B,MAAM;MAAA;MAC5C,IAAI,CAACnE,MAAK,GAAI2F,SAAS,CAACG,MAAK,KAAM;MACnC,IAAI,CAAC7F,QAAO,GAAI,CAAC0F,SAAS,CAACG,MAAK;IAClC,CAAC;IACD,aACAC,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACb,KAAK,CAAC;MACX;MACA,IAAI,CAAC5E,IAAG,GAAI,IAAG;MACf,IAAI,CAACD,KAAI,GAAI,MAAK;MAClB,IAAI,CAACG,MAAK,GAAI,KAAI;IACpB,CAAC;IACDwF,eAAe,WAAfA,eAAeA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACnCD,IAAG,GAAID,MAAM,CAACC,IAAG;MACjBC,KAAI,GAAIF,MAAM,CAACE,KAAI;MACnB,IAAIA,KAAI,KAAM,YAAY,EAAE;QAC1B,IAAI,CAACjF,WAAW,CAACgF,IAAG,GAAI,OAAO,IAAI,MAAK;MAC1C,OAAO,IAAIC,KAAI,KAAM,WAAW,EAAE;QAChC,IAAI,CAACjF,WAAW,CAACgF,IAAG,GAAI,OAAO,IAAI,KAAI;MACzC,OAAO;QACL,IAAI,CAAChF,WAAW,CAACgF,IAAG,GAAI,OAAO,IAAI5E,SAAQ;MAC7C;MACA,IAAI,CAACc,OAAO,CAAC;IACf,CAAC;IACD,aACAgE,YAAY,WAAZA,YAAYA,CAACtC,GAAG,EAAE;MAAA,IAAAuC,MAAA;MAChB,IAAI,CAACjG,cAAa,GAAI,EAAC;MACvB,IAAI,CAAC8E,KAAK,CAAC;MACX,IAAMf,MAAK,GAAIL,GAAG,CAACK,MAAK,IAAK,IAAI,CAACpE,GAAE;MACpCf,OAAO,CAACmF,MAAM,CAAC,CAAC5B,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC/B6D,MAAI,CAAC5E,IAAG,GAAIe,QAAQ,CAAC3C,IAAG;QACxBwG,MAAI,CAACjG,cAAa,GAAIoC,QAAQ,CAAC3C,IAAI,CAACuF,OAAM;QAC1CiB,MAAI,CAAChG,KAAI,GAAI,MAAK;QAClBgG,MAAI,CAAC7F,MAAK,GAAI,IAAG;QACjB6F,MAAI,CAAC/F,IAAG,GAAI,IAAG;QACf+F,MAAI,CAACxC,qBAAqB,CAACC,GAAG,EAAEtB,QAAQ,CAAC3C,IAAI,CAACuF,OAAO;MACvD,CAAC;IACH,CAAC;IACD,eACAkB,eAAe,WAAfA,eAAeA,CAACxC,GAAG,EAAE;MAAA,IAAAyC,MAAA;MACnB,IAAI,CAACrB,KAAK,CAAC;MACXlG,OAAO,CAAC8E,GAAG,CAACK,MAAM,CAAC,CAAC5B,IAAI,CAAC,UAAAC,QAAO,EAAK;QACnC+D,MAAI,CAAC9E,IAAG,GAAIe,QAAQ,CAAC3C,IAAG;QACxB0G,MAAI,CAAChG,aAAY,GAAI,IAAG;QACxBgG,MAAI,CAAClG,KAAI,GAAI,QAAO;QACpBkG,MAAI,CAACrC,qBAAqB,CAACJ,GAAG,CAACK,MAAM;MACvC,CAAC;IACH,CAAC;IACD;IACAqC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAa;MAAA,IAAAC,MAAA;MACrB,IAAI,CAACrD,KAAK,CAAC,MAAM,CAAC,CAACsD,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAIA,KAAK,EAAE;UACT,IAAIF,MAAI,CAAChF,IAAI,CAAC0C,MAAK,KAAM7C,SAAS,EAAE;YAClCmF,MAAI,CAAChF,IAAI,CAAC2D,OAAM,GAAIqB,MAAI,CAACvD,qBAAqB,CAAC;YAC/C/D,UAAU,CAACsH,MAAI,CAAChF,IAAI,EAAEgF,MAAI,CAAChF,IAAI,CAAC0C,MAAM,CAAC,CAAC5B,IAAI,CAAC,UAAAC,QAAO,EAAK;cACvD,IAAIA,QAAQ,CAACoE,IAAG,KAAM,GAAG,EAAE;gBACzBH,MAAI,CAAC3B,UAAU,CAACtC,QAAQ,CAACuC,GAAG;gBAC5B0B,MAAI,CAACnG,IAAG,GAAI,KAAI;gBAChBmG,MAAI,CAACrE,OAAO,CAAC;cACf,OAAO;gBACLqE,MAAI,CAACI,QAAQ,CAACrE,QAAQ,CAACuC,GAAG;cAC5B;YACF,CAAC;UACH,OAAO;YACL0B,MAAI,CAAChF,IAAI,CAAC2D,OAAM,GAAIqB,MAAI,CAACvD,qBAAqB,CAAC;YAC/ChE,OAAO,CAACuH,MAAI,CAAChF,IAAI,CAAC,CAACc,IAAI,CAAC,UAAAC,QAAO,EAAK;cAClC,IAAIA,QAAQ,CAACoE,IAAG,KAAM,GAAG,EAAE;gBACzBH,MAAI,CAAC3B,UAAU,CAACtC,QAAQ,CAACuC,GAAG;gBAC5B0B,MAAI,CAACnG,IAAG,GAAI,KAAI;gBAChBmG,MAAI,CAACrE,OAAO,CAAC;cACf,OAAO;gBACLqE,MAAI,CAACI,QAAQ,CAACrE,QAAQ,CAACuC,GAAG;cAC5B;YACF,CAAC;UACH;QACF;MACF,CAAC;IACH,CAAC;IACD;IACA+B,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAa;MAAA,IAAAC,MAAA;MAC1B,IAAI,IAAI,CAACtF,IAAI,CAAC0C,MAAK,KAAM7C,SAAS,EAAE;QAClC,IAAI,CAACG,IAAI,CAAC4D,OAAM,GAAI,IAAI,CAAC1B,qBAAqB,CAAC;QAC/CJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAACG,qBAAqB,CAAC,CAAC;QACxCvE,SAAS,CAAC,IAAI,CAACqC,IAAI,CAAC,CAACc,IAAI,CAAC,UAAAC,QAAO,EAAK;UACpC,IAAIA,QAAQ,CAACoE,IAAG,KAAM,GAAG,EAAE;YACzBG,MAAI,CAACjC,UAAU,CAACtC,QAAQ,CAACuC,GAAG;YAC5BgC,MAAI,CAACxG,aAAY,GAAI,KAAI;YACzBwG,MAAI,CAAC3E,OAAO,CAAC;UACf,OAAO;YACL2E,MAAI,CAACF,QAAQ,CAACrE,QAAQ,CAACuC,GAAG;UAC5B;QACF,CAAC;MACH;IACF,CAAC;IACD,aACAiC,YAAY,WAAZA,YAAYA,CAAClD,GAAG,EAAE;MAAA,IAAAmD,OAAA;MAChB,IAAMC,OAAM,GAAKpD,GAAG,CAACK,MAAK,IAAK,CAACL,GAAG,CAACK,MAAM,CAAC,IAAK,IAAI,CAACpE,GAAE;MACvD,IAAI,CAAC0E,QAAQ,CAAC,cAAa,GAAIyC,OAAM,GAAI,QAAQ,EAAE,IAAI,EAAE;QACvDxC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACrC,IAAI,CAAC,YAAW;QACjB,OAAOtD,OAAO,CAAC;UAAE,KAAK,EAAEiI;QAAQ,CAAC;MACnC,CAAC,CAAC,CAAC3E,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpByE,OAAI,CAAC7E,OAAO,CAAC;QACb6E,OAAI,CAACnC,UAAU,CAACtC,QAAQ,CAACuC,GAAG;MAC9B,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACD,aACAmC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,OAAA;MACb,IAAI,CAAC3C,QAAQ,CAAC,gBAAgB,EAAE,IAAI,EAAE;QACpCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACrC,IAAI,CAAC,YAAM;QACZ6E,OAAI,CAACC,eAAc,GAAI,IAAG;QAC1BC,OAAA,CAAAC,OAAA,GAAAhF,IAAA;UAAA,OAAAiF,uBAAA,CAAAC,OAAA,CAAO,uBAAuB;QAAA,GAAElF,IAAI,CAAC,UAAAmF,KAAI,EAAK;UAC5C,IAAMC,OAAM,GAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM;UAC7D,IAAMC,SAAQ,GAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW;UACrF,IAAMhF,IAAG,GAAIwE,OAAI,CAACjH,QAAO;UACzB,IAAMN,IAAG,GAAIH,UAAU,CAACkI,SAAS,EAAEhF,IAAI;UACvC8E,KAAK,CAACG,oBAAoB,CAAC;YACzBC,MAAM,EAAEH,OAAO;YACf9H,IAAI,EAAJA,IAAI;YACJkI,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,IAAI;YAAE;YACjBC,QAAQ,EAAE,MAAK,CAAE;UACnB,CAAC;UACDb,OAAI,CAACC,eAAc,GAAI,KAAI;QAC7B,CAAC;MACH,CAAC;IACH;EACF;AACF", "ignoreList": []}]}