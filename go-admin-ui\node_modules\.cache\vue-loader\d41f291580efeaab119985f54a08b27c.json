{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue?vue&type=style&index=1&id=87ad2a86&lang=scss", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue", "mtime": 1753924830300}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICNjb2RlbWlycm9yIHsNCiAgICAgIGhlaWdodDogYXV0bzsNCiAgICAgIG1hcmdpbjogMDsNCiAgICAgIG92ZXJmbG93OiBhdXRvOw0KICAgIH0NCiAgLkNvZGVNaXJyb3Igew0KICAgICAgYm9yZGVyOiAxcHggc29saWQgI2VlZTsNCiAgICAgIGhlaWdodDogNjAwcHg7DQogICAgfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue"], "names": [], "mappings": ";EA2ZE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/gen/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-position=\"left\">\r\n          <el-form-item label=\"表名称\" prop=\"tableName\">\r\n            <el-input\r\n              v-model=\"queryParams.tableName\"\r\n              placeholder=\"请输入表名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"菜单名称\" prop=\"tableComment\">\r\n            <el-input\r\n              v-model=\"queryParams.tableComment\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleGenTable\"\r\n            >生成</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"info\"\r\n              icon=\"el-icon-upload\"\r\n              size=\"mini\"\r\n              @click=\"openImportTable\"\r\n            >导入</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"success\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleEditTable\"\r\n            >修改</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" />\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"tableId\" width=\"50px\" />\r\n          <el-table-column\r\n            label=\"表名称\"\r\n            align=\"center\"\r\n            prop=\"tableName\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column\r\n            label=\"菜单名称\"\r\n            align=\"center\"\r\n            prop=\"tableComment\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column\r\n            label=\"模型名称\"\r\n            align=\"center\"\r\n            prop=\"className\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"165\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleEditTable(scope.row)\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handlePreview(scope.row)\"\r\n              >预览</el-button>\r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"handleToProject(scope.row)\"\r\n                >代码生成</el-button>\r\n\r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"handleToDB(scope.row)\"\r\n                >生成配置</el-button>\r\n\r\n     \r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                   @click=\"handleToApiFile(scope.row)\"\r\n                >生成迁移脚本</el-button>\r\n                \r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleSingleDelete(scope.row)\"\r\n                >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 预览界面 -->\r\n\r\n      <el-dialog class=\"preview\" :title=\"preview.title\" :visible.sync=\"preview.open\" :close-on-click-modal=\"false\" fullscreen>\r\n        <div class=\"el-dialog-container\">\r\n          <div class=\"tag-group\">\r\n            <!-- eslint-disable-next-line vue/valid-v-for -->\r\n            <el-tag v-for=\"(value, key) in preview.data\" @click=\"codeChange(key)\">\r\n              <template>\r\n                {{ key.substring(key.lastIndexOf('/')+1,key.indexOf('.go.template')) }}\r\n              </template>\r\n            </el-tag>\r\n          </div>\r\n          <div id=\"codemirror\">\r\n            <codemirror ref=\"cmEditor\" :value=\"codestr\" :options=\"cmOptions\" />\r\n          </div>\r\n          <!-- <el-tabs v-model=\"preview.activeName\" tab-position=\"left\">\r\n            <el-tab-pane\r\n              v-for=\"(value, key) in preview.data\"\r\n              :key=\"key\"\r\n              :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.template'))\"\r\n              :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.template'))\"\r\n            >\r\n\r\n              <pre class=\"pre\"/>\r\n\r\n            </el-tab-pane>\r\n            </el-tabs> -->\r\n        </div>\r\n\r\n      </el-dialog>\r\n      <import-table ref=\"importTB\" @ok=\"handleQuery\" />\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listTable, previewTable, delTable, toDBTable, toProjectTableCheckRole, apiToFile } from '@/api/tools/gen'\r\nimport importTable from './importTable'\r\nimport { downLoadFile } from '@/utils/zipdownload'\r\nimport { codemirror } from 'vue-codemirror'\r\nimport 'codemirror/theme/material-palenight.css'\r\n\r\nrequire('codemirror/mode/javascript/javascript')\r\nimport 'codemirror/mode/javascript/javascript'\r\nimport 'codemirror/mode/go/go'\r\nimport 'codemirror/mode/vue/vue'\r\n\r\nexport default {\r\n  name: 'Gen',\r\n  components: { importTable, codemirror },\r\n  data() {\r\n    return {\r\n      cmOptions: {\r\n        tabSize: 4,\r\n        theme: 'material-palenight',\r\n        mode: 'text/javascript',\r\n        lineNumbers: true,\r\n        line: true\r\n        // more CodeMirror options...\r\n      },\r\n      codestr: '',\r\n      // 遮罩层\r\n      loading: true,\r\n      // 唯一标识符\r\n      uniqueId: '',\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中表数组\r\n      tableNames: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      tableList: [],\r\n      // 日期范围\r\n      dateRange: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      },\r\n      // 预览参数\r\n      preview: {\r\n        open: false,\r\n        title: '代码预览',\r\n        data: {},\r\n        activeName: 'api.go'\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  activated() {\r\n    const time = this.$route.query.t\r\n    if (time !== null && time !== this.uniqueId) {\r\n      this.uniqueId = time\r\n      this.resetQuery()\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询表集合 */\r\n    getList() {\r\n      this.loading = true\r\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.tableList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    codeChange(e) {\r\n      if (e.indexOf('js') > -1) {\r\n        this.cmOptions.mode = 'text/javascript'\r\n      }\r\n      if (e.indexOf('model') > -1 || e.indexOf('router') > -1 || e.indexOf('api') > -1 || e.indexOf('service') > -1 || e.indexOf('dto') > -1) {\r\n        this.cmOptions.mode = 'text/x-go'\r\n      }\r\n      if (e.indexOf('vue') > -1) {\r\n        this.cmOptions.mode = 'text/x-vue'\r\n      }\r\n      this.codestr = this.preview.data[e]\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 生成代码操作 */\r\n    handleGenTable(row) {\r\n      const ids = row.tableId || this.ids\r\n      if (ids === '') {\r\n        this.msgError('请选择要生成的数据')\r\n        return\r\n      }\r\n      downLoadFile('/api/v1/gen/gencode/' + ids)\r\n    },\r\n    /** 打开导入表弹窗 */\r\n    openImportTable() {\r\n      this.$refs.importTB.show()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 预览按钮 */\r\n    handlePreview(row) {\r\n      previewTable(row.tableId).then(response => {\r\n        this.preview.data = response.data\r\n        this.preview.open = true\r\n        this.codeChange('template/api.go.template')\r\n      })\r\n    },\r\n    handleToProject(row) {\r\n      toProjectTableCheckRole(row.tableId, false).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    handleToApiFile(row) {\r\n      apiToFile(row.tableId, true).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    handleToDB(row) {\r\n      toDBTable(row.tableId).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.tableId)\r\n      this.tableNames = selection.map(item => item.tableName)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleEditTable(row) {\r\n      const tableId = row.tableId || this.ids[0]\r\n      this.$router.push({ path: '/dev-tools/editTable', query: { tableId: tableId }})\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const tableIds = row.tableId || this.ids\r\n      this.$confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delTable(tableIds)\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    handleSingleDelete(row) {\r\n      const tableIds = row.tableId || this.ids\r\n      delTable(tableIds).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n .el-dialog-container ::v-deep{\r\n   overflow: hidden;\r\n   .el-scrollbar__view{\r\n     height: 100%;\r\n   }\r\n   .pre{\r\n     height: 546px;\r\n      overflow: hidden;\r\n      .el-scrollbar{\r\n        height: 100%;\r\n      }\r\n   }\r\n   .el-scrollbar__wrap::-webkit-scrollbar{\r\n     display: none;\r\n   }\r\n }\r\n ::v-deep .el-dialog__body{\r\n    padding: 0 20px;\r\n    margin:0;\r\n  }\r\n  .tag-group {\r\n    margin: 0 0 10px -10px;\r\n  }\r\n  .tag-group .el-tag{\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .el-tag {\r\n    cursor: pointer;\r\n  }\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n  #codemirror {\r\n      height: auto;\r\n      margin: 0;\r\n      overflow: auto;\r\n    }\r\n  .CodeMirror {\r\n      border: 1px solid #eee;\r\n      height: 600px;\r\n    }\r\n</style>\r\n"]}]}