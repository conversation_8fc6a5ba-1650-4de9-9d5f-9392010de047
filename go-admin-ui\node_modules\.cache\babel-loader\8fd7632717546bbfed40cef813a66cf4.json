{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1753924830216}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["path", "data", "editableTabsValue", "top", "left", "selectedTag", "affixTags", "visible", "computed", "visitedViews", "$store", "state", "tagsView", "routes", "permission", "theme", "settings", "watch", "$route", "addTags", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "isActive", "beforeUnload", "methods", "_this", "window", "tabViews", "map", "item", "fullPath", "hash", "meta", "_objectSpread", "name", "params", "query", "title", "sessionStorage", "setItem", "JSON", "stringify", "oldViews", "parse", "getItem", "length", "handleTagsOver", "index", "tags", "querySelectorAll", "style", "cssText", "concat", "colorRgb", "handleTagsLeave", "_this2", "findIndex", "pathIndex", "isAffix", "tag", "affix", "filterAffixTags", "_this3", "basePath", "arguments", "undefined", "for<PERSON>ach", "route", "tagPath", "resolve", "push", "children", "tempTags", "_toConsumableArray", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "dispatch", "err", "e", "f", "moveToCurrentTag", "_this4", "$refs", "$nextTick", "_iterator2", "_step2", "to", "refreshSelectedTag", "view", "_this5", "then", "$router", "replace", "closeSelectedTag", "_this6", "routerPath", "_ref", "toLastView", "closeOthersTags", "_this7", "catch", "closeAllTags", "_this8", "_ref2", "some", "latestView", "slice", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY", "String", "prototype", "sColor", "toLowerCase", "reg", "test", "sColorNew", "i", "sColorChange", "parseInt", "join"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue"], "sourcesContent": ["<template>\r\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n    <el-tabs\r\n      v-model=\"editableTabsValue\"\r\n      type=\"card\"\r\n      @tab-remove=\"closeSelectedTag\"\r\n    >\r\n      <el-tab-pane\r\n        v-for=\"item in visitedViews\"\r\n        :key=\"item.path\"\r\n        :closable=\"item.fullPath === '/dashboard' ? false : true\"\r\n        :name=\"item.fullPath\"\r\n      >\r\n        <router-link\r\n          ref=\"tag\"\r\n          slot=\"label\"\r\n          tag=\"span\"\r\n          class=\"tags-view-item\"\r\n          :style=\"{ color: item.fullPath === $route.fullPath ? theme : '' }\"\r\n          :to=\"{ path: item.path, query: item.query, fullPath: item.fullPath }\"\r\n          @contextmenu.prevent.native=\"openMenu(item,$event)\"\r\n        >\r\n          {{ item.title }}\r\n        </router-link>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n      <li class=\"tags-item\" @click=\"refreshSelectedTag(selectedTag)\" @mouseover=\"handleTagsOver(1)\" @mouseleave=\"handleTagsLeave(1)\">刷新当前标签页</li>\r\n      <li v-if=\"!isAffix(selectedTag)\" class=\"tags-item\" @click=\"closeSelectedTag(selectedTag)\" @mouseover=\"handleTagsOver(2)\" @mouseleave=\"handleTagsLeave(2)\">关闭当前标签页</li>\r\n      <li class=\"tags-item\" @click=\"closeOthersTags\" @mouseover=\"handleTagsOver(3)\" @mouseleave=\"handleTagsLeave(3)\">关闭其他标签页</li>\r\n      <li class=\"tags-item\" @click=\"closeAllTags(selectedTag)\" @mouseover=\"handleTagsOver(4)\" @mouseleave=\"handleTagsLeave(4)\">关闭全部标签页</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      editableTabsValue: '/',\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: [],\r\n      visible: false\r\n    }\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags()\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.closeMenu)\r\n      } else {\r\n        document.body.removeEventListener('click', this.closeMenu)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTags()\r\n    this.addTags()\r\n    this.isActive()\r\n    this.beforeUnload()\r\n  },\r\n  methods: {\r\n    // 刷新前缓存tab\r\n    beforeUnload() {\r\n      // 监听页面刷新\r\n      window.addEventListener('beforeunload', () => {\r\n        const tabViews = this.visitedViews.map(item => {\r\n          return {\r\n            fullPath: item.fullPath,\r\n            hash: item.hash,\r\n            meta: { ...item.meta },\r\n            name: item.name,\r\n            params: { ...item.params },\r\n            path: item.path,\r\n            query: { ...item.query },\r\n            title: item.title\r\n          }\r\n        })\r\n        sessionStorage.setItem('tabViews', JSON.stringify(tabViews))\r\n      })\r\n      // 页面初始化加载判断缓存中是否有数据\r\n      const oldViews = JSON.parse(sessionStorage.getItem('tabViews')) || []\r\n      if (oldViews.length > 0) {\r\n        this.$store.state.tagsView.visitedViews = oldViews\r\n      }\r\n    },\r\n    handleTagsOver(index) {\r\n      const tags = document.querySelectorAll('.tags-item')\r\n      const item = tags[index - 1]\r\n      item.style.cssText = `color:${this.$store.state.settings.theme};background:${\r\n        this.$store.state.settings.theme.colorRgb()\r\n      }`\r\n    },\r\n    handleTagsLeave(index) {\r\n      const tags = document.querySelectorAll('.tags-item')\r\n      const item = tags[index - 1]\r\n      item.style.cssText = `color:#606266`\r\n    },\r\n    isActive() {\r\n      const index = this.visitedViews.findIndex(item => item.fullPath === this.$route.fullPath)\r\n      const pathIndex = index > -1 ? index : 0\r\n      this.editableTabsValue = this.visitedViews[pathIndex].fullPath\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix\r\n    },\r\n    filterAffixTags(routes, basePath = '/') {\r\n      let tags = []\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path)\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: { ...route.meta }\r\n          })\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path)\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags]\r\n          }\r\n        }\r\n      })\r\n      return tags\r\n    },\r\n    initTags() {\r\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch('tagsView/addVisitedView', tag)\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const { name } = this.$route\r\n      if (name) {\r\n        this.$store.dispatch('tagsView/addView', this.$route)\r\n        this.isActive()\r\n      }\r\n      return false\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path === this.$route.path) {\r\n            // this.$refs.scrollPane.moveToTarget(tag)\r\n            // when query is different then update\r\n            if (tag.to.fullPath !== this.$route.fullPath) {\r\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n            }\r\n            break\r\n          }\r\n        }\r\n      })\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n        const { fullPath } = view\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: '/redirect' + fullPath\r\n          })\r\n        })\r\n      })\r\n    },\r\n    closeSelectedTag(view) {\r\n      const routerPath = view.fullPath ? view.fullPath : view\r\n      const index = this.visitedViews.findIndex(item => item.fullPath === routerPath)\r\n      if (index > -1) {\r\n        const path = this.visitedViews[index]\r\n        this.$store.dispatch('tagsView/delView', path).then(({ visitedViews }) => {\r\n          if (this.editableTabsValue === path.fullPath) {\r\n            this.toLastView(visitedViews, path)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag.path).catch(e => e)\r\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\r\n        this.moveToCurrentTag()\r\n      })\r\n    },\r\n    closeAllTags(view) {\r\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\r\n        if (this.affixTags.some(tag => tag.path === view.path)) {\r\n          return\r\n        }\r\n        this.toLastView(visitedViews, view)\r\n      })\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0]\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath).catch(err => err)\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name === 'Dashboard') {\r\n          // to reload home page\r\n          this.$router.replace({ path: '/redirect' + view.fullPath })\r\n        } else {\r\n          this.$router.push('/')\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105\r\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n      const offsetWidth = this.$el.offsetWidth // container width\r\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\r\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft\r\n      } else {\r\n        this.left = left\r\n      }\r\n\r\n      this.top = e.clientY\r\n      this.visible = true\r\n      this.selectedTag = tag\r\n    },\r\n    closeMenu() {\r\n      this.visible = false\r\n    }\r\n  }\r\n}\r\n\r\n// eslint-disable-next-line no-extend-native\r\nString.prototype.colorRgb = function() {\r\n  let sColor = this.toLowerCase()\r\n  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n  if (sColor && reg.test(sColor)) {\r\n    if (sColor.length === 4) {\r\n      let sColorNew = '#'\r\n      for (let i = 1; i < 4; i += 1) {\r\n        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n      }\r\n      sColor = sColorNew\r\n    }\r\n    const sColorChange = []\r\n    for (let i = 1; i < 7; i += 2) {\r\n      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))\r\n    }\r\n    return 'rgba(' + sColorChange.join(',') + ',0.2)'\r\n  } else {\r\n    return sColor\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tags-view-container ::v-deep{\r\n  height: 43px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-bottom: 1px solid #d8dce5;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n  padding: 0 15px;\r\n  box-sizing: border-box;\r\n  .el-tabs__item{\r\n    &:hover{\r\n      color: #000;\r\n    }\r\n  }\r\n  .tags-view-item{\r\n    height: 40px;\r\n    display: inline-block;\r\n  }\r\n  .tags-view-wrapper {\r\n    .tags-view-item {\r\n      display: inline-block;\r\n      position: relative;\r\n      cursor: pointer;\r\n      height: 26px;\r\n      line-height: 26px;\r\n      border: 1px solid #d8dce5;\r\n      color: #495060;\r\n      background: #fff;\r\n      padding: 0 8px;\r\n      font-size: 12px;\r\n      margin-left: 5px;\r\n      margin-top: 4px;\r\n      &:first-of-type {\r\n        margin-left: 15px;\r\n      }\r\n      &:last-of-type {\r\n        margin-right: 15px;\r\n      }\r\n      &.active {\r\n        background-color: #42b983;\r\n        color: #fff;\r\n        border-color: #42b983;\r\n        &::before {\r\n          content: '';\r\n          background: #fff;\r\n          display: inline-block;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          position: relative;\r\n          margin-right: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .contextmenu {\r\n    margin: 0;\r\n    background: #fff;\r\n    z-index: 3000;\r\n    position: absolute;\r\n    list-style-type: none;\r\n    padding: 5px 0;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333;\r\n    box-shadow: 1px 2px 10px #ccc;\r\n    -moz-user-select:none;\r\n    -webkit-user-select:none;\r\n    user-select:none;\r\n    li {\r\n      list-style: none;\r\n      line-height: 36px;\r\n      padding: 2px 20px;\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      cursor: pointer;\r\n      outline: 0;\r\n      cursor: pointer;\r\n      &:hover {\r\n        background: #eee;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n//reset element css of el-icon-close\r\n.tags-view-wrapper {\r\n  .tags-view-item {\r\n    .el-icon-close {\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: 2px;\r\n      border-radius: 50%;\r\n      text-align: center;\r\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\r\n      transform-origin: 100% 50%;\r\n      &:before {\r\n        transform: scale(.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n      }\r\n      &:hover {\r\n        background-color: #b4bccc;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAoCA,OAAOA,IAAG,MAAO,MAAK;AAEtB,eAAe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,iBAAiB,EAAE,GAAG;MACtBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,CAAC,CAAC;MACfC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,QAAQ,EAAE;IACRC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACH,YAAW;IAC/C,CAAC;IACDI,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAACH,MAAM,CAACC,KAAK,CAACG,UAAU,CAACD,MAAK;IAC3C,CAAC;IACDE,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACL,MAAM,CAACC,KAAK,CAACK,QAAQ,CAACD,KAAI;IACxC;EACF,CAAC;EACDE,KAAK,EAAE;IACLC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,OAAO,CAAC;IACf,CAAC;IACDZ,OAAO,WAAPA,OAAOA,CAACa,KAAK,EAAE;MACb,IAAIA,KAAK,EAAE;QACTC,QAAQ,CAACC,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,SAAS;MACxD,OAAO;QACLH,QAAQ,CAACC,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACD,SAAS;MAC3D;IACF;EACF,CAAC;EACDE,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC;IACd,IAAI,CAACR,OAAO,CAAC;IACb,IAAI,CAACS,QAAQ,CAAC;IACd,IAAI,CAACC,YAAY,CAAC;EACpB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAE,KAAA;MACb;MACAC,MAAM,CAACT,gBAAgB,CAAC,cAAc,EAAE,YAAM;QAC5C,IAAMU,QAAO,GAAIF,KAAI,CAACtB,YAAY,CAACyB,GAAG,CAAC,UAAAC,IAAG,EAAK;UAC7C,OAAO;YACLC,QAAQ,EAAED,IAAI,CAACC,QAAQ;YACvBC,IAAI,EAAEF,IAAI,CAACE,IAAI;YACfC,IAAI,EAAAC,aAAA,KAAOJ,IAAI,CAACG,IAAG,CAAG;YACtBE,IAAI,EAAEL,IAAI,CAACK,IAAI;YACfC,MAAM,EAAAF,aAAA,KAAOJ,IAAI,CAACM,MAAK,CAAG;YAC1BzC,IAAI,EAAEmC,IAAI,CAACnC,IAAI;YACf0C,KAAK,EAAAH,aAAA,KAAOJ,IAAI,CAACO,KAAI,CAAG;YACxBC,KAAK,EAAER,IAAI,CAACQ;UACd;QACF,CAAC;QACDC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACd,QAAQ,CAAC;MAC7D,CAAC;MACD;MACA,IAAMe,QAAO,GAAIF,IAAI,CAACG,KAAK,CAACL,cAAc,CAACM,OAAO,CAAC,UAAU,CAAC,KAAK,EAAC;MACpE,IAAIF,QAAQ,CAACG,MAAK,GAAI,CAAC,EAAE;QACvB,IAAI,CAACzC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACH,YAAW,GAAIuC,QAAO;MACnD;IACF,CAAC;IACDI,cAAc,WAAdA,cAAcA,CAACC,KAAK,EAAE;MACpB,IAAMC,IAAG,GAAIjC,QAAQ,CAACkC,gBAAgB,CAAC,YAAY;MACnD,IAAMpB,IAAG,GAAImB,IAAI,CAACD,KAAI,GAAI,CAAC;MAC3BlB,IAAI,CAACqB,KAAK,CAACC,OAAM,YAAAC,MAAA,CAAa,IAAI,CAAChD,MAAM,CAACC,KAAK,CAACK,QAAQ,CAACD,KAAK,kBAAA2C,MAAA,CAC5D,IAAI,CAAChD,MAAM,CAACC,KAAK,CAACK,QAAQ,CAACD,KAAK,CAAC4C,QAAQ,CAAC,EAC3C;IACH,CAAC;IACDC,eAAe,WAAfA,eAAeA,CAACP,KAAK,EAAE;MACrB,IAAMC,IAAG,GAAIjC,QAAQ,CAACkC,gBAAgB,CAAC,YAAY;MACnD,IAAMpB,IAAG,GAAImB,IAAI,CAACD,KAAI,GAAI,CAAC;MAC3BlB,IAAI,CAACqB,KAAK,CAACC,OAAM,kBAAkB;IACrC,CAAC;IACD7B,QAAQ,WAARA,QAAQA,CAAA,EAAG;MAAA,IAAAiC,MAAA;MACT,IAAMR,KAAI,GAAI,IAAI,CAAC5C,YAAY,CAACqD,SAAS,CAAC,UAAA3B,IAAG;QAAA,OAAKA,IAAI,CAACC,QAAO,KAAMyB,MAAI,CAAC3C,MAAM,CAACkB,QAAQ;MAAA;MACxF,IAAM2B,SAAQ,GAAIV,KAAI,GAAI,CAAC,IAAIA,KAAI,GAAI;MACvC,IAAI,CAACnD,iBAAgB,GAAI,IAAI,CAACO,YAAY,CAACsD,SAAS,CAAC,CAAC3B,QAAO;IAC/D,CAAC;IACD4B,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAE;MACX,OAAOA,GAAG,CAAC3B,IAAG,IAAK2B,GAAG,CAAC3B,IAAI,CAAC4B,KAAI;IAClC,CAAC;IACDC,eAAe,WAAfA,eAAeA,CAACtD,MAAM,EAAkB;MAAA,IAAAuD,MAAA;MAAA,IAAhBC,QAAO,GAAAC,SAAA,CAAAnB,MAAA,QAAAmB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAI,GAAG;MACpC,IAAIhB,IAAG,GAAI,EAAC;MACZzC,MAAM,CAAC2D,OAAO,CAAC,UAAAC,KAAI,EAAK;QACtB,IAAIA,KAAK,CAACnC,IAAG,IAAKmC,KAAK,CAACnC,IAAI,CAAC4B,KAAK,EAAE;UAClC,IAAMQ,OAAM,GAAI1E,IAAI,CAAC2E,OAAO,CAACN,QAAQ,EAAEI,KAAK,CAACzE,IAAI;UACjDsD,IAAI,CAACsB,IAAI,CAAC;YACRxC,QAAQ,EAAEsC,OAAO;YACjB1E,IAAI,EAAE0E,OAAO;YACblC,IAAI,EAAEiC,KAAK,CAACjC,IAAI;YAChBF,IAAI,EAAAC,aAAA,KAAOkC,KAAK,CAACnC,IAAG;UACtB,CAAC;QACH;QACA,IAAImC,KAAK,CAACI,QAAQ,EAAE;UAClB,IAAMC,QAAO,GAAIV,MAAI,CAACD,eAAe,CAACM,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACzE,IAAI;UAChE,IAAI8E,QAAQ,CAAC3B,MAAK,IAAK,CAAC,EAAE;YACxBG,IAAG,MAAAI,MAAA,CAAAqB,kBAAA,CAAQzB,IAAI,GAAAyB,kBAAA,CAAKD,QAAQ;UAC9B;QACF;MACF,CAAC;MACD,OAAOxB,IAAG;IACZ,CAAC;IACD3B,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,IAAMrB,SAAQ,GAAI,IAAI,CAACA,SAAQ,GAAI,IAAI,CAAC6D,eAAe,CAAC,IAAI,CAACtD,MAAM;MAAA,IAAAmE,SAAA,GAAAC,0BAAA,CACjD3E,SAAS;QAAA4E,KAAA;MAAA;QAA3B,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAA6B;UAAA,IAAlBpB,GAAE,GAAAiB,KAAA,CAAA9D,KAAA;UACX;UACA,IAAI6C,GAAG,CAACzB,IAAI,EAAE;YACZ,IAAI,CAAC9B,MAAM,CAAC4E,QAAQ,CAAC,yBAAyB,EAAErB,GAAG;UACrD;QACF;MAAA,SAAAsB,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;IACF,CAAC;IACDtE,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,IAAQqB,IAAG,GAAM,IAAI,CAACtB,MAAK,CAAnBsB,IAAG;MACX,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC9B,MAAM,CAAC4E,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAACpE,MAAM;QACpD,IAAI,CAACU,QAAQ,CAAC;MAChB;MACA,OAAO,KAAI;IACb,CAAC;IACD8D,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACjB,IAAMrC,IAAG,GAAI,IAAI,CAACsC,KAAK,CAAC3B,GAAE;MAC1B,IAAI,CAAC4B,SAAS,CAAC,YAAM;QAAA,IAAAC,UAAA,GAAAb,0BAAA,CACD3B,IAAI;UAAAyC,MAAA;QAAA;UAAtB,KAAAD,UAAA,CAAAX,CAAA,MAAAY,MAAA,GAAAD,UAAA,CAAAV,CAAA,IAAAC,IAAA,GAAwB;YAAA,IAAbpB,GAAE,GAAA8B,MAAA,CAAA3E,KAAA;YACX,IAAI6C,GAAG,CAAC+B,EAAE,CAAChG,IAAG,KAAM2F,MAAI,CAACzE,MAAM,CAAClB,IAAI,EAAE;cACpC;cACA;cACA,IAAIiE,GAAG,CAAC+B,EAAE,CAAC5D,QAAO,KAAMuD,MAAI,CAACzE,MAAM,CAACkB,QAAQ,EAAE;gBAC5CuD,MAAI,CAACjF,MAAM,CAAC4E,QAAQ,CAAC,4BAA4B,EAAEK,MAAI,CAACzE,MAAM;cAChE;cACA;YACF;UACF;QAAA,SAAAqE,GAAA;UAAAO,UAAA,CAAAN,CAAA,CAAAD,GAAA;QAAA;UAAAO,UAAA,CAAAL,CAAA;QAAA;MACF,CAAC;IACH,CAAC;IACDQ,kBAAkB,WAAlBA,kBAAkBA,CAACC,IAAI,EAAE;MAAA,IAAAC,MAAA;MACvB,IAAI,CAACzF,MAAM,CAAC4E,QAAQ,CAAC,wBAAwB,EAAEY,IAAI,CAAC,CAACE,IAAI,CAAC,YAAM;QAC9D,IAAQhE,QAAO,GAAM8D,IAAG,CAAhB9D,QAAO;QACf+D,MAAI,CAACN,SAAS,CAAC,YAAM;UACnBM,MAAI,CAACE,OAAO,CAACC,OAAO,CAAC;YACnBtG,IAAI,EAAE,WAAU,GAAIoC;UACtB,CAAC;QACH,CAAC;MACH,CAAC;IACH,CAAC;IACDmE,gBAAgB,WAAhBA,gBAAgBA,CAACL,IAAI,EAAE;MAAA,IAAAM,MAAA;MACrB,IAAMC,UAAS,GAAIP,IAAI,CAAC9D,QAAO,GAAI8D,IAAI,CAAC9D,QAAO,GAAI8D,IAAG;MACtD,IAAM7C,KAAI,GAAI,IAAI,CAAC5C,YAAY,CAACqD,SAAS,CAAC,UAAA3B,IAAG;QAAA,OAAKA,IAAI,CAACC,QAAO,KAAMqE,UAAU;MAAA;MAC9E,IAAIpD,KAAI,GAAI,CAAC,CAAC,EAAE;QACd,IAAMrD,KAAG,GAAI,IAAI,CAACS,YAAY,CAAC4C,KAAK;QACpC,IAAI,CAAC3C,MAAM,CAAC4E,QAAQ,CAAC,kBAAkB,EAAEtF,KAAI,CAAC,CAACoG,IAAI,CAAC,UAAAM,IAAA,EAAsB;UAAA,IAAnBjG,YAAW,GAAAiG,IAAA,CAAXjG,YAAW;UAChE,IAAI+F,MAAI,CAACtG,iBAAgB,KAAMF,KAAI,CAACoC,QAAQ,EAAE;YAC5CoE,MAAI,CAACG,UAAU,CAAClG,YAAY,EAAET,KAAI;UACpC;QACF,CAAC;MACH;IACF,CAAC;IACD4G,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAChB,IAAI,CAACR,OAAO,CAACzB,IAAI,CAAC,IAAI,CAACvE,WAAW,CAACL,IAAI,CAAC,CAAC8G,KAAK,CAAC,UAAAtB,CAAA;QAAA,OAAKA,CAAC;MAAA;MACrD,IAAI,CAAC9E,MAAM,CAAC4E,QAAQ,CAAC,yBAAyB,EAAE,IAAI,CAACjF,WAAW,CAAC,CAAC+F,IAAI,CAAC,YAAM;QAC3ES,MAAI,CAACnB,gBAAgB,CAAC;MACxB,CAAC;IACH,CAAC;IACDqB,YAAY,WAAZA,YAAYA,CAACb,IAAI,EAAE;MAAA,IAAAc,MAAA;MACjB,IAAI,CAACtG,MAAM,CAAC4E,QAAQ,CAAC,sBAAsB,CAAC,CAACc,IAAI,CAAC,UAAAa,KAAA,EAAsB;QAAA,IAAnBxG,YAAW,GAAAwG,KAAA,CAAXxG,YAAW;QAC9D,IAAIuG,MAAI,CAAC1G,SAAS,CAAC4G,IAAI,CAAC,UAAAjD,GAAE;UAAA,OAAKA,GAAG,CAACjE,IAAG,KAAMkG,IAAI,CAAClG,IAAI;QAAA,EAAC,EAAE;UACtD;QACF;QACAgH,MAAI,CAACL,UAAU,CAAClG,YAAY,EAAEyF,IAAI;MACpC,CAAC;IACH,CAAC;IACDS,UAAU,WAAVA,UAAUA,CAAClG,YAAY,EAAEyF,IAAI,EAAE;MAC7B,IAAMiB,UAAS,GAAI1G,YAAY,CAAC2G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,IAAID,UAAU,EAAE;QACd,IAAI,CAACd,OAAO,CAACzB,IAAI,CAACuC,UAAU,CAAC/E,QAAQ,CAAC,CAAC0E,KAAK,CAAC,UAAAvB,GAAE;UAAA,OAAKA,GAAG;QAAA;MACzD,OAAO;QACL;QACA;QACA,IAAIW,IAAI,CAAC1D,IAAG,KAAM,WAAW,EAAE;UAC7B;UACA,IAAI,CAAC6D,OAAO,CAACC,OAAO,CAAC;YAAEtG,IAAI,EAAE,WAAU,GAAIkG,IAAI,CAAC9D;UAAS,CAAC;QAC5D,OAAO;UACL,IAAI,CAACiE,OAAO,CAACzB,IAAI,CAAC,GAAG;QACvB;MACF;IACF,CAAC;IACDyC,QAAQ,WAARA,QAAQA,CAACpD,GAAG,EAAEuB,CAAC,EAAE;MACf,IAAM8B,YAAW,GAAI,GAAE;MACvB,IAAMC,UAAS,GAAI,IAAI,CAACC,GAAG,CAACC,qBAAqB,CAAC,CAAC,CAACrH,IAAG,EAAE;MACzD,IAAMsH,WAAU,GAAI,IAAI,CAACF,GAAG,CAACE,WAAU,EAAE;MACzC,IAAMC,OAAM,GAAID,WAAU,GAAIJ,YAAW,EAAE;MAC3C,IAAMlH,IAAG,GAAIoF,CAAC,CAACoC,OAAM,GAAIL,UAAS,GAAI,EAAC,EAAE;;MAEzC,IAAInH,IAAG,GAAIuH,OAAO,EAAE;QAClB,IAAI,CAACvH,IAAG,GAAIuH,OAAM;MACpB,OAAO;QACL,IAAI,CAACvH,IAAG,GAAIA,IAAG;MACjB;MAEA,IAAI,CAACD,GAAE,GAAIqF,CAAC,CAACqC,OAAM;MACnB,IAAI,CAACtH,OAAM,GAAI,IAAG;MAClB,IAAI,CAACF,WAAU,GAAI4D,GAAE;IACvB,CAAC;IACDzC,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACjB,OAAM,GAAI,KAAI;IACrB;EACF;AACF;;AAEA;AACAuH,MAAM,CAACC,SAAS,CAACpE,QAAO,GAAI,YAAW;EACrC,IAAIqE,MAAK,GAAI,IAAI,CAACC,WAAW,CAAC;EAC9B,IAAMC,GAAE,GAAI,oCAAmC;EAC/C,IAAIF,MAAK,IAAKE,GAAG,CAACC,IAAI,CAACH,MAAM,CAAC,EAAE;IAC9B,IAAIA,MAAM,CAAC7E,MAAK,KAAM,CAAC,EAAE;MACvB,IAAIiF,SAAQ,GAAI,GAAE;MAClB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,CAAC,EAAEA,CAAA,IAAK,CAAC,EAAE;QAC7BD,SAAQ,IAAKJ,MAAM,CAACZ,KAAK,CAACiB,CAAC,EAAEA,CAAA,GAAI,CAAC,CAAC,CAAC3E,MAAM,CAACsE,MAAM,CAACZ,KAAK,CAACiB,CAAC,EAAEA,CAAA,GAAI,CAAC,CAAC;MACnE;MACAL,MAAK,GAAII,SAAQ;IACnB;IACA,IAAME,YAAW,GAAI,EAAC;IACtB,KAAK,IAAID,EAAA,GAAI,CAAC,EAAEA,EAAA,GAAI,CAAC,EAAEA,EAAA,IAAK,CAAC,EAAE;MAC7BC,YAAY,CAAC1D,IAAI,CAAC2D,QAAQ,CAAC,IAAG,GAAIP,MAAM,CAACZ,KAAK,CAACiB,EAAC,EAAEA,EAAA,GAAI,CAAC,CAAC,CAAC;IAC3D;IACA,OAAO,OAAM,GAAIC,YAAY,CAACE,IAAI,CAAC,GAAG,IAAI,OAAM;EAClD,OAAO;IACL,OAAOR,MAAK;EACd;AACF", "ignoreList": []}]}