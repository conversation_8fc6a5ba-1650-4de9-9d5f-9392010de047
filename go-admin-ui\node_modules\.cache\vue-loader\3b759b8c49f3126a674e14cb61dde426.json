{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=template&id=33ec43fc", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1753924830213}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgOmNsYXNzPSJ7J2hhcy1sb2dvJzpzaG93TG9nb30iPg0KICAgIDxsb2dvIHYtaWY9InNob3dMb2dvIiA6Y29sbGFwc2U9ImlzQ29sbGFwc2UiIC8+DQogICAgPGVsLXNjcm9sbGJhciB3cmFwLWNsYXNzPSJzY3JvbGxiYXItd3JhcHBlciI+DQogICAgICA8ZWwtbWVudQ0KICAgICAgICA6ZGVmYXVsdC1hY3RpdmU9ImFjdGl2ZU1lbnUiDQogICAgICAgIDpjb2xsYXBzZT0iaXNDb2xsYXBzZSINCiAgICAgICAgOmJhY2tncm91bmQtY29sb3I9IiAkc3RvcmUuc3RhdGUuc2V0dGluZ3MudGhlbWVTdHlsZSA9PT0gJ2xpZ2h0JyA/IHZhcmlhYmxlcy5tZW51TGlnaHRCZyA6IHZhcmlhYmxlcy5tZW51QmciDQogICAgICAgIDp0ZXh0LWNvbG9yPSIkc3RvcmUuc3RhdGUuc2V0dGluZ3MudGhlbWVTdHlsZSA9PT0gJ2xpZ2h0JyA/ICdyZ2JhKDAsMCwwLC42NSknIDogJyNmZmYnIg0KICAgICAgICA6YWN0aXZlLXRleHQtY29sb3I9IiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50aGVtZSINCiAgICAgICAgOnVuaXF1ZS1vcGVuZWQ9InRydWUiDQogICAgICAgIDpjb2xsYXBzZS10cmFuc2l0aW9uPSJ0cnVlIg0KICAgICAgICBtb2RlPSJ2ZXJ0aWNhbCINCiAgICAgID4NCiAgICAgICAgPHNpZGViYXItaXRlbQ0KICAgICAgICAgIHYtZm9yPSIocm91dGUpIGluIHNpZGViYXJSb3V0ZXJzIg0KICAgICAgICAgIDprZXk9InJvdXRlLnBhdGgiDQogICAgICAgICAgOml0ZW09InJvdXRlIg0KICAgICAgICAgIDpiYXNlLXBhdGg9InJvdXRlLnBhdGgiDQogICAgICAgIC8+DQoNCiAgICAgIDwvZWwtbWVudT4NCiAgICA8L2VsLXNjcm9sbGJhcj4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;;MAEH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/Sidebar/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :class=\"{'has-logo':showLogo}\">\r\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\r\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\r\n      <el-menu\r\n        :default-active=\"activeMenu\"\r\n        :collapse=\"isCollapse\"\r\n        :background-color=\" $store.state.settings.themeStyle === 'light' ? variables.menuLightBg : variables.menuBg\"\r\n        :text-color=\"$store.state.settings.themeStyle === 'light' ? 'rgba(0,0,0,.65)' : '#fff'\"\r\n        :active-text-color=\"$store.state.settings.theme\"\r\n        :unique-opened=\"true\"\r\n        :collapse-transition=\"true\"\r\n        mode=\"vertical\"\r\n      >\r\n        <sidebar-item\r\n          v-for=\"(route) in sidebarRouters\"\r\n          :key=\"route.path\"\r\n          :item=\"route\"\r\n          :base-path=\"route.path\"\r\n        />\r\n\r\n      </el-menu>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Logo from './Logo'\r\nimport SidebarItem from './SidebarItem'\r\nimport variables from '@/styles/variables.scss'\r\n\r\nexport default {\r\n  components: { SidebarItem, Logo },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebarRouters',\r\n      'sidebar'\r\n    ]),\r\n    activeMenu() {\r\n      const route = this.$route\r\n      const { meta, path } = route\r\n      // if set path, the sidebar will highlight the path you set\r\n      if (meta.activeMenu) {\r\n        return meta.activeMenu\r\n      }\r\n      return path\r\n    },\r\n    showLogo() {\r\n      return this.$store.state.settings.sidebarLogo\r\n    },\r\n    variables() {\r\n      return variables\r\n    },\r\n    isCollapse() {\r\n      return !this.sidebar.opened\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n\r\n  }\r\n}\r\n</script>\r\n"]}]}