{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue", "mtime": 1753924830065}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdUcmVuZCcsCiAgcHJvcHM6IHsKICAgIHJhdGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBmbGFnOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycsCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "props", "rate", "type", "String", "default", "required", "flag"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chart-trend\">\r\n    <slot name=\"term\" />\r\n    <span>{{ rate }}%</span>\r\n    <span :class=\"[flag]\">\r\n      <i :class=\"'el-icon-caret-' + flag\" />\r\n    </span>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Trend',\r\n  props: {\r\n    rate: {\r\n      type: String,\r\n      default: '',\r\n      required: true\r\n    },\r\n    flag: {\r\n      type: String,\r\n      default: '',\r\n      required: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chart-trend {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 22px;\r\n  .trend-icon {\r\n    font-size: 12px;\r\n  }\r\n}\r\n.top,\r\n.bottom {\r\n  margin-left: 4px;\r\n  position: relative;\r\n  top: 1px;\r\n  width: 15px;\r\n  display: inline-block;\r\n  i {\r\n    font-size: 12px;\r\n    transform: scale(0.83);\r\n  }\r\n}\r\n\r\n.top {\r\n      i{\r\n          color: #f5222d!important;\r\n      }\r\n    }\r\n    .bottom {\r\n      top: -1px;\r\n      i{\r\n          color: #52c41a!important;\r\n      }\r\n    }\r\n</style>\r\n"], "mappings": "AAWA,eAAe;EACbA,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;IACZ;EACF;AACF", "ignoreList": []}]}