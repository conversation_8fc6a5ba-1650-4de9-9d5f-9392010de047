{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue?vue&type=style&index=0&id=37dfd6fc&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue", "mtime": 1753924830436}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\index.vue"], "names": [], "mappings": ";AAgVA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;AAExE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACjB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACvB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACjB;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;AACF;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1B;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACZ;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;AACF;AACA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACb;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/login/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"login-container\">\r\n    <div id=\"particles-js\">\r\n      <!-- <vue-particles\r\n        v-if=\"refreshParticles\"\r\n        color=\"#dedede\"\r\n        :particle-opacity=\"0.7\"\r\n        :particles-number=\"80\"\r\n        shape-type=\"circle\"\r\n        :particle-size=\"4\"\r\n        lines-color=\"#dedede\"\r\n        :lines-width=\"1\"\r\n        :line-linked=\"true\"\r\n        :line-opacity=\"0.4\"\r\n        :lines-distance=\"150\"\r\n        :move-speed=\"3\"\r\n        :hover-effect=\"true\"\r\n        hover-mode=\"grab\"\r\n        :click-effect=\"true\"\r\n        click-mode=\"push\"\r\n      /> -->\r\n    </div>\r\n\r\n    <div class=\"login-weaper animated bounceInDown\">\r\n      <div class=\"login-left\">\r\n        <div class=\"login-time\" v-text=\"currentTime\" />\r\n        <img :src=\"sysInfo.sys_app_logo\" alt=\"\" class=\"img\">\r\n        <p class=\"title\" v-text=\"sysInfo.sys_app_name\" />\r\n      </div>\r\n      <div class=\"login-border\">\r\n        <div class=\"login-main\">\r\n          <div class=\"login-title\">用户登录</div>\r\n          <el-form\r\n            ref=\"loginForm\"\r\n            :model=\"loginForm\"\r\n            :rules=\"loginRules\"\r\n            class=\"login-form\"\r\n            autocomplete=\"on\"\r\n            label-position=\"left\"\r\n          >\r\n            <el-form-item prop=\"username\">\r\n              <span class=\"svg-container\">\r\n                <i class=\"el-icon-user\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.username\"\r\n                placeholder=\"用户名\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"1\"\r\n                autocomplete=\"on\"\r\n              />\r\n            </el-form-item>\r\n\r\n            <el-tooltip\r\n              v-model=\"capsTooltip\"\r\n              content=\"Caps lock is On\"\r\n              placement=\"right\"\r\n              manual\r\n            >\r\n              <el-form-item prop=\"password\">\r\n                <span class=\"svg-container\">\r\n                  <svg-icon icon-class=\"password\" />\r\n                </span>\r\n                <el-input\r\n                  :key=\"passwordType\"\r\n                  ref=\"password\"\r\n                  v-model=\"loginForm.password\"\r\n                  :type=\"passwordType\"\r\n                  placeholder=\"密码\"\r\n                  name=\"password\"\r\n                  tabindex=\"2\"\r\n                  autocomplete=\"on\"\r\n                  @keyup.native=\"checkCapslock\"\r\n                  @blur=\"capsTooltip = false\"\r\n                  @keyup.enter.native=\"handleLogin\"\r\n                />\r\n                <span class=\"show-pwd\" @click=\"showPwd\">\r\n                  <svg-icon\r\n                    :icon-class=\"\r\n                      passwordType === 'password' ? 'eye' : 'eye-open'\r\n                    \"\r\n                  />\r\n                </span>\r\n              </el-form-item>\r\n            </el-tooltip>\r\n            <el-form-item prop=\"code\" style=\"width: 66%; float: left\">\r\n              <span class=\"svg-container\">\r\n                <svg-icon icon-class=\"validCode\" />\r\n              </span>\r\n              <el-input\r\n                ref=\"username\"\r\n                v-model=\"loginForm.code\"\r\n                placeholder=\"验证码\"\r\n                name=\"username\"\r\n                type=\"text\"\r\n                tabindex=\"3\"\r\n                maxlength=\"5\"\r\n                autocomplete=\"off\"\r\n                style=\"width: 75%\"\r\n                @keyup.enter.native=\"handleLogin\"\r\n              />\r\n            </el-form-item>\r\n            <div\r\n              class=\"login-code\"\r\n              style=\"\r\n                cursor: pointer;\r\n                width: 30%;\r\n                height: 48px;\r\n                float: right;\r\n                background-color: #f0f1f5;\r\n              \"\r\n            >\r\n              <img\r\n                style=\"\r\n                  height: 48px;\r\n                  width: 100%;\r\n                  border: 1px solid rgba(0, 0, 0, 0.1);\r\n                  border-radius: 5px;\r\n                \"\r\n                :src=\"codeUrl\"\r\n                @click=\"getCode\"\r\n              >\r\n            </div>\r\n\r\n            <el-button\r\n              :loading=\"loading\"\r\n              type=\"primary\"\r\n              style=\"width: 100%; padding: 12px 20px; margin-bottom: 30px\"\r\n              @click.native.prevent=\"handleLogin\"\r\n            >\r\n              <span v-if=\"!loading\">登 录</span>\r\n              <span v-else>登 录 中...</span>\r\n            </el-button>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-dialog title=\"Or connect with\" :visible.sync=\"showDialog\" :close-on-click-modal=\"false\">\r\n      Can not be simulated on local, so please combine you own business\r\n      simulation! ! !\r\n      <br>\r\n      <br>\r\n      <br>\r\n      <social-sign />\r\n    </el-dialog>\r\n    <div\r\n      id=\"bottom_layer\"\r\n      class=\"s-bottom-layer s-isindex-wrap\"\r\n      style=\"visibility: visible; width: 100%\"\r\n    >\r\n      <div class=\"s-bottom-layer-content\">\r\n\r\n        <div class=\"lh\">\r\n          <a class=\"text-color\" href=\"https://beian.miit.gov.cn\" target=\"_blank\">\r\n            沪ICP备XXXXXXXXX号-1\r\n          </a>\r\n        </div>\r\n        <div class=\"open-content-info\">\r\n          <div class=\"tip-hover-panel\" style=\"top: -18px; right: -12px\">\r\n            <div class=\"rest_info_tip\">\r\n              <div class=\"tip-wrapper\">\r\n                <div class=\"lh tip-item\" style=\"display: none\">\r\n                  <a\r\n                    class=\"text-color\"\r\n                    href=\"https://beian.miit.gov.cn\"\r\n                    target=\"_blank\"\r\n                  >\r\n                    沪ICP备XXXXXXXXX号-1\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg } from '@/api/login'\r\nimport moment from 'moment'\r\nimport SocialSign from './components/SocialSignin'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: { SocialSign },\r\n  data() {\r\n    return {\r\n      codeUrl: '',\r\n      cookiePassword: '',\r\n      refreshParticles: true,\r\n      loginForm: {\r\n        username: 'admin',\r\n        password: '123456',\r\n        rememberMe: false,\r\n        code: '',\r\n        uuid: ''\r\n      },\r\n      loginRules: {\r\n        username: [\r\n          { required: true, trigger: 'blur', message: '用户名不能为空' }\r\n        ],\r\n        password: [\r\n          { required: true, trigger: 'blur', message: '密码不能为空' }\r\n        ],\r\n        code: [\r\n          { required: true, trigger: 'change', message: '验证码不能为空' }\r\n        ]\r\n      },\r\n      passwordType: 'password',\r\n      capsTooltip: false,\r\n      loading: false,\r\n      showDialog: false,\r\n      redirect: undefined,\r\n      otherQuery: {},\r\n      currentTime: null,\r\n      sysInfo: ''\r\n    }\r\n  },\r\n  watch: {\r\n    $route: {\r\n      handler: function(route) {\r\n        const query = route.query\r\n        if (query) {\r\n          this.redirect = query.redirect\r\n          this.otherQuery = this.getOtherQuery(query)\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getCode()\r\n    // window.addEventListener('storage', this.afterQRScan)\r\n    this.getCurrentTime()\r\n    this.getSystemSetting()\r\n  },\r\n  mounted() {\r\n    if (this.loginForm.username === '') {\r\n      this.$refs.username.focus()\r\n    } else if (this.loginForm.password === '') {\r\n      this.$refs.password.focus()\r\n    }\r\n    window.addEventListener('resize', () => {\r\n      this.refreshParticles = false\r\n      this.$nextTick(() => (this.refreshParticles = true))\r\n    })\r\n  },\r\n  destroyed() {\r\n    clearInterval(this.timer)\r\n    window.removeEventListener('resize', () => {})\r\n    // window.removeEventListener('storage', this.afterQRScan)\r\n  },\r\n  methods: {\r\n    getSystemSetting() {\r\n      this.$store.dispatch('system/settingDetail').then((ret) => {\r\n        this.sysInfo = ret\r\n        document.title = ret.sys_app_name\r\n      })\r\n    },\r\n    getCurrentTime() {\r\n      this.timer = setInterval((_) => {\r\n        this.currentTime = moment().format('YYYY-MM-DD HH时mm分ss秒')\r\n      }, 1000)\r\n    },\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        if (res !== undefined) {\r\n          this.codeUrl = res.data\r\n          this.loginForm.uuid = res.id\r\n        }\r\n      })\r\n    },\r\n    checkCapslock({ shiftKey, key } = {}) {\r\n      if (key && key.length === 1) {\r\n        if (\r\n          (shiftKey && key >= 'a' && key <= 'z') ||\r\n          (!shiftKey && key >= 'A' && key <= 'Z')\r\n        ) {\r\n          this.capsTooltip = true\r\n        } else {\r\n          this.capsTooltip = false\r\n        }\r\n      }\r\n      if (key === 'CapsLock' && this.capsTooltip === true) {\r\n        this.capsTooltip = false\r\n      }\r\n    },\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n    handleLogin() {\r\n      this.$refs.loginForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true\r\n          this.$store\r\n            .dispatch('user/login', this.loginForm)\r\n            .then(() => {\r\n              this.$router\r\n                .push({ path: this.redirect || '/', query: this.otherQuery })\r\n                .catch(() => {})\r\n            })\r\n            .catch(() => {\r\n              this.loading = false\r\n              this.getCode()\r\n            })\r\n        } else {\r\n          console.log('error submit!!')\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    getOtherQuery(query) {\r\n      return Object.keys(query).reduce((acc, cur) => {\r\n        if (cur !== 'redirect') {\r\n          acc[cur] = query[cur]\r\n        }\r\n        return acc\r\n      }, {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 修复input 背景不协调 和光标变色 */\r\n/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */\r\n\r\n$bg: #283443;\r\n$light_gray: #fff;\r\n$cursor: #fff;\r\n\r\n#bottom_layer {\r\n  visibility: hidden;\r\n  width: 3000px;\r\n  position: fixed;\r\n  z-index: 302;\r\n  bottom: 0;\r\n  left: 0;\r\n  height: 39px;\r\n  padding-top: 1px;\r\n  zoom: 1;\r\n  margin: 0;\r\n  line-height: 39px;\r\n  // background: #0e6cff;\r\n}\r\n#bottom_layer .lh {\r\n  display: inline-block;\r\n  margin-right: 14px;\r\n}\r\n#bottom_layer .lh .emphasize {\r\n  text-decoration: underline;\r\n  font-weight: 700;\r\n}\r\n#bottom_layer .lh:last-child {\r\n  margin-left: -2px;\r\n  margin-right: 0;\r\n}\r\n#bottom_layer .lh.activity {\r\n  font-weight: 700;\r\n  text-decoration: underline;\r\n}\r\n#bottom_layer a {\r\n  font-size: 12px;\r\n  text-decoration: none;\r\n}\r\n#bottom_layer .text-color {\r\n  color: #bbb;\r\n}\r\n#bottom_layer .aria-img {\r\n  width: 49px;\r\n  height: 20px;\r\n  margin-bottom: -5px;\r\n}\r\n#bottom_layer a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .s-bottom-layer-content {\r\n  margin: 0 17px;\r\n  text-align: center;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line {\r\n  display: inline;\r\n}\r\n#bottom_layer .s-bottom-layer-content .auto-transform-line:first-child {\r\n  margin-right: 14px;\r\n}\r\n.s-bottom-space {\r\n  position: static;\r\n  width: 100%;\r\n  height: 40px;\r\n  margin: 23px auto 12px;\r\n}\r\n#bottom_layer .open-content-info a:hover {\r\n  color: #fff;\r\n}\r\n#bottom_layer .open-content-info .text-color {\r\n  color: #626675;\r\n}\r\n.open-content-info {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 20px;\r\n}\r\n.open-content-info > span {\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n.open-content-info > span:hover {\r\n  color: #fff;\r\n}\r\n.open-content-info .tip-hover-panel {\r\n  position: absolute;\r\n  display: none;\r\n  padding-bottom: 18px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip {\r\n  max-width: 560px;\r\n  padding: 8px 12px 8px 12px;\r\n  background: #fff;\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(0, 0, 0, 0.05);\r\n  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);\r\n  text-align: left;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper {\r\n  white-space: nowrap;\r\n  line-height: 20px;\r\n}\r\n.open-content-info .tip-hover-panel .rest_info_tip .tip-wrapper .tip-item {\r\n  height: 20px;\r\n  line-height: 20px;\r\n}\r\n.open-content-info\r\n  .tip-hover-panel\r\n  .rest_info_tip\r\n  .tip-wrapper\r\n  .tip-item:last-child {\r\n  margin-right: 0;\r\n}\r\n@media screen and (max-width: 515px) {\r\n  .open-content-info {\r\n    width: 16px;\r\n  }\r\n  .open-content-info .tip-hover-panel {\r\n    right: -16px !important;\r\n  }\r\n}\r\n.footer {\r\n  background-color: #0e6cff;\r\n  margin-bottom: -20px;\r\n}\r\n\r\n.login-container {\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  margin: 0 auto;\r\n  background: url(\"../../assets/login.png\") no-repeat;\r\n  background-color: #0e6cff;\r\n  position: relative;\r\n  background-size: cover;\r\n  height: 100vh;\r\n  background-position: 50%;\r\n}\r\n\r\n#particles-js {\r\n  z-index: 1;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: absolute;\r\n}\r\n\r\n.login-weaper {\r\n  margin: 0 auto;\r\n  width: 1000px;\r\n  -webkit-box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);\r\n  z-index: 1000;\r\n}\r\n\r\n.login-left {\r\n  border-top-left-radius: 5px;\r\n  border-bottom-left-radius: 5px;\r\n  -webkit-box-pack: center;\r\n  -ms-flex-pack: center;\r\n  justify-content: center;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-box-direction: normal;\r\n  -ms-flex-direction: column;\r\n  flex-direction: column;\r\n  background-color: rgba(64, 158, 255, 0);\r\n  color: #fff;\r\n  float: left;\r\n  width: 50%;\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  .login-time {\r\n    position: absolute;\r\n    left: 25px;\r\n    top: 25px;\r\n    width: 100%;\r\n    color: #fff;\r\n    opacity: 0.9;\r\n    font-size: 18px;\r\n    overflow: hidden;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.login-left .img {\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 3px;\r\n}\r\n\r\n.login-left .title {\r\n  text-align: center;\r\n  color: #fff;\r\n  letter-spacing: 2px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.login-border {\r\n  position: relative;\r\n  min-height: 500px;\r\n  -webkit-box-align: center;\r\n  -ms-flex-align: center;\r\n  align-items: center;\r\n  display: -webkit-box;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  border-left: none;\r\n  border-top-right-radius: 5px;\r\n  border-bottom-right-radius: 5px;\r\n  color: #fff;\r\n  background-color: hsla(0, 0%, 100%, 0.9);\r\n  width: 50%;\r\n  float: left;\r\n}\r\n\r\n.login-main {\r\n  margin: 0 auto;\r\n  width: 65%;\r\n}\r\n\r\n.login-title {\r\n  color: #333;\r\n  margin-bottom: 40px;\r\n  font-weight: 500;\r\n  font-size: 22px;\r\n  text-align: center;\r\n  letter-spacing: 4px;\r\n}\r\n\r\n@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {\r\n  .login-container .el-input input {\r\n    color: $cursor;\r\n  }\r\n}\r\n\r\n/* reset element-ui css */\r\n.login-container {\r\n  ::v-deep .el-input {\r\n    display: inline-block;\r\n    height: 47px;\r\n    width: 85%;\r\n\r\n    input {\r\n      background: transparent;\r\n      border: 0px;\r\n      -webkit-appearance: none;\r\n      border-radius: 0px;\r\n      padding: 12px 5px 12px 15px;\r\n      color: #333;\r\n      height: 47px;\r\n      caret-color: #333;\r\n\r\n      &:-webkit-autofill {\r\n        box-shadow: 0 0 0px 1000px $bg inset !important;\r\n        -webkit-text-fill-color: $cursor !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-form-item {\r\n    border: 1px solid rgba(0, 0, 0, 0.1);\r\n    background: rgba(255, 255, 255, 0.8);\r\n    border-radius: 5px;\r\n    color: #454545;\r\n  }\r\n}\r\n$bg: #2d3a4b;\r\n$dark_gray: #889aa4;\r\n$light_gray: #eee;\r\n\r\n.login-container {\r\n  .tips {\r\n    font-size: 14px;\r\n    color: #fff;\r\n    margin-bottom: 10px;\r\n\r\n    span {\r\n      &:first-of-type {\r\n        margin-right: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .svg-container {\r\n    padding: 6px 5px 6px 15px;\r\n    color: $dark_gray;\r\n    vertical-align: middle;\r\n    width: 30px;\r\n    display: inline-block;\r\n  }\r\n\r\n  .title-container {\r\n    position: relative;\r\n\r\n    .title {\r\n      font-size: 26px;\r\n      color: $light_gray;\r\n      margin: 0px auto 40px auto;\r\n      text-align: center;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .show-pwd {\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 7px;\r\n    font-size: 16px;\r\n    color: $dark_gray;\r\n    cursor: pointer;\r\n    user-select: none;\r\n  }\r\n\r\n  .thirdparty-button {\r\n    position: absolute;\r\n    right: 0;\r\n    bottom: 6px;\r\n  }\r\n\r\n  @media only screen and (max-width: 470px) {\r\n    .thirdparty-button {\r\n      display: none;\r\n    }\r\n    .login-weaper {\r\n      width: 100%;\r\n      padding: 0 30px;\r\n      box-sizing: border-box;\r\n      box-shadow: none;\r\n    }\r\n    .login-main {\r\n      width: 80%;\r\n    }\r\n    .login-left {\r\n      display: none !important;\r\n    }\r\n    .login-border {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}