{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue?vue&type=style&index=0&id=5bc425de&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue", "mtime": 1753924830024}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5jaGFydC1jYXJkLWhlYWRlciB7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgd2lkdGg6IDEwMCU7DQogICAgLm1ldGEgew0KICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjQ1KTsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAyMnB4Ow0KICAgIH0NCiAgfQ0KICAuY2hhcnQtY2FyZC1hY3Rpb24gew0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgdG9wOiAwOw0KICAgIHJpZ2h0OiAwOw0KICB9DQogIC5jaGFydC1jYXJkLWZvb3RlciB7DQogICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOGU4ZTg7DQogICAgcGFkZGluZy10b3A6IDlweDsNCiAgICBtYXJnaW4tdG9wOiA4cHg7DQogICAgPiAqIHsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICB9DQogICAgLmZpZWxkIHsNCiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICBtYXJnaW46IDA7DQogICAgICBjb2xvcjogcmdiYSgwLDAsMCwuNjUpOw0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIH0NCiAgfQ0KICAuY2hhcnQtY2FyZC1jb250ZW50IHsNCiAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICBoZWlnaHQ6IDQ2cHg7DQogICAgd2lkdGg6IDEwMCU7DQogICAgLmNvbnRlbnQtZml4IHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIGxlZnQ6IDA7DQogICAgICBib3R0b206IDA7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICB9DQogIH0NCiAgLnRvdGFsIHsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgIGNvbG9yOiAjMDAwOw0KICAgIG1hcmdpbi10b3A6IDRweDsNCiAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgIGZvbnQtc2l6ZTogMzBweDsNCiAgICBsaW5lLWhlaWdodDogMzhweDsNCiAgICBoZWlnaHQ6IDM4cHg7DQogIH0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue"], "names": [], "mappings": ";EAsDE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACV;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,EAAE,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACd", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/ChartCard/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card :loading=\"loading\" :body-style=\"{ padding: '20px 24px 8px' }\" :bordered=\"false\">\r\n    <div class=\"chart-card-header\">\r\n      <div class=\"meta\">\r\n        <span class=\"chart-card-title\">\r\n          <slot name=\"title\">\r\n            {{ title }}\r\n          </slot>\r\n        </span>\r\n        <span class=\"chart-card-action\">\r\n          <slot name=\"action\" />\r\n        </span>\r\n      </div>\r\n      <div class=\"total\">\r\n        <slot name=\"total\">\r\n          <span>{{ typeof total === 'function' && total() || total }}</span>\r\n        </slot>\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card-content\">\r\n      <div class=\"content-fix\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card-footer\">\r\n      <div class=\"field\">\r\n        <slot name=\"footer\" />\r\n      </div>\r\n    </div>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ChartCard',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    total: {\r\n      type: [Function, Number, String],\r\n      required: false,\r\n      default: null\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-card-header {\r\n    position: relative;\r\n    overflow: hidden;\r\n    width: 100%;\r\n    .meta {\r\n      position: relative;\r\n      overflow: hidden;\r\n      width: 100%;\r\n      color: rgba(0, 0, 0, .45);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n  }\r\n  .chart-card-action {\r\n    cursor: pointer;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n  }\r\n  .chart-card-footer {\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 9px;\r\n    margin-top: 8px;\r\n    > * {\r\n      position: relative;\r\n    }\r\n    .field {\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 0;\r\n      color: rgba(0,0,0,.65);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  .chart-card-content {\r\n    margin-bottom: 12px;\r\n    position: relative;\r\n    height: 46px;\r\n    width: 100%;\r\n    .content-fix {\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n    }\r\n  }\r\n  .total {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    word-break: break-all;\r\n    white-space: nowrap;\r\n    color: #000;\r\n    margin-top: 4px;\r\n    margin-bottom: 0;\r\n    font-size: 30px;\r\n    line-height: 38px;\r\n    height: 38px;\r\n  }\r\n</style>\r\n"]}]}