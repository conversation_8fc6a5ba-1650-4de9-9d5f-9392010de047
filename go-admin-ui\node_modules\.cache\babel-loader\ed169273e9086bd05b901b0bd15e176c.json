{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\directive\\permission\\permisaction.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\directive\\permission\\permisaction.js", "mtime": 1753924830076}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3Iuc29tZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIGFsbF9wZXJtaXNzaW9uID0gJyo6KjoqJzsKICAgIHZhciBwZXJtaXNzaW9ucyA9IHN0b3JlLmdldHRlcnMgJiYgc3RvcmUuZ2V0dGVycy5wZXJtaXNhY3Rpb247CiAgICBpZiAodmFsdWUgJiYgdmFsdWUgaW5zdGFuY2VvZiBBcnJheSAmJiB2YWx1ZS5sZW5ndGggPiAwKSB7CiAgICAgIHZhciBwZXJtaXNzaW9uRmxhZyA9IHZhbHVlOwogICAgICB2YXIgaGFzUGVybWlzc2lvbnMgPSBwZXJtaXNzaW9ucy5zb21lKGZ1bmN0aW9uIChwZXJtaXNzaW9uKSB7CiAgICAgICAgcmV0dXJuIGFsbF9wZXJtaXNzaW9uID09PSBwZXJtaXNzaW9uIHx8IHBlcm1pc3Npb25GbGFnLmluY2x1ZGVzKHBlcm1pc3Npb24pOwogICAgICB9KTsKICAgICAgaWYgKCFoYXNQZXJtaXNzaW9ucykgewogICAgICAgIGVsLnBhcmVudE5vZGUgJiYgZWwucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChlbCk7CiAgICAgIH0KICAgIH0gZWxzZSB7CiAgICAgIHRocm93IG5ldyBFcnJvcigiXHU4QkY3XHU4QkJFXHU3RjZFXHU2NENEXHU0RjVDXHU2NzQzXHU5NjUwXHU2ODA3XHU3QjdFXHU1MDNDIik7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["store", "inserted", "el", "binding", "vnode", "value", "all_permission", "permissions", "getters", "permisaction", "Array", "length", "permissionFlag", "hasPermissions", "some", "permission", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/directive/permission/permisaction.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const all_permission = '*:*:*'\r\n    const permissions = store.getters && store.getters.permisaction\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const permissionFlag = value\r\n\r\n      const hasPermissions = permissions.some(permission => {\r\n        return all_permission === permission || permissionFlag.includes(permission)\r\n      })\r\n\r\n      if (!hasPermissions) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error(`请设置操作权限标签值`)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,SAAS;AAE3B,eAAe;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,cAAc,GAAG,OAAO;IAC9B,IAAMC,WAAW,GAAGP,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACC,YAAY;IAE/D,IAAIJ,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,cAAc,GAAGP,KAAK;MAE5B,IAAMQ,cAAc,GAAGN,WAAW,CAACO,IAAI,CAAC,UAAAC,UAAU,EAAI;QACpD,OAAOT,cAAc,KAAKS,UAAU,IAAIH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;MAC7E,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,EAAE;QACnBX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,+DAAa,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}]}