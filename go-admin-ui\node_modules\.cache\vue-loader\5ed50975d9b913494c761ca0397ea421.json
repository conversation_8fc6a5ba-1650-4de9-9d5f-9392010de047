{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue?vue&type=style&index=0&id=2b33be98&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1753924830430}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5zb2NpYWwtc2lnbnVwLWNvbnRhaW5lciB7DQogICAgbWFyZ2luOiAyMHB4IDA7DQogICAgLnNpZ24tYnRuIHsNCiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICB9DQogICAgLmljb24gew0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBmb250LXNpemU6IDI0cHg7DQogICAgICBtYXJnaW4tdG9wOiA4cHg7DQogICAgfQ0KICAgIC53eC1zdmctY29udGFpbmVyLA0KICAgIC5xcS1zdmctY29udGFpbmVyIHsNCiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgIHdpZHRoOiA0MHB4Ow0KICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBwYWRkaW5nLXRvcDogMXB4Ow0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgIG1hcmdpbi1yaWdodDogNXB4Ow0KICAgIH0NCiAgICAud3gtc3ZnLWNvbnRhaW5lciB7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjRkYTcwOw0KICAgIH0NCiAgICAucXEtc3ZnLWNvbnRhaW5lciB7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNkJBMkQ2Ow0KICAgICAgbWFyZ2luLWxlZnQ6IDUwcHg7DQogICAgfQ0KICB9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue"], "names": [], "mappings": ";EAwCE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/login/components/SocialSignin.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"social-signup-container\">\r\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\r\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\r\n      WeChat\r\n    </div>\r\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\r\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\r\n      QQ\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import openWindow from '@/utils/open-window'\r\n\r\nexport default {\r\n  name: 'SocialSignin',\r\n  methods: {\r\n    wechatHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const appid = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    },\r\n    tencentHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const client_id = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .social-signup-container {\r\n    margin: 20px 0;\r\n    .sign-btn {\r\n      display: inline-block;\r\n      cursor: pointer;\r\n    }\r\n    .icon {\r\n      color: #fff;\r\n      font-size: 24px;\r\n      margin-top: 8px;\r\n    }\r\n    .wx-svg-container,\r\n    .qq-svg-container {\r\n      display: inline-block;\r\n      width: 40px;\r\n      height: 40px;\r\n      line-height: 40px;\r\n      text-align: center;\r\n      padding-top: 1px;\r\n      border-radius: 4px;\r\n      margin-bottom: 20px;\r\n      margin-right: 5px;\r\n    }\r\n    .wx-svg-container {\r\n      background-color: #24da70;\r\n    }\r\n    .qq-svg-container {\r\n      background-color: #6BA2D6;\r\n      margin-left: 50px;\r\n    }\r\n  }\r\n</style>\r\n"]}]}