{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\job\\sys-job.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\job\\sys-job.js", "mtime": 1753924829934}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivolN5c0pvYuWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFN5c0pvYihxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvc3lzam9iJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivolN5c0pvYuivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0U3lzSm9iKGpvYklkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9zeXNqb2IvJyArIGpvYklkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop5TeXNKb2IKZXhwb3J0IGZ1bmN0aW9uIGFkZFN5c0pvYihkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9zeXNqb2InLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuVN5c0pvYgpleHBvcnQgZnVuY3Rpb24gdXBkYXRlU3lzSm9iKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3N5c2pvYicsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaRTeXNKb2IKZXhwb3J0IGZ1bmN0aW9uIGRlbFN5c0pvYihkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9zeXNqb2InLAogICAgbWV0aG9kOiAnZGVsZXRlJywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g56e76ZmkU3lzSm9iCmV4cG9ydCBmdW5jdGlvbiByZW1vdmVKb2Ioam9iSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2pvYi9yZW1vdmUvJyArIGpvYklkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDlkK/liqhTeXNKb2IKZXhwb3J0IGZ1bmN0aW9uIHN0YXJ0Sm9iKGpvYklkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9qb2Ivc3RhcnQvJyArIGpvYklkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9"}, {"version": 3, "names": ["request", "listSysJob", "query", "url", "method", "params", "getSysJob", "jobId", "addSysJob", "data", "updateSysJob", "delSysJob", "removeJob", "startJob"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/job/sys-job.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询SysJob列表\r\nexport function listSysJob(query) {\r\n  return request({\r\n    url: '/api/v1/sysjob',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询SysJob详细\r\nexport function getSysJob(jobId) {\r\n  return request({\r\n    url: '/api/v1/sysjob/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增SysJob\r\nexport function addSysJob(data) {\r\n  return request({\r\n    url: '/api/v1/sysjob',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改SysJob\r\nexport function updateSysJob(data) {\r\n  return request({\r\n    url: '/api/v1/sysjob',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除SysJob\r\nexport function delSysJob(data) {\r\n  return request({\r\n    url: '/api/v1/sysjob',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 移除SysJob\r\nexport function removeJob(jobId) {\r\n  return request({\r\n    url: '/api/v1/job/remove/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 启动SysJob\r\nexport function startJob(jobId) {\r\n  return request({\r\n    url: '/api/v1/job/start/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,KAAK;IAC9BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,SAASA,CAACL,KAAK,EAAE;EAC/B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB,GAAGI,KAAK;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,QAAQA,CAACN,KAAK,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGI,KAAK;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}