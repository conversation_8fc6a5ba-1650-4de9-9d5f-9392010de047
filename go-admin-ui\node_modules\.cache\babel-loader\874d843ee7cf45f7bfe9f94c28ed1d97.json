{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue", "mtime": 1753924830444}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0aW1lbGluZTogW3sKICAgICAgICB0aW1lc3RhbXA6ICcyMDE5LzQvMjAnLAogICAgICAgIHRpdGxlOiAnVXBkYXRlIEdpdGh1YiB0ZW1wbGF0ZScsCiAgICAgICAgY29udGVudDogJ1BhbkppYUNoZW4gY29tbWl0dGVkIDIwMTkvNC8yMCAyMDo0NicKICAgICAgfSwgewogICAgICAgIHRpbWVzdGFtcDogJzIwMTkvNC8yMScsCiAgICAgICAgdGl0bGU6ICdVcGRhdGUgR2l0aHViIHRlbXBsYXRlJywKICAgICAgICBjb250ZW50OiAnUGFuSmlhQ2hlbiBjb21taXR0ZWQgMjAxOS80LzIxIDIwOjQ2JwogICAgICB9LCB7CiAgICAgICAgdGltZXN0YW1wOiAnMjAxOS80LzIyJywKICAgICAgICB0aXRsZTogJ0J1aWxkIFRlbXBsYXRlJywKICAgICAgICBjb250ZW50OiAnUGFuSmlhQ2hlbiBjb21taXR0ZWQgMjAxOS80LzIyIDIwOjQ2JwogICAgICB9LCB7CiAgICAgICAgdGltZXN0YW1wOiAnMjAxOS80LzIzJywKICAgICAgICB0aXRsZTogJ1JlbGVhc2UgTmV3IFZlcnNpb24nLAogICAgICAgIGNvbnRlbnQ6ICdQYW5KaWFDaGVuIGNvbW1pdHRlZCAyMDE5LzQvMjMgMjA6NDYnCiAgICAgIH1dCiAgICB9OwogIH0KfTs="}, {"version": 3, "names": ["data", "timeline", "timestamp", "title", "content"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue"], "sourcesContent": ["<template>\r\n  <div class=\"block\">\r\n    <el-timeline>\r\n      <el-timeline-item v-for=\"(item,index) of timeline\" :key=\"index\" :timestamp=\"item.timestamp\" placement=\"top\">\r\n        <el-card>\r\n          <h4>{{ item.title }}</h4>\r\n          <p>{{ item.content }}</p>\r\n        </el-card>\r\n      </el-timeline-item>\r\n    </el-timeline>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      timeline: [\r\n        {\r\n          timestamp: '2019/4/20',\r\n          title: 'Update Github template',\r\n          content: 'PanJiaChen committed 2019/4/20 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/21',\r\n          title: 'Update Github template',\r\n          content: 'PanJiaChen committed 2019/4/21 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/22',\r\n          title: 'Build Template',\r\n          content: 'PanJiaChen committed 2019/4/22 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/23',\r\n          title: 'Release New Version',\r\n          content: 'PanJiaChen committed 2019/4/23 20:46'\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAcA,eAAe;EACbA,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,CACR;QACEC,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE;MACX,CAAC,EACD;QACEF,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,wBAAwB;QAC/BC,OAAO,EAAE;MACX,CAAC,EACD;QACEF,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE;MACX,CAAC,EACD;QACEF,SAAS,EAAE,WAAW;QACtBC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE;MACX;IAEJ;EACF;AACF", "ignoreList": []}]}