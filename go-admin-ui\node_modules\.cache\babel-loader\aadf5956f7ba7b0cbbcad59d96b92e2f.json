{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\AppMain.vue", "mtime": 1753924830205}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdBcHBNYWluJywKICBjb21wdXRlZDogewogICAgY2FjaGVkVmlld3M6IGZ1bmN0aW9uIGNhY2hlZFZpZXdzKCkgewogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUudGFnc1ZpZXcuY2FjaGVkVmlld3M7CiAgICB9LAogICAga2V5OiBmdW5jdGlvbiBrZXkoKSB7CiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZS5wYXRoOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "computed", "cachedViews", "$store", "state", "tagsView", "key", "$route", "path"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\AppMain.vue"], "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AppMain',\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 93px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.fixed-header+.app-main {\r\n  padding-top: 93px;\r\n}\r\n\r\n// .hasTagsView {\r\n//   .app-main {\r\n//     /* 84 = navbar + tags-view = 50 + 34 */\r\n//     min-height: calc(100vh - 93px);\r\n//   }\r\n\r\n//   .fixed-header+.app-main {\r\n//     padding-top: 93px;\r\n//   }\r\n// }\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 15px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAWA,eAAe;EACbA,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE;IACRC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACH,WAAU;IAC9C,CAAC;IACDI,GAAG,WAAHA,GAAGA,CAAA,EAAG;MACJ,OAAO,IAAI,CAACC,MAAM,CAACC,IAAG;IACxB;EACF;AACF", "ignoreList": []}]}