{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-oper-log\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-oper-log\\index.vue", "mtime": 1753924830278}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["listSysOperlog", "delSysOperlog", "cleanOperlog", "formatJson", "name", "data", "loading", "ids", "multiple", "total", "list", "open", "statusOptions", "date<PERSON><PERSON><PERSON>", "form", "queryParams", "pageIndex", "pageSize", "title", "undefined", "operName", "businessType", "status", "createdAtOrder", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "addDateRange", "count", "statusFormat", "row", "column", "selectDictLabel", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "id", "length", "handleView", "handleDelete", "_this3", "operIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "code", "msgSuccess", "msg", "msgError", "catch", "handleClean", "_this4", "handleExport", "_this5", "downloadLoading", "Promise", "resolve", "_interopRequireWildcard", "require", "excel", "tHeader", "filterVal", "export_json_to_excel", "header", "filename", "autoWidth", "bookType"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-oper-log\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-width=\"68px\">\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"操作状态\"\r\n              clearable\r\n              size=\"small\"\r\n              style=\"width: 160px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in statusOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              size=\"small\"\r\n              type=\"datetimerange\"\r\n              :picker-options=\"pickerOptions\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              align=\"right\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysOperLog:remove']\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-permisaction=\"['admin:sysOperLog:export']\"\r\n              type=\"warning\"\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n            >导出</el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"list\" border @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"编号\" width=\"70\" prop=\"id\" />\r\n          <el-table-column\r\n            label=\"Request info\"\r\n            prop=\"operUrl\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-popover trigger=\"hover\" placement=\"top\">\r\n\r\n                <p>Request:\r\n                  <el-tag v-if=\"scope.row.requestMethod=='GET'\">{{ scope.row.requestMethod }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.requestMethod=='POST'\" type=\"success\">{{ scope.row.requestMethod }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.requestMethod=='PUT'\" type=\"warning\">{{ scope.row.requestMethod }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.requestMethod=='DELETE'\" type=\"danger\">{{ scope.row.requestMethod }}</el-tag>\r\n                  {{ scope.row.operUrl }}\r\n                </p>\r\n                <p>Host: {{ scope.row.operIp }}</p>\r\n                <p>Location: {{ scope.row.operLocation }}</p>\r\n                <p>耗时: {{ scope.row.latencyTime }}</p>\r\n                <div slot=\"reference\" class=\"name-wrapper\">\r\n                  <el-tag v-if=\"scope.row.requestMethod=='GET'\">{{ scope.row.requestMethod }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.requestMethod=='POST'\" type=\"success\">{{ scope.row.requestMethod }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.requestMethod=='PUT'\" type=\"warning\">{{ scope.row.requestMethod }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.requestMethod=='DELETE'\" type=\"danger\">{{ scope.row.requestMethod }}</el-tag>\r\n                  {{ scope.row.operUrl }}\r\n                </div>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作人员\"\r\n            prop=\"operName\"\r\n            width=\"160\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column\r\n            label=\"状态\"\r\n            prop=\"status\"\r\n            width=\"80\"\r\n            :show-overflow-tooltip=\"true\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag v-if=\"scope.row.status=='2'\" type=\"success\">{{ statusFormat(scope.row,scope.row.status) }}</el-tag>\r\n              <el-tag v-if=\"scope.row.status=='1'\" type=\"danger\">{{ statusFormat(scope.row,scope.row.status) }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作日期\" prop=\"operTime\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.operTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            width=\"80\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysOperLog:query']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleView(scope.row,scope.index)\"\r\n              >详细</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 操作日志详细 -->\r\n        <el-dialog title=\"操作日志详细\" :visible.sync=\"open\" width=\"700px\" :close-on-click-modal=\"false\">\r\n          <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"请求地址：\">{{ form.operUrl }}</el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item\r\n                  label=\"登录信息：\"\r\n                >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</el-form-item>\r\n              </el-col>\r\n\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"耗时：\">{{ form.latencyTime }}</el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"请求参数：\">{{ form.operParam }}</el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"返回参数：\">{{ form.jsonResult }}</el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作状态：\">\r\n                  <div v-if=\"form.status === '2'\">正常</div>\r\n                  <div v-else-if=\"form.status === '1'\">关闭</div>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作时间：\">{{ parseTime(form.operTime) }}</el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item v-if=\"form.status === 1\" label=\"异常信息：\">{{ form.errorMsg }}</el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n          <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button @click=\"open = false\">关 闭</el-button>\r\n          </div>\r\n        </el-dialog>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listSysOperlog, delSysOperlog, cleanOperlog } from '@/api/admin/sys-opera-log'\r\nimport { formatJson } from '@/utils'\r\n\r\nexport default {\r\n  name: 'SysOperLogManage',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      list: [],\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 类型数据字典\r\n      statusOptions: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        title: undefined,\r\n        operName: undefined,\r\n        businessType: undefined,\r\n        status: undefined,\r\n        createdAtOrder: 'desc'\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n\r\n    this.getDicts('sys_common_status').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询登录日志 */\r\n    getList() {\r\n      this.loading = true\r\n      listSysOperlog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.list = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    // 操作日志状态字典翻译\r\n    statusFormat(row, column) {\r\n      return this.selectDictLabel(this.statusOptions, row.status)\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true\r\n      this.form = row\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operIds = (row.id && [row.id]) || this.ids\r\n      this.$confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delSysOperlog({ 'ids': operIds })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$confirm('是否确认清空所有操作日志数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return cleanOperlog()\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // const queryParams = this.queryParams\r\n      this.$confirm('是否确认导出所有操作日志数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.downloadLoading = true\r\n        import('@/vendor/Export2Excel').then(excel => {\r\n          const tHeader = ['日志编号', '系统模块', '操作类型', '请求方式', '操作人员', '主机', '操作地点', '操作状态', '操作url', '操作日期']\r\n          const filterVal = ['ID', 'title', 'businessType', 'method', 'operName', 'operIp', 'operLocation', 'status', 'operUrl', 'operTime']\r\n          const list = this.list\r\n          const data = formatJson(filterVal, list)\r\n          excel.export_json_to_excel({\r\n            header: tHeader,\r\n            data,\r\n            filename: '操作日志',\r\n            autoWidth: true, // Optional\r\n            bookType: 'xlsx' // Optional\r\n          })\r\n          this.downloadLoading = false\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n"], "mappings": ";;;;;;;AA4LA,SAASA,cAAc,EAAEC,aAAa,EAAEC,YAAW,QAAS,2BAA0B;AACtF,SAASC,UAAS,QAAS,SAAQ;AAEnC,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,GAAG,EAAE,EAAE;MACP;MACAC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,CAAC;MACR;MACAC,IAAI,EAAE,EAAE;MACR;MACAC,IAAI,EAAE,KAAK;MACX;MACAC,aAAa,EAAE,EAAE;MACjB;MACAC,SAAS,EAAE,EAAE;MACb;MACAC,IAAI,EAAE,CAAC,CAAC;MACR;MACAC,WAAW,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAEC,SAAS;QAChBC,QAAQ,EAAED,SAAS;QACnBE,YAAY,EAAEF,SAAS;QACvBG,MAAM,EAAEH,SAAS;QACjBI,cAAc,EAAE;MAClB;IACF;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,OAAO,CAAC;IAEb,IAAI,CAACC,QAAQ,CAAC,mBAAmB,CAAC,CAACC,IAAI,CAAC,UAAAC,QAAO,EAAK;MAClDJ,KAAI,CAACb,aAAY,GAAIiB,QAAQ,CAACxB,IAAG;IACnC,CAAC;EACH,CAAC;EACDyB,OAAO,EAAE;IACP,aACAJ,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAK,MAAA;MACR,IAAI,CAACzB,OAAM,GAAI,IAAG;MAClBN,cAAc,CAAC,IAAI,CAACgC,YAAY,CAAC,IAAI,CAACjB,WAAW,EAAE,IAAI,CAACF,SAAS,CAAC,CAAC,CAACe,IAAI,CAAC,UAAAC,QAAO,EAAK;QACnFE,MAAI,CAACrB,IAAG,GAAImB,QAAQ,CAACxB,IAAI,CAACK,IAAG;QAC7BqB,MAAI,CAACtB,KAAI,GAAIoB,QAAQ,CAACxB,IAAI,CAAC4B,KAAI;QAC/BF,MAAI,CAACzB,OAAM,GAAI,KAAI;MACrB,CACA;IACF,CAAC;IACD;IACA4B,YAAY,WAAZA,YAAYA,CAACC,GAAG,EAAEC,MAAM,EAAE;MACxB,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACzB,aAAa,EAAEuB,GAAG,CAACb,MAAM;IAC5D,CAAC;IACD,aACAgB,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACvB,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAACU,OAAO,CAAC;IACf,CAAC;IACD,aACAa,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAAC1B,SAAQ,GAAI,EAAC;MAClB,IAAI,CAAC2B,SAAS,CAAC,WAAW;MAC1B,IAAI,CAACF,WAAW,CAAC;IACnB,CAAC;IACD;IACAG,qBAAqB,WAArBA,qBAAqBA,CAACC,SAAS,EAAE;MAC/B,IAAI,CAACnC,GAAE,GAAImC,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACC,EAAE;MAAA;MACxC,IAAI,CAACrC,QAAO,GAAI,CAACkC,SAAS,CAACI,MAAK;IAClC,CAAC;IACD,aACAC,UAAU,WAAVA,UAAUA,CAACZ,GAAG,EAAE;MACd,IAAI,CAACxB,IAAG,GAAI,IAAG;MACf,IAAI,CAACG,IAAG,GAAIqB,GAAE;IAChB,CAAC;IACD,aACAa,YAAY,WAAZA,YAAYA,CAACb,GAAG,EAAE;MAAA,IAAAc,MAAA;MAChB,IAAMC,OAAM,GAAKf,GAAG,CAACU,EAAC,IAAK,CAACV,GAAG,CAACU,EAAE,CAAC,IAAK,IAAI,CAACtC,GAAE;MAC/C,IAAI,CAAC4C,QAAQ,CAAC,cAAa,GAAID,OAAM,GAAI,QAAQ,EAAE,IAAI,EAAE;QACvDE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC1B,IAAI,CAAC,YAAW;QACjB,OAAO3B,aAAa,CAAC;UAAE,KAAK,EAAEiD;QAAQ,CAAC;MACzC,CAAC,CAAC,CAACtB,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAAC0B,IAAG,KAAM,GAAG,EAAE;UACzBN,MAAI,CAACO,UAAU,CAAC3B,QAAQ,CAAC4B,GAAG;UAC5BR,MAAI,CAACtC,IAAG,GAAI,KAAI;UAChBsC,MAAI,CAACvB,OAAO,CAAC;QACf,OAAO;UACLuB,MAAI,CAACS,QAAQ,CAAC7B,QAAQ,CAAC4B,GAAG;QAC5B;MACF,CAAC,CAAC,CAACE,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACD,aACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACZ,IAAI,CAACV,QAAQ,CAAC,kBAAkB,EAAE,IAAI,EAAE;QACtCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC1B,IAAI,CAAC,YAAW;QACjB,OAAO1B,YAAY,CAAC;MACtB,CAAC,CAAC,CAAC0B,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAAC0B,IAAG,KAAM,GAAG,EAAE;UACzBM,MAAI,CAACL,UAAU,CAAC3B,QAAQ,CAAC4B,GAAG;UAC5BI,MAAI,CAAClD,IAAG,GAAI,KAAI;UAChBkD,MAAI,CAACnC,OAAO,CAAC;QACf,OAAO;UACLmC,MAAI,CAACH,QAAQ,CAAC7B,QAAQ,CAAC4B,GAAG;QAC5B;MACF,CAAC,CAAC,CAACE,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACD,aACAG,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACb;MACA,IAAI,CAACZ,QAAQ,CAAC,kBAAkB,EAAE,IAAI,EAAE;QACtCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC1B,IAAI,CAAC,YAAM;QACZmC,MAAI,CAACC,eAAc,GAAI,IAAG;QAC1BC,OAAA,CAAAC,OAAA,GAAAtC,IAAA;UAAA,OAAAuC,uBAAA,CAAAC,OAAA,CAAO,uBAAuB;QAAA,GAAExC,IAAI,CAAC,UAAAyC,KAAI,EAAK;UAC5C,IAAMC,OAAM,GAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;UAC9F,IAAMC,SAAQ,GAAI,CAAC,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU;UACjI,IAAM7D,IAAG,GAAIqD,MAAI,CAACrD,IAAG;UACrB,IAAML,IAAG,GAAIF,UAAU,CAACoE,SAAS,EAAE7D,IAAI;UACvC2D,KAAK,CAACG,oBAAoB,CAAC;YACzBC,MAAM,EAAEH,OAAO;YACfjE,IAAI,EAAJA,IAAI;YACJqE,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,IAAI;YAAE;YACjBC,QAAQ,EAAE,MAAK,CAAE;UACnB,CAAC;UACDb,MAAI,CAACC,eAAc,GAAI,KAAI;QAC7B,CAAC;MACH,CAAC;IACH;EACF;AACF", "ignoreList": []}]}