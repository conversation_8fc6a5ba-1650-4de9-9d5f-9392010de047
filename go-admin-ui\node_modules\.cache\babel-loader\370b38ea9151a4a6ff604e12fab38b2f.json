{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-api\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-api\\index.vue", "mtime": 1753924830270}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["addSysApi", "delSysApi", "getSysApi", "listSysApi", "updateSysApi", "name", "components", "data", "dialog", "loading", "ids", "single", "multiple", "total", "title", "open", "isEdit", "typeOptions", "sysapiList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageIndex", "pageSize", "undefined", "path", "type", "action", "parentId", "form", "rules", "required", "message", "trigger", "created", "getList", "methods", "handleClose", "done", "_this", "then", "response", "list", "count", "cancel", "reset", "id", "paths", "sort", "resetForm", "parentIdFormat", "row", "selectItemsLabel", "parentIdOptions", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSortChang", "column", "prop", "order", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "_this2", "submitForm", "_this3", "$refs", "validate", "valid", "code", "msgSuccess", "msg", "msgError", "handleDelete", "_this4", "Ids", "$confirm", "confirmButtonText", "cancelButtonText", "catch"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-api\\index.vue"], "sourcesContent": ["\r\n<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-width=\"48px\">\r\n          <el-form-item label=\"标题\" prop=\"title\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入标题\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"地址\" prop=\"path\">\r\n            <el-input\r\n              v-model=\"queryParams.path\"\r\n              placeholder=\"请输入地址\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"Method\" prop=\"action\">\r\n            <el-select\r\n              v-model=\"queryParams.action\"\r\n              placeholder=\"请选择Method\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <el-option value=\"GET\">GET</el-option>\r\n              <el-option value=\"POST\">POST</el-option>\r\n              <el-option value=\"PUT\">PUT</el-option>\r\n              <el-option value=\"DELETE\">DELETE</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"类型\" prop=\"type\">\r\n            <el-select\r\n              v-model=\"queryParams.type\"\r\n              placeholder=\"请选择类型\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <el-option value=\"SYS\">SYS</el-option>\r\n              <el-option value=\"BUS\">BUS</el-option>\r\n              <el-option value=\"暂无\">暂无</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"sysapiList\"\r\n          border\r\n          @selection-change=\"handleSelectionChange\"\r\n          @sort-change=\"handleSortChang\"\r\n        >\r\n          <el-table-column\r\n            label=\"标题\"\r\n            header-align=\"center\"\r\n            align=\"left\"\r\n            prop=\"title\"\r\n            fixed=\"left\"\r\n            sortable=\"custom\"\r\n            width=\"260px\"\r\n            :show-overflow-tooltip=\"true\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.type=='SYS' && scope.row.title!=''\"><el-tag type=\"success\">{{ '['+scope.row.type +'] '+ scope.row.title }}</el-tag></span>\r\n              <span v-if=\"scope.row.type!='SYS' && scope.row.title!=''\"><el-tag type=\"\">{{ '['+scope.row.type +'] '+scope.row.title }}</el-tag></span>\r\n              <span v-if=\"scope.row.title==''\"><el-tag type=\"danger\">暂无</el-tag></span>\r\n\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column\r\n            label=\"Request Info\"\r\n            header-align=\"center\"\r\n            align=\"left\"\r\n            prop=\"path\"\r\n            sortable=\"custom\"\r\n            :show-overflow-tooltip=\"true\"\r\n          >\r\n            <!-- <template slot-scope=\"scope\">\r\n              <span>{{ \"[\"+scope.row.action +\"] \"+ scope.row.path }}</span>\r\n            </template> -->\r\n            <template slot-scope=\"scope\">\r\n              <el-popover trigger=\"hover\" placement=\"top\">\r\n                <p><span v-if=\"scope.row.type=='SYS' && scope.row.title!=''\"><el-tag type=\"success\">{{ '['+scope.row.type +'] '+ scope.row.title }}</el-tag></span>\r\n                  <span v-if=\"scope.row.type!='SYS' && scope.row.title!=''\"><el-tag type=\"\">{{ '['+scope.row.type +'] '+scope.row.title }}</el-tag></span>\r\n                  <span v-if=\"scope.row.title==''\"><el-tag type=\"danger\">暂无</el-tag></span>\r\n                </p>\r\n                <p>Handle: {{ scope.row.handle }}</p>\r\n                <p>Method:\r\n                  <el-tag v-if=\"scope.row.action=='GET'\">{{ scope.row.action }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.action=='POST'\" type=\"success\">{{ scope.row.action }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.action=='PUT'\" type=\"warning\">{{ scope.row.action }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.action=='DELETE'\" type=\"danger\">{{ scope.row.action }}</el-tag>\r\n                </p>\r\n                <p>接口类型: {{ scope.row.type }}</p>\r\n                <div slot=\"reference\" class=\"name-wrapper\">\r\n                  <el-tag v-if=\"scope.row.action=='GET'\">{{ scope.row.action }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.action=='POST'\" type=\"success\">{{ scope.row.action }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.action=='PUT'\" type=\"warning\">{{ scope.row.action }}</el-tag>\r\n                  <el-tag v-if=\"scope.row.action=='DELETE'\" type=\"danger\">{{ scope.row.action }}</el-tag>\r\n                  {{ scope.row.path }}\r\n                </div>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"创建时间\"\r\n            align=\"center\"\r\n            prop=\"createdAt\"\r\n            width=\"155px\"\r\n            sortable=\"custom\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"80px\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysApi:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n\r\n        <!-- 添加或修改对话框 -->\r\n        <el-drawer\r\n          ref=\"drawer\"\r\n          :title=\"title\"\r\n          :before-close=\"cancel\"\r\n          :visible.sync=\"open\"\r\n          direction=\"rtl\"\r\n          custom-class=\"demo-drawer\"\r\n        >\r\n          <div class=\"demo-drawer__content\">\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n\r\n              <el-form-item label=\"Handle\" prop=\"handle\">\r\n                <el-input\r\n                  v-model=\"form.handle\"\r\n                  placeholder=\"handle\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"标题\" prop=\"title\">\r\n                <el-input\r\n                  v-model=\"form.title\"\r\n                  placeholder=\"标题\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"类型\" prop=\"type\">\r\n                <el-select\r\n                  v-model=\"form.type\"\r\n                  placeholder=\"请选择类型\"\r\n                  clearable\r\n                  size=\"small\"\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                >\r\n                  <el-option value=\"SYS\">SYS</el-option>\r\n                  <el-option value=\"BUS\">BUS</el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"Method\" prop=\"action\">\r\n                <el-select\r\n                  v-model=\"form.action\"\r\n                  placeholder=\"请选择方式\"\r\n                  clearable\r\n                  size=\"small\"\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                >\r\n                  <el-option value=\"GET\">GET</el-option>\r\n                  <el-option value=\"POST\">POST</el-option>\r\n                  <el-option value=\"PUT\">PUT</el-option>\r\n                  <el-option value=\"DELETE\">DELETE</el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"地址\" prop=\"path\">\r\n                <el-input\r\n                  v-model=\"form.path\"\r\n                  :disabled=\"isEdit\"\r\n                  placeholder=\"地址\"\r\n                />\r\n              </el-form-item>\r\n\r\n            </el-form>\r\n            <div class=\"demo-drawer__footer\">\r\n              <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n              <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n          </div>\r\n\r\n        </el-drawer>\r\n\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { addSysApi, delSysApi, getSysApi, listSysApi, updateSysApi } from '@/api/admin/sys-api'\r\n\r\nexport default {\r\n  name: 'SysApiManage',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      dialog: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      isEdit: false,\r\n      // 类型数据字典\r\n      typeOptions: [],\r\n      sysapiList: [],\r\n      dateRange: [],\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        name: undefined,\r\n        title: undefined,\r\n        path: undefined,\r\n        type: undefined,\r\n        action: undefined,\r\n        parentId: undefined\r\n\r\n      },\r\n      // 表单参数\r\n      form: {\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],\r\n        path: [{ required: true, message: '地址不能为空', trigger: 'blur' }],\r\n        action: [{ required: true, message: '类型不能为空', trigger: 'blur' }],\r\n        parentId: [{ required: true, message: '按钮id不能为空', trigger: 'blur' }]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    handleClose(done) {\r\n      if (this.loading) {\r\n        return\r\n      }\r\n      done()\r\n    },\r\n    /** 查询参数列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listSysApi(this.queryParams).then(response => {\r\n        this.sysapiList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        title: undefined,\r\n        path: undefined,\r\n        paths: undefined,\r\n        action: undefined,\r\n        parentId: undefined,\r\n        sort: undefined\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    parentIdFormat(row) {\r\n      return this.selectItemsLabel(this.parentIdOptions, row.parentId)\r\n    },\r\n    // 文件\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加接口管理'\r\n      this.isEdit = false\r\n    },\r\n    /** 排序回调函数 */\r\n    handleSortChang(column, prop, order) {\r\n      prop = column.prop\r\n      order = column.order\r\n      if (this.order !== '' && this.order !== prop + 'Order') {\r\n        this.queryParams[this.order] = undefined\r\n      }\r\n      if (order === 'descending') {\r\n        this.queryParams[prop + 'Order'] = 'desc'\r\n        this.order = prop + 'Order'\r\n      } else if (order === 'ascending') {\r\n        this.queryParams[prop + 'Order'] = 'asc'\r\n        this.order = prop + 'Order'\r\n      } else {\r\n        this.queryParams[prop + 'Order'] = undefined\r\n      }\r\n      this.getList()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const id =\r\n                row.id || this.ids\r\n      getSysApi(id).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改接口管理'\r\n        this.isEdit = true\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id !== undefined) {\r\n            updateSysApi(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addSysApi(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      var Ids = (row.id && [row.id]) || this.ids\r\n\r\n      this.$confirm('是否确认删除编号为\"' + Ids + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delSysApi({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;AAqOA,SAASA,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAW,QAAS,qBAAoB;AAE9F,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAE,KAAK;MACb;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,GAAG,EAAE,EAAE;MACP;MACAC,MAAM,EAAE,IAAI;MACZ;MACAC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,CAAC;MACR;MACAC,KAAK,EAAE,EAAE;MACT;MACAC,IAAI,EAAE,KAAK;MACXC,MAAM,EAAE,KAAK;MACb;MACAC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MAEb;MACAC,WAAW,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZjB,IAAI,EAAEkB,SAAS;QACfT,KAAK,EAAES,SAAS;QAChBC,IAAI,EAAED,SAAS;QACfE,IAAI,EAAEF,SAAS;QACfG,MAAM,EAAEH,SAAS;QACjBI,QAAQ,EAAEJ;MAEZ,CAAC;MACD;MACAK,IAAI,EAAE,CACN,CAAC;MACD;MACAC,KAAK,EAAE;QACLf,KAAK,EAAE,CAAC;UAAEgB,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC/DR,IAAI,EAAE,CAAC;UAAEM,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAC9DN,MAAM,EAAE,CAAC;UAAEI,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QAChEL,QAAQ,EAAE,CAAC;UAAEG,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC;MACrE;IACF;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC;EACf,CAAC;EACDC,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAACC,IAAI,EAAE;MAChB,IAAI,IAAI,CAAC5B,OAAO,EAAE;QAChB;MACF;MACA4B,IAAI,CAAC;IACP,CAAC;IACD,aACAH,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAI,KAAA;MACR,IAAI,CAAC7B,OAAM,GAAI,IAAG;MAClBN,UAAU,CAAC,IAAI,CAACiB,WAAW,CAAC,CAACmB,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC5CF,KAAI,CAACpB,UAAS,GAAIsB,QAAQ,CAACjC,IAAI,CAACkC,IAAG;QACnCH,KAAI,CAACzB,KAAI,GAAI2B,QAAQ,CAACjC,IAAI,CAACmC,KAAI;QAC/BJ,KAAI,CAAC7B,OAAM,GAAI,KAAI;MACrB,CACA;IACF,CAAC;IACD;IACAkC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC5B,IAAG,GAAI,KAAI;MAChB,IAAI,CAAC6B,KAAK,CAAC;IACb,CAAC;IACD;IACAA,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAAChB,IAAG,GAAI;QACViB,EAAE,EAAEtB,SAAS;QACblB,IAAI,EAAEkB,SAAS;QACfT,KAAK,EAAES,SAAS;QAChBC,IAAI,EAAED,SAAS;QACfuB,KAAK,EAAEvB,SAAS;QAChBG,MAAM,EAAEH,SAAS;QACjBI,QAAQ,EAAEJ,SAAS;QACnBwB,IAAI,EAAExB;MACR;MACA,IAAI,CAACyB,SAAS,CAAC,MAAM;IACvB,CAAC;IACDC,cAAc,WAAdA,cAAcA,CAACC,GAAG,EAAE;MAClB,OAAO,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,eAAe,EAAEF,GAAG,CAACvB,QAAQ;IACjE,CAAC;IACD;IACA;IACA0B,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACjC,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAACa,OAAO,CAAC;IACf,CAAC;IACD,aACAoB,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAACnC,SAAQ,GAAI,EAAC;MAClB,IAAI,CAAC6B,SAAS,CAAC,WAAW;MAC1B,IAAI,CAACK,WAAW,CAAC;IACnB,CAAC;IACD,aACAE,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACX,KAAK,CAAC;MACX,IAAI,CAAC7B,IAAG,GAAI,IAAG;MACf,IAAI,CAACD,KAAI,GAAI,QAAO;MACpB,IAAI,CAACE,MAAK,GAAI,KAAI;IACpB,CAAC;IACD,aACAwC,eAAe,WAAfA,eAAeA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;MACnCD,IAAG,GAAID,MAAM,CAACC,IAAG;MACjBC,KAAI,GAAIF,MAAM,CAACE,KAAI;MACnB,IAAI,IAAI,CAACA,KAAI,KAAM,EAAC,IAAK,IAAI,CAACA,KAAI,KAAMD,IAAG,GAAI,OAAO,EAAE;QACtD,IAAI,CAACtC,WAAW,CAAC,IAAI,CAACuC,KAAK,IAAIpC,SAAQ;MACzC;MACA,IAAIoC,KAAI,KAAM,YAAY,EAAE;QAC1B,IAAI,CAACvC,WAAW,CAACsC,IAAG,GAAI,OAAO,IAAI,MAAK;QACxC,IAAI,CAACC,KAAI,GAAID,IAAG,GAAI,OAAM;MAC5B,OAAO,IAAIC,KAAI,KAAM,WAAW,EAAE;QAChC,IAAI,CAACvC,WAAW,CAACsC,IAAG,GAAI,OAAO,IAAI,KAAI;QACvC,IAAI,CAACC,KAAI,GAAID,IAAG,GAAI,OAAM;MAC5B,OAAO;QACL,IAAI,CAACtC,WAAW,CAACsC,IAAG,GAAI,OAAO,IAAInC,SAAQ;MAC7C;MACA,IAAI,CAACW,OAAO,CAAC;IACf,CAAC;IACD;IACA0B,qBAAqB,WAArBA,qBAAqBA,CAACC,SAAS,EAAE;MAC/B,IAAI,CAACnD,GAAE,GAAImD,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAAClB,EAAE;MAAA;MACxC,IAAI,CAAClC,MAAK,GAAIkD,SAAS,CAACG,MAAK,KAAM;MACnC,IAAI,CAACpD,QAAO,GAAI,CAACiD,SAAS,CAACG,MAAK;IAClC,CAAC;IACD,aACAC,YAAY,WAAZA,YAAYA,CAACf,GAAG,EAAE;MAAA,IAAAgB,MAAA;MAChB,IAAI,CAACtB,KAAK,CAAC;MACX,IAAMC,EAAC,GACGK,GAAG,CAACL,EAAC,IAAK,IAAI,CAACnC,GAAE;MAC3BR,SAAS,CAAC2C,EAAE,CAAC,CAACN,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC7B0B,MAAI,CAACtC,IAAG,GAAIY,QAAQ,CAACjC,IAAG;QACxB2D,MAAI,CAACnD,IAAG,GAAI,IAAG;QACfmD,MAAI,CAACpD,KAAI,GAAI,QAAO;QACpBoD,MAAI,CAAClD,MAAK,GAAI,IAAG;MACnB,CAAC;IACH,CAAC;IACD;IACAmD,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAa;MAAA,IAAAC,MAAA;MACrB,IAAI,CAACC,KAAK,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAIA,KAAK,EAAE;UACT,IAAIH,MAAI,CAACxC,IAAI,CAACiB,EAAC,KAAMtB,SAAS,EAAE;YAC9BnB,YAAY,CAACgE,MAAI,CAACxC,IAAI,CAAC,CAACW,IAAI,CAAC,UAAAC,QAAO,EAAK;cACvC,IAAIA,QAAQ,CAACgC,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAACjC,QAAQ,CAACkC,GAAG;gBAC5BN,MAAI,CAACrD,IAAG,GAAI,KAAI;gBAChBqD,MAAI,CAAClC,OAAO,CAAC;cACf,OAAO;gBACLkC,MAAI,CAACO,QAAQ,CAACnC,QAAQ,CAACkC,GAAG;cAC5B;YACF,CAAC;UACH,OAAO;YACL1E,SAAS,CAACoE,MAAI,CAACxC,IAAI,CAAC,CAACW,IAAI,CAAC,UAAAC,QAAO,EAAK;cACpC,IAAIA,QAAQ,CAACgC,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAACjC,QAAQ,CAACkC,GAAG;gBAC5BN,MAAI,CAACrD,IAAG,GAAI,KAAI;gBAChBqD,MAAI,CAAClC,OAAO,CAAC;cACf,OAAO;gBACLkC,MAAI,CAACO,QAAQ,CAACnC,QAAQ,CAACkC,GAAG;cAC5B;YACF,CAAC;UACH;QACF;MACF,CAAC;IACH,CAAC;IACD,aACAE,YAAY,WAAZA,YAAYA,CAAC1B,GAAG,EAAE;MAAA,IAAA2B,MAAA;MAChB,IAAIC,GAAE,GAAK5B,GAAG,CAACL,EAAC,IAAK,CAACK,GAAG,CAACL,EAAE,CAAC,IAAK,IAAI,CAACnC,GAAE;MAEzC,IAAI,CAACqE,QAAQ,CAAC,YAAW,GAAID,GAAE,GAAI,QAAQ,EAAE,IAAI,EAAE;QACjDE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBxD,IAAI,EAAE;MACR,CAAC,CAAC,CAACc,IAAI,CAAC,YAAW;QACjB,OAAOtC,SAAS,CAAC;UAAE,KAAK,EAAE6E;QAAI,CAAC;MACjC,CAAC,CAAC,CAACvC,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAACgC,IAAG,KAAM,GAAG,EAAE;UACzBK,MAAI,CAACJ,UAAU,CAACjC,QAAQ,CAACkC,GAAG;UAC5BG,MAAI,CAAC9D,IAAG,GAAI,KAAI;UAChB8D,MAAI,CAAC3C,OAAO,CAAC;QACf,OAAO;UACL2C,MAAI,CAACF,QAAQ,CAACnC,QAAQ,CAACkC,GAAG;QAC5B;MACF,CAAC,CAAC,CAACQ,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB;EACF;AACF", "ignoreList": []}]}