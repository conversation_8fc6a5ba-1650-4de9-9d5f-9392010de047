{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue", "mtime": 1753924830069}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["XLSX", "props", "beforeUpload", "Function", "onSuccess", "data", "loading", "excelData", "header", "results", "methods", "generateData", "_ref", "handleDrop", "e", "stopPropagation", "preventDefault", "files", "dataTransfer", "length", "$message", "error", "rawFile", "isExcel", "upload", "handleDragover", "dropEffect", "handleUpload", "$refs", "click", "handleClick", "target", "value", "readerData", "before", "_this", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "result", "workbook", "read", "type", "firstSheetName", "SheetNames", "worksheet", "Sheets", "getHeaderRow", "utils", "sheet_to_json", "readAsA<PERSON>y<PERSON><PERSON>er", "sheet", "headers", "range", "decode_range", "C", "R", "s", "r", "c", "cell", "encode_cell", "hdr", "t", "format_cell", "push", "file", "test", "name"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\UploadExcel\\index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <input ref=\"excel-upload-input\" class=\"excel-upload-input\" type=\"file\" accept=\".xlsx, .xls\" @change=\"handleClick\">\r\n    <div class=\"drop\" @drop=\"handleDrop\" @dragover=\"handleDragover\" @dragenter=\"handleDragover\">\r\n      Drop excel file here or\r\n      <el-button :loading=\"loading\" style=\"margin-left:16px;\" size=\"mini\" type=\"primary\" @click=\"handleUpload\">\r\n        Browse\r\n      </el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport XLSX from 'xlsx'\r\n\r\nexport default {\r\n  props: {\r\n    beforeUpload: Function, // eslint-disable-line\r\n    onSuccess: Function// eslint-disable-line\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      excelData: {\r\n        header: null,\r\n        results: null\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    generateData({ header, results }) {\r\n      this.excelData.header = header\r\n      this.excelData.results = results\r\n      this.onSuccess && this.onSuccess(this.excelData)\r\n    },\r\n    handleDrop(e) {\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n      if (this.loading) return\r\n      const files = e.dataTransfer.files\r\n      if (files.length !== 1) {\r\n        this.$message.error('Only support uploading one file!')\r\n        return\r\n      }\r\n      const rawFile = files[0] // only use files[0]\r\n\r\n      if (!this.isExcel(rawFile)) {\r\n        this.$message.error('Only supports upload .xlsx, .xls, .csv suffix files')\r\n        return false\r\n      }\r\n      this.upload(rawFile)\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n    },\r\n    handleDragover(e) {\r\n      e.stopPropagation()\r\n      e.preventDefault()\r\n      e.dataTransfer.dropEffect = 'copy'\r\n    },\r\n    handleUpload() {\r\n      this.$refs['excel-upload-input'].click()\r\n    },\r\n    handleClick(e) {\r\n      const files = e.target.files\r\n      const rawFile = files[0] // only use files[0]\r\n      if (!rawFile) return\r\n      this.upload(rawFile)\r\n    },\r\n    upload(rawFile) {\r\n      this.$refs['excel-upload-input'].value = null // fix can't select the same excel\r\n\r\n      if (!this.beforeUpload) {\r\n        this.readerData(rawFile)\r\n        return\r\n      }\r\n      const before = this.beforeUpload(rawFile)\r\n      if (before) {\r\n        this.readerData(rawFile)\r\n      }\r\n    },\r\n    readerData(rawFile) {\r\n      this.loading = true\r\n      return new Promise((resolve, reject) => {\r\n        const reader = new FileReader()\r\n        reader.onload = e => {\r\n          const data = e.target.result\r\n          const workbook = XLSX.read(data, { type: 'array' })\r\n          const firstSheetName = workbook.SheetNames[0]\r\n          const worksheet = workbook.Sheets[firstSheetName]\r\n          const header = this.getHeaderRow(worksheet)\r\n          const results = XLSX.utils.sheet_to_json(worksheet)\r\n          this.generateData({ header, results })\r\n          this.loading = false\r\n          resolve()\r\n        }\r\n        reader.readAsArrayBuffer(rawFile)\r\n      })\r\n    },\r\n    getHeaderRow(sheet) {\r\n      const headers = []\r\n      const range = XLSX.utils.decode_range(sheet['!ref'])\r\n      let C\r\n      const R = range.s.r\r\n      /* start in the first row */\r\n      for (C = range.s.c; C <= range.e.c; ++C) { /* walk every column in the range */\r\n        const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })]\r\n        /* find the cell in the first row */\r\n        let hdr = 'UNKNOWN ' + C // <-- replace with your desired default\r\n        if (cell && cell.t) hdr = XLSX.utils.format_cell(cell)\r\n        headers.push(hdr)\r\n      }\r\n      return headers\r\n    },\r\n    isExcel(file) {\r\n      return /\\.(xlsx|xls|csv)$/.test(file.name)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.excel-upload-input{\r\n  display: none;\r\n  z-index: -9999;\r\n}\r\n.drop{\r\n  border: 2px dashed #bbb;\r\n  width: 600px;\r\n  height: 160px;\r\n  line-height: 160px;\r\n  margin: 0 auto;\r\n  font-size: 24px;\r\n  border-radius: 5px;\r\n  text-align: center;\r\n  color: #bbb;\r\n  position: relative;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AAaA,OAAOA,IAAG,MAAO,MAAK;AAEtB,eAAe;EACbC,KAAK,EAAE;IACLC,YAAY,EAAEC,QAAQ;IAAE;IACxBC,SAAS,EAAED,QAAQ;EACrB,CAAC;EACDE,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;QACTC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE;MACX;IACF;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,YAAY,WAAZA,YAAYA,CAAAC,IAAA,EAAsB;MAAA,IAAnBJ,MAAM,GAAAI,IAAA,CAANJ,MAAM;QAAEC,OAAM,GAAAG,IAAA,CAANH,OAAM;MAC3B,IAAI,CAACF,SAAS,CAACC,MAAK,GAAIA,MAAK;MAC7B,IAAI,CAACD,SAAS,CAACE,OAAM,GAAIA,OAAM;MAC/B,IAAI,CAACL,SAAQ,IAAK,IAAI,CAACA,SAAS,CAAC,IAAI,CAACG,SAAS;IACjD,CAAC;IACDM,UAAU,WAAVA,UAAUA,CAACC,CAAC,EAAE;MACZA,CAAC,CAACC,eAAe,CAAC;MAClBD,CAAC,CAACE,cAAc,CAAC;MACjB,IAAI,IAAI,CAACV,OAAO,EAAE;MAClB,IAAMW,KAAI,GAAIH,CAAC,CAACI,YAAY,CAACD,KAAI;MACjC,IAAIA,KAAK,CAACE,MAAK,KAAM,CAAC,EAAE;QACtB,IAAI,CAACC,QAAQ,CAACC,KAAK,CAAC,kCAAkC;QACtD;MACF;MACA,IAAMC,OAAM,GAAIL,KAAK,CAAC,CAAC,GAAE;;MAEzB,IAAI,CAAC,IAAI,CAACM,OAAO,CAACD,OAAO,CAAC,EAAE;QAC1B,IAAI,CAACF,QAAQ,CAACC,KAAK,CAAC,qDAAqD;QACzE,OAAO,KAAI;MACb;MACA,IAAI,CAACG,MAAM,CAACF,OAAO;MACnBR,CAAC,CAACC,eAAe,CAAC;MAClBD,CAAC,CAACE,cAAc,CAAC;IACnB,CAAC;IACDS,cAAc,WAAdA,cAAcA,CAACX,CAAC,EAAE;MAChBA,CAAC,CAACC,eAAe,CAAC;MAClBD,CAAC,CAACE,cAAc,CAAC;MACjBF,CAAC,CAACI,YAAY,CAACQ,UAAS,GAAI,MAAK;IACnC,CAAC;IACDC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACC,KAAK,CAAC,oBAAoB,CAAC,CAACC,KAAK,CAAC;IACzC,CAAC;IACDC,WAAW,WAAXA,WAAWA,CAAChB,CAAC,EAAE;MACb,IAAMG,KAAI,GAAIH,CAAC,CAACiB,MAAM,CAACd,KAAI;MAC3B,IAAMK,OAAM,GAAIL,KAAK,CAAC,CAAC,GAAE;MACzB,IAAI,CAACK,OAAO,EAAE;MACd,IAAI,CAACE,MAAM,CAACF,OAAO;IACrB,CAAC;IACDE,MAAM,WAANA,MAAMA,CAACF,OAAO,EAAE;MACd,IAAI,CAACM,KAAK,CAAC,oBAAoB,CAAC,CAACI,KAAI,GAAI,IAAG,EAAE;;MAE9C,IAAI,CAAC,IAAI,CAAC9B,YAAY,EAAE;QACtB,IAAI,CAAC+B,UAAU,CAACX,OAAO;QACvB;MACF;MACA,IAAMY,MAAK,GAAI,IAAI,CAAChC,YAAY,CAACoB,OAAO;MACxC,IAAIY,MAAM,EAAE;QACV,IAAI,CAACD,UAAU,CAACX,OAAO;MACzB;IACF,CAAC;IACDW,UAAU,WAAVA,UAAUA,CAACX,OAAO,EAAE;MAAA,IAAAa,KAAA;MAClB,IAAI,CAAC7B,OAAM,GAAI,IAAG;MAClB,OAAO,IAAI8B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAMC,MAAK,GAAI,IAAIC,UAAU,CAAC;QAC9BD,MAAM,CAACE,MAAK,GAAI,UAAA3B,CAAA,EAAK;UACnB,IAAMT,IAAG,GAAIS,CAAC,CAACiB,MAAM,CAACW,MAAK;UAC3B,IAAMC,QAAO,GAAI3C,IAAI,CAAC4C,IAAI,CAACvC,IAAI,EAAE;YAAEwC,IAAI,EAAE;UAAQ,CAAC;UAClD,IAAMC,cAAa,GAAIH,QAAQ,CAACI,UAAU,CAAC,CAAC;UAC5C,IAAMC,SAAQ,GAAIL,QAAQ,CAACM,MAAM,CAACH,cAAc;UAChD,IAAMtC,MAAK,GAAI2B,KAAI,CAACe,YAAY,CAACF,SAAS;UAC1C,IAAMvC,OAAM,GAAIT,IAAI,CAACmD,KAAK,CAACC,aAAa,CAACJ,SAAS;UAClDb,KAAI,CAACxB,YAAY,CAAC;YAAEH,MAAM,EAANA,MAAM;YAAEC,OAAM,EAANA;UAAQ,CAAC;UACrC0B,KAAI,CAAC7B,OAAM,GAAI,KAAI;UACnB+B,OAAO,CAAC;QACV;QACAE,MAAM,CAACc,iBAAiB,CAAC/B,OAAO;MAClC,CAAC;IACH,CAAC;IACD4B,YAAY,WAAZA,YAAYA,CAACI,KAAK,EAAE;MAClB,IAAMC,OAAM,GAAI,EAAC;MACjB,IAAMC,KAAI,GAAIxD,IAAI,CAACmD,KAAK,CAACM,YAAY,CAACH,KAAK,CAAC,MAAM,CAAC;MACnD,IAAII,CAAA;MACJ,IAAMC,CAAA,GAAIH,KAAK,CAACI,CAAC,CAACC,CAAA;MAClB;MACA,KAAKH,CAAA,GAAIF,KAAK,CAACI,CAAC,CAACE,CAAC,EAAEJ,CAAA,IAAKF,KAAK,CAAC1C,CAAC,CAACgD,CAAC,EAAE,EAAEJ,CAAC,EAAE;QAAE;QACzC,IAAMK,IAAG,GAAIT,KAAK,CAACtD,IAAI,CAACmD,KAAK,CAACa,WAAW,CAAC;UAAEF,CAAC,EAAEJ,CAAC;UAAEG,CAAC,EAAEF;QAAE,CAAC,CAAC;QACzD;QACA,IAAIM,GAAE,GAAI,UAAS,GAAIP,CAAA,EAAE;QACzB,IAAIK,IAAG,IAAKA,IAAI,CAACG,CAAC,EAAED,GAAE,GAAIjE,IAAI,CAACmD,KAAK,CAACgB,WAAW,CAACJ,IAAI;QACrDR,OAAO,CAACa,IAAI,CAACH,GAAG;MAClB;MACA,OAAOV,OAAM;IACf,CAAC;IACDhC,OAAO,WAAPA,OAAOA,CAAC8C,IAAI,EAAE;MACZ,OAAO,mBAAmB,CAACC,IAAI,CAACD,IAAI,CAACE,IAAI;IAC3C;EACF;AACF", "ignoreList": []}]}