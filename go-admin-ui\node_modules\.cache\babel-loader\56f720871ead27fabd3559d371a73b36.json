{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1753924830430}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gaW1wb3J0IG9wZW5XaW5kb3cgZnJvbSAnQC91dGlscy9vcGVuLXdpbmRvdycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU29jaWFsU2lnbmluJywKICBtZXRob2RzOiB7CiAgICB3ZWNoYXRIYW5kbGVDbGljazogZnVuY3Rpb24gd2VjaGF0SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7CiAgICAgIGFsZXJ0KCdvaycpOwogICAgICAvLyB0aGlzLiRzdG9yZS5jb21taXQoJ1NFVF9BVVRIX1RZUEUnLCB0aGlyZHBhcnQpCiAgICAgIC8vIGNvbnN0IGFwcGlkID0gJ3h4eHh4JwogICAgICAvLyBjb25zdCByZWRpcmVjdF91cmkgPSBlbmNvZGVVUklDb21wb25lbnQoJ3h4eC9yZWRpcmVjdD9yZWRpcmVjdD0nICsgd2luZG93LmxvY2F0aW9uLm9yaWdpbiArICcvYXV0aC1yZWRpcmVjdCcpCiAgICAgIC8vIGNvbnN0IHVybCA9ICdodHRwczovL29wZW4ud2VpeGluLnFxLmNvbS9jb25uZWN0L3FyY29ubmVjdD9hcHBpZD0nICsgYXBwaWQgKyAnJnJlZGlyZWN0X3VyaT0nICsgcmVkaXJlY3RfdXJpICsgJyZyZXNwb25zZV90eXBlPWNvZGUmc2NvcGU9c25zYXBpX2xvZ2luI3dlY2hhdF9yZWRpcmVjdCcKICAgICAgLy8gb3BlbldpbmRvdyh1cmwsIHRoaXJkcGFydCwgNTQwLCA1NDApCiAgICB9LAogICAgdGVuY2VudEhhbmRsZUNsaWNrOiBmdW5jdGlvbiB0ZW5jZW50SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7CiAgICAgIGFsZXJ0KCdvaycpOwogICAgICAvLyB0aGlzLiRzdG9yZS5jb21taXQoJ1NFVF9BVVRIX1RZUEUnLCB0aGlyZHBhcnQpCiAgICAgIC8vIGNvbnN0IGNsaWVudF9pZCA9ICd4eHh4eCcKICAgICAgLy8gY29uc3QgcmVkaXJlY3RfdXJpID0gZW5jb2RlVVJJQ29tcG9uZW50KCd4eHgvcmVkaXJlY3Q/cmVkaXJlY3Q9JyArIHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4gKyAnL2F1dGgtcmVkaXJlY3QnKQogICAgICAvLyBjb25zdCB1cmwgPSAnaHR0cHM6Ly9ncmFwaC5xcS5jb20vb2F1dGgyLjAvYXV0aG9yaXplP3Jlc3BvbnNlX3R5cGU9Y29kZSZjbGllbnRfaWQ9JyArIGNsaWVudF9pZCArICcmcmVkaXJlY3RfdXJpPScgKyByZWRpcmVjdF91cmkKICAgICAgLy8gb3BlbldpbmRvdyh1cmwsIHRoaXJkcGFydCwgNTQwLCA1NDApCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "methods", "wechatHandleClick", "thirdpart", "alert", "tencentHandleClick"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"social-signup-container\">\r\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\r\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\r\n      WeChat\r\n    </div>\r\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\r\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\r\n      QQ\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import openWindow from '@/utils/open-window'\r\n\r\nexport default {\r\n  name: 'SocialSignin',\r\n  methods: {\r\n    wechatHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const appid = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    },\r\n    tencentHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const client_id = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .social-signup-container {\r\n    margin: 20px 0;\r\n    .sign-btn {\r\n      display: inline-block;\r\n      cursor: pointer;\r\n    }\r\n    .icon {\r\n      color: #fff;\r\n      font-size: 24px;\r\n      margin-top: 8px;\r\n    }\r\n    .wx-svg-container,\r\n    .qq-svg-container {\r\n      display: inline-block;\r\n      width: 40px;\r\n      height: 40px;\r\n      line-height: 40px;\r\n      text-align: center;\r\n      padding-top: 1px;\r\n      border-radius: 4px;\r\n      margin-bottom: 20px;\r\n      margin-right: 5px;\r\n    }\r\n    .wx-svg-container {\r\n      background-color: #24da70;\r\n    }\r\n    .qq-svg-container {\r\n      background-color: #6BA2D6;\r\n      margin-left: 50px;\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": "AAcA;;AAEA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAE;IACPC,iBAAiB,WAAjBA,iBAAiBA,CAACC,SAAS,EAAE;MAC3BC,KAAK,CAAC,IAAI;MACV;MACA;MACA;MACA;MACA;IACF,CAAC;IACDC,kBAAkB,WAAlBA,kBAAkBA,CAACF,SAAS,EAAE;MAC5BC,KAAK,CAAC,IAAI;MACV;MACA;MACA;MACA;MACA;IACF;EACF;AACF", "ignoreList": []}]}