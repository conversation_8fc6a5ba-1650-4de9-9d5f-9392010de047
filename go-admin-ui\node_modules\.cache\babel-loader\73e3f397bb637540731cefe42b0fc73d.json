{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\redirect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\redirect\\index.vue", "mtime": 1753924830468}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzJCRyb3V0ZSA9IHRoaXMuJHJvdXRlLAogICAgICBwYXJhbXMgPSBfdGhpcyQkcm91dGUucGFyYW1zLAogICAgICBxdWVyeSA9IF90aGlzJCRyb3V0ZS5xdWVyeTsKICAgIHZhciBwYXRoID0gcGFyYW1zLnBhdGg7CiAgICB0aGlzLiRyb3V0ZXIucmVwbGFjZSh7CiAgICAgIHBhdGg6ICcvJyArIHBhdGgsCiAgICAgIHF1ZXJ5OiBxdWVyeQogICAgfSk7CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaCgpOyAvLyBhdm9pZCB3YXJuaW5nIG1lc3NhZ2UKICB9Cn07"}, {"version": 3, "names": ["created", "_this$$route", "$route", "params", "query", "path", "$router", "replace", "render", "h"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\redirect\\index.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  created() {\r\n    const { params, query } = this.$route\r\n    const { path } = params\r\n    this.$router.replace({ path: '/' + path, query })\r\n  },\r\n  render: function(h) {\r\n    return h() // avoid warning message\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;AACA,eAAe;EACbA,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAAC,YAAA,GAA0B,IAAI,CAACC,MAAK;MAA5BC,MAAM,GAAAF,YAAA,CAANE,MAAM;MAAEC,KAAI,GAAAH,YAAA,CAAJG,KAAI;IACpB,IAAQC,IAAG,GAAMF,MAAK,CAAdE,IAAG;IACX,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC;MAAEF,IAAI,EAAE,GAAE,GAAIA,IAAI;MAAED,KAAI,EAAJA;IAAM,CAAC;EAClD,CAAC;EACDI,MAAM,EAAE,SAARA,MAAMA,CAAWC,CAAC,EAAE;IAClB,OAAOA,CAAC,CAAC,GAAE;EACb;AACF", "ignoreList": []}]}