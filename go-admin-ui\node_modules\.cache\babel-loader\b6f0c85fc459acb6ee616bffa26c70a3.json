{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\zipdownload.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\zipdownload.js", "mtime": 1753924830263}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "getToken", "mimeMap", "xlsx", "zip", "baseUrl", "process", "env", "VUE_APP_BASE_API", "downLoadZip", "str", "filename", "url", "method", "responseType", "headers", "then", "res", "resolveBlob", "downLoadFile", "aLink", "document", "createElement", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "mimeType", "blob", "Blob", "data", "type", "patt", "RegExp", "contentDisposition", "decodeURI", "result", "exec", "fileName", "replace", "URL", "createObjectURL", "setAttribute"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/zipdownload.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nconst mimeMap = {\r\n  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\r\n  zip: 'application/zip'\r\n}\r\n\r\nconst baseUrl = process.env.VUE_APP_BASE_API\r\nexport function downLoadZip(str, filename) {\r\n  var url = baseUrl + str\r\n  axios({\r\n    method: 'get',\r\n    url: url,\r\n    responseType: 'blob',\r\n    headers: { 'Authorization': 'Bearer ' + getToken() }\r\n  }).then(res => {\r\n    resolveBlob(res, mimeMap.zip)\r\n  })\r\n}\r\n\r\nexport function downLoadFile(str) {\r\n  var url = baseUrl + str\r\n  const aLink = document.createElement('a')\r\n  aLink.href = url\r\n  document.body.appendChild(aLink)\r\n  aLink.click()\r\n  document.body.appendChild(aLink)\r\n}\r\n/**\r\n * 解析blob响应内容并下载\r\n * @param {*} res blob响应内容\r\n * @param {String} mimeType MIME类型\r\n */\r\nexport function resolveBlob(res, mimeType) {\r\n  const aLink = document.createElement('a')\r\n  var blob = new Blob([res.data], { type: mimeType })\r\n  // //从response的headers中获取filename, 后端response.setHeader(\"Content-disposition\", \"attachment; filename=xxxx.docx\") 设置的文件名;\r\n  var patt = new RegExp('filename=([^;]+\\\\.[^\\\\.;]+);*')\r\n  var contentDisposition = decodeURI(res.headers['content-disposition'])\r\n  var result = patt.exec(contentDisposition)\r\n  var fileName = result[1]\r\n  fileName = fileName.replace(/\\\"/g, '')\r\n  aLink.href = URL.createObjectURL(blob)\r\n  aLink.setAttribute('download', fileName) // 设置下载文件名称\r\n  document.body.appendChild(aLink)\r\n  aLink.click()\r\n  document.body.appendChild(aLink)\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AAEvC,IAAMC,OAAO,GAAG;EACdC,IAAI,EAAE,mEAAmE;EACzEC,GAAG,EAAE;AACP,CAAC;AAED,IAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB;AAC5C,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACzC,IAAIC,GAAG,GAAGP,OAAO,GAAGK,GAAG;EACvBV,KAAK,CAAC;IACJa,MAAM,EAAE,KAAK;IACbD,GAAG,EAAEA,GAAG;IACRE,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAE;MAAE,eAAe,EAAE,SAAS,GAAGd,QAAQ,CAAC;IAAE;EACrD,CAAC,CAAC,CAACe,IAAI,CAAC,UAAAC,GAAG,EAAI;IACbC,WAAW,CAACD,GAAG,EAAEf,OAAO,CAACE,GAAG,CAAC;EAC/B,CAAC,CAAC;AACJ;AAEA,OAAO,SAASe,YAAYA,CAACT,GAAG,EAAE;EAChC,IAAIE,GAAG,GAAGP,OAAO,GAAGK,GAAG;EACvB,IAAMU,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACzCF,KAAK,CAACG,IAAI,GAAGX,GAAG;EAChBS,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;EAChCA,KAAK,CAACM,KAAK,CAAC,CAAC;EACbL,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASF,WAAWA,CAACD,GAAG,EAAEU,QAAQ,EAAE;EACzC,IAAMP,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACzC,IAAIM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACZ,GAAG,CAACa,IAAI,CAAC,EAAE;IAAEC,IAAI,EAAEJ;EAAS,CAAC,CAAC;EACnD;EACA,IAAIK,IAAI,GAAG,IAAIC,MAAM,CAAC,+BAA+B,CAAC;EACtD,IAAIC,kBAAkB,GAAGC,SAAS,CAAClB,GAAG,CAACF,OAAO,CAAC,qBAAqB,CAAC,CAAC;EACtE,IAAIqB,MAAM,GAAGJ,IAAI,CAACK,IAAI,CAACH,kBAAkB,CAAC;EAC1C,IAAII,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC;EACxBE,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACtCnB,KAAK,CAACG,IAAI,GAAGiB,GAAG,CAACC,eAAe,CAACb,IAAI,CAAC;EACtCR,KAAK,CAACsB,YAAY,CAAC,UAAU,EAAEJ,QAAQ,CAAC,EAAC;EACzCjB,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;EAChCA,KAAK,CAACM,KAAK,CAAC,CAAC;EACbL,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;AAClC", "ignoreList": []}]}