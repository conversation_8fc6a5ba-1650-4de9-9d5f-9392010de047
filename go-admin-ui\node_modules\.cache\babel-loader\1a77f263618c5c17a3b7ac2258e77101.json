{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1753924830210}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgaXNFeHRlcm5hbCB9IGZyb20gJ0AvdXRpbHMvdmFsaWRhdGUnOwpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIHRvOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGxpbmtQcm9wczogZnVuY3Rpb24gbGlua1Byb3BzKHVybCkgewogICAgICBpZiAoaXNFeHRlcm5hbCh1cmwpKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGlzOiAnYScsCiAgICAgICAgICBocmVmOiB1cmwsCiAgICAgICAgICB0YXJnZXQ6ICdfYmxhbmsnLAogICAgICAgICAgcmVsOiAnbm9vcGVuZXInCiAgICAgICAgfTsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIGlzOiAncm91dGVyLWxpbmsnLAogICAgICAgIHRvOiB1cmwKICAgICAgfTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["isExternal", "props", "to", "type", "String", "required", "methods", "linkProps", "url", "is", "href", "target", "rel"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Link.vue"], "sourcesContent": ["\r\n<template>\r\n  <!-- eslint-disable vue/require-component-is -->\r\n  <component v-bind=\"linkProps(to)\">\r\n    <slot />\r\n  </component>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(url) {\r\n      if (isExternal(url)) {\r\n        return {\r\n          is: 'a',\r\n          href: url,\r\n          target: '_blank',\r\n          rel: 'noopener'\r\n        }\r\n      }\r\n      return {\r\n        is: 'router-link',\r\n        to: url\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AASA,SAASA,UAAS,QAAS,kBAAiB;AAE5C,eAAe;EACbC,KAAK,EAAE;IACLC,EAAE,EAAE;MACFC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,SAAS,WAATA,SAASA,CAACC,GAAG,EAAE;MACb,IAAIR,UAAU,CAACQ,GAAG,CAAC,EAAE;QACnB,OAAO;UACLC,EAAE,EAAE,GAAG;UACPC,IAAI,EAAEF,GAAG;UACTG,MAAM,EAAE,QAAQ;UAChBC,GAAG,EAAE;QACP;MACF;MACA,OAAO;QACLH,EAAE,EAAE,aAAa;QACjBP,EAAE,EAAEM;MACN;IACF;EACF;AACF", "ignoreList": []}]}