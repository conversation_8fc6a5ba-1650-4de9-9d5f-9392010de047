{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Account.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Account.vue", "mtime": *************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgdXNlcjogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIG5hbWU6ICcnLA0KICAgICAgICAgIGVtYWlsOiAnJw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc3VibWl0KCkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICdVc2VyIGluZm9ybWF0aW9uIGhhcyBiZWVuIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5JywNCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLA0KICAgICAgICBkdXJhdGlvbjogNSAqIDEwMDANCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Account.vue"], "names": [], "mappings": ";AAeA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV;MACF;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC;IACH;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/Account.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-form>\r\n    <el-form-item label=\"Name\">\r\n      <el-input v-model.trim=\"user.name\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"Email\">\r\n      <el-input v-model.trim=\"user.email\" />\r\n    </el-form-item>\r\n    <el-form-item>\r\n      <el-button type=\"primary\" @click=\"submit\">Update</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    user: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          name: '',\r\n          email: ''\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$message({\r\n        message: 'User information has been updated successfully',\r\n        type: 'success',\r\n        duration: 5 * 1000\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}