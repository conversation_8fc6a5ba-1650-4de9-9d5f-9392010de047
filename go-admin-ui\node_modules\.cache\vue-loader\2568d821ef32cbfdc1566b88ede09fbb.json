{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue?vue&type=template&id=218e8838&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue", "mtime": 1753924830445}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/UserCard.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card style=\"margin-bottom:20px;\">\r\n    <div slot=\"header\" class=\"clearfix\">\r\n      <span>About me</span>\r\n    </div>\r\n\r\n    <div class=\"user-profile\">\r\n      <div class=\"box-center\">\r\n        <pan-thumb :image=\"user.avatar\" :height=\"'100px'\" :width=\"'100px'\" :hoverable=\"false\">\r\n          <div>Hello</div>\r\n          {{ user.role }}\r\n        </pan-thumb>\r\n      </div>\r\n      <div class=\"box-center\">\r\n        <div class=\"user-name text-center\">{{ user.name }}</div>\r\n        <div class=\"user-role text-center text-muted\">{{ user.role | uppercaseFirst }}</div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"user-bio\">\r\n      <div class=\"user-education user-bio-section\">\r\n        <div class=\"user-bio-section-header\"><svg-icon icon-class=\"education\" /><span>Education</span></div>\r\n        <div class=\"user-bio-section-body\">\r\n          <div class=\"text-muted\">\r\n            JS in Computer Science from the University of Technology\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"user-skills user-bio-section\">\r\n        <div class=\"user-bio-section-header\"><svg-icon icon-class=\"skill\" /><span>Skills</span></div>\r\n        <div class=\"user-bio-section-body\">\r\n          <div class=\"progress-item\">\r\n            <span>Vue</span>\r\n            <el-progress :percentage=\"70\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>JavaScript</span>\r\n            <el-progress :percentage=\"18\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>Css</span>\r\n            <el-progress :percentage=\"12\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>ESLint</span>\r\n            <el-progress :percentage=\"100\" status=\"success\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport PanThumb from '@/components/PanThumb'\r\n\r\nexport default {\r\n  components: { PanThumb },\r\n  props: {\r\n    user: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          name: '',\r\n          email: '',\r\n          avatar: '',\r\n          roles: ''\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box-center {\r\n  margin: 0 auto;\r\n  display: table;\r\n}\r\n\r\n.text-muted {\r\n  color: #777;\r\n}\r\n\r\n.user-profile {\r\n  .user-name {\r\n    font-weight: bold;\r\n  }\r\n\r\n  .box-center {\r\n    padding-top: 10px;\r\n  }\r\n\r\n  .user-role {\r\n    padding-top: 10px;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .box-social {\r\n    padding-top: 30px;\r\n\r\n    .el-table {\r\n      border-top: 1px solid #dfe6ec;\r\n    }\r\n  }\r\n\r\n  .user-follow {\r\n    padding-top: 20px;\r\n  }\r\n}\r\n\r\n.user-bio {\r\n  margin-top: 20px;\r\n  color: #606266;\r\n\r\n  span {\r\n    padding-left: 4px;\r\n  }\r\n\r\n  .user-bio-section {\r\n    font-size: 14px;\r\n    padding: 15px 0;\r\n\r\n    .user-bio-section-header {\r\n      border-bottom: 1px solid #dfe6ec;\r\n      padding-bottom: 10px;\r\n      margin-bottom: 10px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}