{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\ws.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\ws.js", "mtime": 1753924829941}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivolN5c0pvYuWIl+ihqApleHBvcnQgZnVuY3Rpb24gdW5Xc0xvZ291dChpZCwgZ3JvdXApIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvd3Nsb2dvdXQvJyArIGlkICsgJy8nICsgZ3JvdXAsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0="}, {"version": 3, "names": ["request", "unWsLogout", "id", "group", "url", "method"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/ws.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询SysJob列表\r\nexport function unWsLogout(id, group) {\r\n  return request({\r\n    url: '/wslogout/' + id + '/' + group,\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,EAAE,EAAEC,KAAK,EAAE;EACpC,OAAOH,OAAO,CAAC;IACbI,GAAG,EAAE,YAAY,GAAGF,EAAE,GAAG,GAAG,GAAGC,KAAK;IACpCE,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}