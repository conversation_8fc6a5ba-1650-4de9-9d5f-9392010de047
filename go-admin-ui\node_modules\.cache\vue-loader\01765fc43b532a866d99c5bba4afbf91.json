{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue", "mtime": 1753924830430}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyBpbXBvcnQgb3BlbldpbmRvdyBmcm9tICdAL3V0aWxzL29wZW4td2luZG93Jw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdTb2NpYWxTaWduaW4nLA0KICBtZXRob2RzOiB7DQogICAgd2VjaGF0SGFuZGxlQ2xpY2sodGhpcmRwYXJ0KSB7DQogICAgICBhbGVydCgnb2snKQ0KICAgICAgLy8gdGhpcy4kc3RvcmUuY29tbWl0KCdTRVRfQVVUSF9UWVBFJywgdGhpcmRwYXJ0KQ0KICAgICAgLy8gY29uc3QgYXBwaWQgPSAneHh4eHgnDQogICAgICAvLyBjb25zdCByZWRpcmVjdF91cmkgPSBlbmNvZGVVUklDb21wb25lbnQoJ3h4eC9yZWRpcmVjdD9yZWRpcmVjdD0nICsgd2luZG93LmxvY2F0aW9uLm9yaWdpbiArICcvYXV0aC1yZWRpcmVjdCcpDQogICAgICAvLyBjb25zdCB1cmwgPSAnaHR0cHM6Ly9vcGVuLndlaXhpbi5xcS5jb20vY29ubmVjdC9xcmNvbm5lY3Q/YXBwaWQ9JyArIGFwcGlkICsgJyZyZWRpcmVjdF91cmk9JyArIHJlZGlyZWN0X3VyaSArICcmcmVzcG9uc2VfdHlwZT1jb2RlJnNjb3BlPXNuc2FwaV9sb2dpbiN3ZWNoYXRfcmVkaXJlY3QnDQogICAgICAvLyBvcGVuV2luZG93KHVybCwgdGhpcmRwYXJ0LCA1NDAsIDU0MCkNCiAgICB9LA0KICAgIHRlbmNlbnRIYW5kbGVDbGljayh0aGlyZHBhcnQpIHsNCiAgICAgIGFsZXJ0KCdvaycpDQogICAgICAvLyB0aGlzLiRzdG9yZS5jb21taXQoJ1NFVF9BVVRIX1RZUEUnLCB0aGlyZHBhcnQpDQogICAgICAvLyBjb25zdCBjbGllbnRfaWQgPSAneHh4eHgnDQogICAgICAvLyBjb25zdCByZWRpcmVjdF91cmkgPSBlbmNvZGVVUklDb21wb25lbnQoJ3h4eC9yZWRpcmVjdD9yZWRpcmVjdD0nICsgd2luZG93LmxvY2F0aW9uLm9yaWdpbiArICcvYXV0aC1yZWRpcmVjdCcpDQogICAgICAvLyBjb25zdCB1cmwgPSAnaHR0cHM6Ly9ncmFwaC5xcS5jb20vb2F1dGgyLjAvYXV0aG9yaXplP3Jlc3BvbnNlX3R5cGU9Y29kZSZjbGllbnRfaWQ9JyArIGNsaWVudF9pZCArICcmcmVkaXJlY3RfdXJpPScgKyByZWRpcmVjdF91cmkNCiAgICAgIC8vIG9wZW5XaW5kb3codXJsLCB0aGlyZHBhcnQsIDU0MCwgNTQwKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\login\\components\\SocialSignin.vue"], "names": [], "mappings": ";AAcA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/login/components/SocialSignin.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"social-signup-container\">\r\n    <div class=\"sign-btn\" @click=\"wechatHandleClick('wechat')\">\r\n      <span class=\"wx-svg-container\"><svg-icon icon-class=\"wechat\" class=\"icon\" /></span>\r\n      WeChat\r\n    </div>\r\n    <div class=\"sign-btn\" @click=\"tencentHandleClick('tencent')\">\r\n      <span class=\"qq-svg-container\"><svg-icon icon-class=\"qq\" class=\"icon\" /></span>\r\n      QQ\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import openWindow from '@/utils/open-window'\r\n\r\nexport default {\r\n  name: 'SocialSignin',\r\n  methods: {\r\n    wechatHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const appid = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://open.weixin.qq.com/connect/qrconnect?appid=' + appid + '&redirect_uri=' + redirect_uri + '&response_type=code&scope=snsapi_login#wechat_redirect'\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    },\r\n    tencentHandleClick(thirdpart) {\r\n      alert('ok')\r\n      // this.$store.commit('SET_AUTH_TYPE', thirdpart)\r\n      // const client_id = 'xxxxx'\r\n      // const redirect_uri = encodeURIComponent('xxx/redirect?redirect=' + window.location.origin + '/auth-redirect')\r\n      // const url = 'https://graph.qq.com/oauth2.0/authorize?response_type=code&client_id=' + client_id + '&redirect_uri=' + redirect_uri\r\n      // openWindow(url, thirdpart, 540, 540)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .social-signup-container {\r\n    margin: 20px 0;\r\n    .sign-btn {\r\n      display: inline-block;\r\n      cursor: pointer;\r\n    }\r\n    .icon {\r\n      color: #fff;\r\n      font-size: 24px;\r\n      margin-top: 8px;\r\n    }\r\n    .wx-svg-container,\r\n    .qq-svg-container {\r\n      display: inline-block;\r\n      width: 40px;\r\n      height: 40px;\r\n      line-height: 40px;\r\n      text-align: center;\r\n      padding-top: 1px;\r\n      border-radius: 4px;\r\n      margin-bottom: 20px;\r\n      margin-right: 5px;\r\n    }\r\n    .wx-svg-container {\r\n      background-color: #24da70;\r\n    }\r\n    .qq-svg-container {\r\n      background-color: #6BA2D6;\r\n      margin-left: 50px;\r\n    }\r\n  }\r\n</style>\r\n"]}]}