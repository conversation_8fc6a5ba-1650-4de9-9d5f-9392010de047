{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PieChart.vue", "mtime": 1753924830286}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "require", "resize", "mixins", "props", "className", "type", "String", "default", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "formatter", "legend", "left", "bottom", "series", "name", "roseType", "radius", "center", "value", "animationEasing", "animationDuration"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PieChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\r\n        },\r\n        series: [\r\n          {\r\n            name: 'WEEKLY WRITE ARTICLES',\r\n            type: 'pie',\r\n            roseType: 'radius',\r\n            radius: [15, 95],\r\n            center: ['50%', '38%'],\r\n            data: [\r\n              { value: 320, name: 'Industries' },\r\n              { value: 240, name: 'Technology' },\r\n              { value: 149, name: 'Forex' },\r\n              { value: 100, name: 'Gold' },\r\n              { value: 59, name: 'Forecasts' }\r\n            ],\r\n            animationEasing: 'cubicInOut',\r\n            animationDuration: 2600\r\n          }\r\n        ]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAKA,OAAOA,OAAM,MAAO,SAAQ;AAC5BC,OAAO,CAAC,wBAAwB,GAAE;AAClC,OAAOC,MAAK,MAAO,iBAAgB;AAEnC,eAAe;EACbC,MAAM,EAAE,CAACD,MAAM,CAAC;EAChBE,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE;MACNJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,SAAS,CAAC,YAAM;MACnBD,KAAI,CAACE,SAAS,CAAC;IACjB,CAAC;EACH,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACL,KAAK,EAAE;MACf;IACF;IACA,IAAI,CAACA,KAAK,CAACM,OAAO,CAAC;IACnB,IAAI,CAACN,KAAI,GAAI,IAAG;EAClB,CAAC;EACDO,OAAO,EAAE;IACPH,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACJ,KAAI,GAAIZ,OAAO,CAACoB,IAAI,CAAC,IAAI,CAACC,GAAG,EAAE,UAAU;MAE9C,IAAI,CAACT,KAAK,CAACU,SAAS,CAAC;QACnBC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE;QACb,CAAC;QACDC,MAAM,EAAE;UACNC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,IAAI;UACZjB,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW;QACjE,CAAC;QACDkB,MAAM,EAAE,CACN;UACEC,IAAI,EAAE,uBAAuB;UAC7BxB,IAAI,EAAE,KAAK;UACXyB,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;UAChBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBtB,IAAI,EAAE,CACJ;YAAEuB,KAAK,EAAE,GAAG;YAAEJ,IAAI,EAAE;UAAa,CAAC,EAClC;YAAEI,KAAK,EAAE,GAAG;YAAEJ,IAAI,EAAE;UAAa,CAAC,EAClC;YAAEI,KAAK,EAAE,GAAG;YAAEJ,IAAI,EAAE;UAAQ,CAAC,EAC7B;YAAEI,KAAK,EAAE,GAAG;YAAEJ,IAAI,EAAE;UAAO,CAAC,EAC5B;YAAEI,KAAK,EAAE,EAAE;YAAEJ,IAAI,EAAE;UAAY,EAChC;UACDK,eAAe,EAAE,YAAY;UAC7BC,iBAAiB,EAAE;QACrB;MAEJ,CAAC;IACH;EACF;AACF", "ignoreList": []}]}