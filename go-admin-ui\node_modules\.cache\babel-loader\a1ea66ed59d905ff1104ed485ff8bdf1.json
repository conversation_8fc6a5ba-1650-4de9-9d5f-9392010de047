{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\dict\\data.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\dict\\data.js", "mtime": 1753924829925}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWtl+WFuOaVsOaNruWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdERhdGEocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvZGF0YT9kaWN0VHlwZT0nICsgcXVlcnkuZGljdFR5cGUsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LlrZflhbjmlbDmja7or6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldERhdGEoZGljdENvZGUpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvZGF0YS8nICsgZGljdENvZGUsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOagueaNruWtl+WFuOexu+Wei+afpeivouWtl+WFuOaVsOaNruS/oeaBrwpleHBvcnQgZnVuY3Rpb24gZ2V0RGljdHMoZGljdFR5cGUpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QtZGF0YS9vcHRpb24tc2VsZWN0P2RpY3RUeXBlPScgKyBkaWN0VHlwZSwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5a2X5YW45pWw5o2uCmV4cG9ydCBmdW5jdGlvbiBhZGREYXRhKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvZGF0YScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55a2X5YW45pWw5o2uCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVEYXRhKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvZGF0YS8nICsgZGF0YS5kaWN0Q29kZSwKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWtl+WFuOaVsOaNrgpleHBvcnQgZnVuY3Rpb24gZGVsRGF0YShkaWN0Q29kZSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvZGljdC9kYXRhJywKICAgIG1ldGhvZDogJ2RlbGV0ZScsCiAgICBkYXRhOiBkaWN0Q29kZQogIH0pOwp9CgovLyDlr7zlh7rlrZflhbjmlbDmja4KZXhwb3J0IGZ1bmN0aW9uIGV4cG9ydERhdGEocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvZGF0YS9leHBvcnQnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listData", "query", "url", "dictType", "method", "params", "getData", "dictCode", "getDicts", "addData", "data", "updateData", "delData", "exportData"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/dict/data.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询字典数据列表\r\nexport function listData(query) {\r\n  return request({\r\n    url: '/api/v1/dict/data?dictType=' + query.dictType,\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询字典数据详细\r\nexport function getData(dictCode) {\r\n  return request({\r\n    url: '/api/v1/dict/data/' + dictCode,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据字典类型查询字典数据信息\r\nexport function getDicts(dictType) {\r\n  return request({\r\n    url: '/api/v1/dict-data/option-select?dictType=' + dictType,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增字典数据\r\nexport function addData(data) {\r\n  return request({\r\n    url: '/api/v1/dict/data',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改字典数据\r\nexport function updateData(data) {\r\n  return request({\r\n    url: '/api/v1/dict/data/' + data.dictCode,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除字典数据\r\nexport function delData(dictCode) {\r\n  return request({\r\n    url: '/api/v1/dict/data',\r\n    method: 'delete',\r\n    data: dictCode\r\n  })\r\n}\r\n\r\n// 导出字典数据\r\nexport function exportData(query) {\r\n  return request({\r\n    url: '/api/v1/dict/data/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B,GAAGD,KAAK,CAACE,QAAQ;IACnDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,OAAOA,CAACC,QAAQ,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGK,QAAQ;IACpCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,QAAQA,CAACL,QAAQ,EAAE;EACjC,OAAOJ,OAAO,CAAC;IACbG,GAAG,EAAE,2CAA2C,GAAGC,QAAQ;IAC3DC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBE,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGQ,IAAI,CAACH,QAAQ;IACzCH,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACL,QAAQ,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBE,MAAM,EAAE,QAAQ;IAChBM,IAAI,EAAEH;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,UAAUA,CAACZ,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BE,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}