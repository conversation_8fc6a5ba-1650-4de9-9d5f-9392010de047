{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-login-log.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-login-log.js", "mtime": 1753924829929}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivolN5c0xvZ2lubG9n5YiX6KGoCmV4cG9ydCBmdW5jdGlvbiBsaXN0U3lzTG9naW5sb2cocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3N5cy1sb2dpbi1sb2cnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+iU3lzTG9naW5sb2for6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldFN5c0xvZ2lubG9nKElEKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9zeXMtbG9naW4tbG9nLycgKyBJRCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5Yig6ZmkU3lzTG9naW5sb2cKZXhwb3J0IGZ1bmN0aW9uIGRlbFN5c0xvZ2lubG9nKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3N5cy1sb2dpbi1sb2cnLAogICAgbWV0aG9kOiAnZGVsZXRlJywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listSysLoginlog", "query", "url", "method", "params", "getSysLoginlog", "ID", "delSysLoginlog", "data"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-login-log.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询SysLoginlog列表\r\nexport function listSysLoginlog(query) {\r\n  return request({\r\n    url: '/api/v1/sys-login-log',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询SysLoginlog详细\r\nexport function getSysLoginlog(ID) {\r\n  return request({\r\n    url: '/api/v1/sys-login-log/' + ID,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除SysLoginlog\r\nexport function delSysLoginlog(data) {\r\n  return request({\r\n    url: '/api/v1/sys-login-log',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB,GAAGI,EAAE;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}