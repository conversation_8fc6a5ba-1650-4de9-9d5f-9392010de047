{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\storage.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\storage.js", "mtime": 1753924830262}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF90eXBlb2YgZnJvbSAiRDovRGVza3RvcC9Hb0FkbWluL2dvLWFkbWluLXVpL25vZGVfbW9kdWxlcy8uc3RvcmUvQGJhYmVsK3J1bnRpbWVANy4yOC4yL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90eXBlb2YuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIjsKdmFyIHN0b3JhZ2UgPSB7CiAgc2V0OiBmdW5jdGlvbiBzZXQoa2V5LCB2YWx1ZSkgewogICAgaWYgKF90eXBlb2YodmFsdWUpID09PSAnb2JqZWN0JykgewogICAgICB2YWx1ZSA9IEpTT04uc3RyaW5naWZ5KHZhbHVlKTsKICAgIH0KICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGtleSwgdmFsdWUpOwogIH0sCiAgZ2V0OiBmdW5jdGlvbiBnZXQoa2V5KSB7CiAgICB2YXIgdmFsdWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShrZXkpOwogICAgaWYgKHZhbHVlICYmIHZhbHVlLmluZGV4T2YoJ3snKSAhPT0gLTEpIHsKICAgICAgdmFsdWUgPSBKU09OLnBhcnNlKHZhbHVlKTsKICAgIH0KICAgIHJldHVybiB2YWx1ZTsKICB9LAogIHJlbW92ZTogZnVuY3Rpb24gcmVtb3ZlKGtleSkgewogICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTsKICB9LAogIGNsZWFyOiBmdW5jdGlvbiBjbGVhcigpIHsKICAgIGxvY2FsU3RvcmFnZS5jbGVhcigpOwogIH0KfTsKZXhwb3J0IGRlZmF1bHQgc3RvcmFnZTs="}, {"version": 3, "names": ["storage", "set", "key", "value", "_typeof", "JSON", "stringify", "localStorage", "setItem", "get", "getItem", "indexOf", "parse", "remove", "removeItem", "clear"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/storage.js"], "sourcesContent": ["const storage = {\r\n  set(key, value) {\r\n    if (typeof value === 'object') {\r\n      value = JSON.stringify(value)\r\n    }\r\n    localStorage.setItem(key, value)\r\n  },\r\n  get(key) {\r\n    let value = localStorage.getItem(key)\r\n    if (value && value.indexOf('{') !== -1) {\r\n      value = JSON.parse(value)\r\n    }\r\n    return value\r\n  },\r\n  remove(key) {\r\n    localStorage.removeItem(key)\r\n  },\r\n  clear() {\r\n    localStorage.clear()\r\n  }\r\n}\r\n\r\nexport default storage\r\n"], "mappings": ";;;AAAA,IAAMA,OAAO,GAAG;EACdC,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACd,IAAIC,OAAA,CAAOD,KAAK,MAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGE,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC;IAC/B;IACAI,YAAY,CAACC,OAAO,CAACN,GAAG,EAAEC,KAAK,CAAC;EAClC,CAAC;EACDM,GAAG,WAAHA,GAAGA,CAACP,GAAG,EAAE;IACP,IAAIC,KAAK,GAAGI,YAAY,CAACG,OAAO,CAACR,GAAG,CAAC;IACrC,IAAIC,KAAK,IAAIA,KAAK,CAACQ,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACtCR,KAAK,GAAGE,IAAI,CAACO,KAAK,CAACT,KAAK,CAAC;IAC3B;IACA,OAAOA,KAAK;EACd,CAAC;EACDU,MAAM,WAANA,MAAMA,CAACX,GAAG,EAAE;IACVK,YAAY,CAACO,UAAU,CAACZ,GAAG,CAAC;EAC9B,CAAC;EACDa,KAAK,WAALA,KAAKA,CAAA,EAAG;IACNR,YAAY,CAACQ,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;AAED,eAAef,OAAO", "ignoreList": []}]}