{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1753924830211}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoNCmltcG9ydCB2YXJpYWJsZXMgZnJvbSAnQC9zdHlsZXMvdmFyaWFibGVzLnNjc3MnDQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAndnVleCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU2lkZWJhckxvZ28nLA0KICBwcm9wczogew0KICAgIGNvbGxhcHNlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgcmVxdWlyZWQ6IHRydWUNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycyhbDQogICAgICAnYXBwSW5mbycNCiAgICBdKSwNCiAgICB2YXJpYWJsZXMoKSB7DQogICAgICByZXR1cm4gdmFyaWFibGVzDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Logo.vue"], "names": [], "mappings": ";;AAiBA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/Sidebar/Logo.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: $store.state.settings.themeStyle === 'dark' ? variables.menuBg : variables.menuLightBg }\">\r\n    <transition name=\"sidebarLogoFade\">\r\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"appInfo.sys_app_logo\" :src=\"appInfo.sys_app_logo\" class=\"sidebar-logo\">\r\n        <h1 v-else class=\"sidebar-title\" :style=\"{ color: $store.state.settings.themeStyle === 'dark' ? variables.sidebarTitle : variables.sidebarLightTitle }\">{{ appInfo.sys_app_name }} </h1>\r\n      </router-link>\r\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"appInfo.sys_app_logo\" :src=\"appInfo.sys_app_logo\" class=\"sidebar-logo\">\r\n        <h1 class=\"sidebar-title\" :style=\"{ color: $store.state.settings.themeStyle === 'dark' ? variables.sidebarTitle : variables.sidebarLightTitle }\">{{ appInfo.sys_app_name }} </h1>\r\n      </router-link>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport variables from '@/styles/variables.scss'\r\nimport { mapGetters } from 'vuex'\r\n\r\nexport default {\r\n  name: 'SidebarLogo',\r\n  props: {\r\n    collapse: {\r\n      type: Boolean,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'appInfo'\r\n    ]),\r\n    variables() {\r\n      return variables\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sidebarLogoFade-enter-active {\r\n  transition: opacity 1.5s;\r\n}\r\n\r\n.sidebarLogoFade-enter,\r\n.sidebarLogoFade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.sidebar-logo-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 64px;\r\n  line-height: 64px;\r\n  background: #001529;\r\n  text-align: center;\r\n  overflow: hidden;\r\n\r\n  & .sidebar-logo-link {\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    & .sidebar-logo {\r\n      width: 32px;\r\n      height: 32px;\r\n      vertical-align: middle;\r\n      margin-right: 12px;\r\n      border-radius: 3px;\r\n    }\r\n\r\n    & .sidebar-title {\r\n      display: inline-block;\r\n      margin: 0;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.collapse {\r\n    .sidebar-logo {\r\n      margin-right: 0;\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}