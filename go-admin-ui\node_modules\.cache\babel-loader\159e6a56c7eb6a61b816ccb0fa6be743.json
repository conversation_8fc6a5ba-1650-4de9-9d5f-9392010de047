{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue", "mtime": 1753924830455}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["store", "VueCropper", "uploadAvatar", "components", "props", "user", "type", "Object", "data", "open", "title", "options", "img", "getters", "avatar", "autoCrop", "autoCropWidth", "autoCropHeight", "fixedBox", "previews", "methods", "editCropper", "requestUpload", "rotateLeft", "$refs", "cropper", "rotateRight", "changeScale", "num", "beforeUpload", "file", "_this", "indexOf", "msgError", "reader", "FileReader", "readAsDataURL", "onload", "result", "uploadImg", "_this2", "getCropBlob", "formData", "FormData", "append", "then", "response", "code", "process", "env", "VUE_APP_BASE_API", "msgSuccess", "msg", "clearCrop", "realTime"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userAvatar.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <img :src=\"options.img\" title=\"点击上传头像\" class=\"img-circle img-lg\" @click=\"editCropper()\">\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" :close-on-click-modal=\"false\">\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :auto-crop=\"options.autoCrop\"\r\n            :auto-crop-width=\"options.autoCropWidth\"\r\n            :auto-crop-height=\"options.autoCropHeight\"\r\n            :fixed-box=\"options.fixedBox\"\r\n            @realTime=\"realTime\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{height: '350px'}\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\">\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br>\r\n      <el-row>\r\n        <el-col :lg=\"2\" :md=\"2\">\r\n          <el-upload action=\"#\" :http-request=\"requestUpload\" :show-file-list=\"false\" :before-upload=\"beforeUpload\">\r\n            <el-button size=\"small\">\r\n              上传\r\n              <i class=\"el-icon-upload el-icon--right\" />\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 2}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-plus\" size=\"small\" @click=\"changeScale(1)\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-minus\" size=\"small\" @click=\"changeScale(-1)\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-left\" size=\"small\" @click=\"rotateLeft()\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 1, offset: 1}\" :md=\"2\">\r\n          <el-button icon=\"el-icon-refresh-right\" size=\"small\" @click=\"rotateRight()\" />\r\n        </el-col>\r\n        <el-col :lg=\"{span: 2, offset: 6}\" :md=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\">提 交</el-button>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from '@/store'\r\nimport { VueCropper } from 'vue-cropper'\r\nimport { uploadAvatar } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  props: {\r\n    // eslint-disable-next-line vue/require-default-prop\r\n    user: { type: Object }\r\n  },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 弹出层标题\r\n      title: '修改头像',\r\n      options: {\r\n        img: store.getters.avatar, // 裁剪图片的地址\r\n        autoCrop: true, // 是否默认生成截图框\r\n        autoCropWidth: 200, // 默认生成截图框宽度\r\n        autoCropHeight: 200, // 默认生成截图框高度\r\n        fixedBox: true // 固定截图框大小 不允许改变\r\n      },\r\n      previews: {}\r\n    }\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {\r\n    },\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft()\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight()\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1\r\n      this.$refs.cropper.changeScale(num)\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf('image/') === -1) {\r\n        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')\r\n      } else {\r\n        const reader = new FileReader()\r\n        reader.readAsDataURL(file)\r\n        reader.onload = () => {\r\n          this.options.img = reader.result\r\n        }\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob(data => {\r\n        const formData = new FormData()\r\n        formData.append('upload[]', data)\r\n        uploadAvatar(formData).then(response => {\r\n          if (response.code === 200) {\r\n            this.open = false\r\n            this.options.img = process.env.VUE_APP_BASE_API + response.data\r\n            this.msgSuccess(response.msg)\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n          this.$refs.cropper.clearCrop()\r\n        })\r\n      })\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAsDA,OAAOA,KAAI,MAAO,SAAQ;AAC1B,SAASC,UAAS,QAAS,aAAY;AACvC,SAASC,YAAW,QAAS,sBAAqB;AAElD,eAAe;EACbC,UAAU,EAAE;IAAEF,UAAS,EAATA;EAAW,CAAC;EAC1BG,KAAK,EAAE;IACL;IACAC,IAAI,EAAE;MAAEC,IAAI,EAAEC;IAAO;EACvB,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,IAAI,EAAE,KAAK;MACX;MACAC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE;QACPC,GAAG,EAAEZ,KAAK,CAACa,OAAO,CAACC,MAAM;QAAE;QAC3BC,QAAQ,EAAE,IAAI;QAAE;QAChBC,aAAa,EAAE,GAAG;QAAE;QACpBC,cAAc,EAAE,GAAG;QAAE;QACrBC,QAAQ,EAAE,IAAG,CAAE;MACjB,CAAC;MACDC,QAAQ,EAAE,CAAC;IACb;EACF,CAAC;EACDC,OAAO,EAAE;IACP;IACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACZ,IAAG,GAAI,IAAG;IACjB,CAAC;IACD;IACAa,aAAa,WAAbA,aAAaA,CAAA,EAAG,CAChB,CAAC;IACD;IACAC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC;IAChC,CAAC;IACD;IACAG,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACF,KAAK,CAACC,OAAO,CAACC,WAAW,CAAC;IACjC,CAAC;IACD;IACAC,WAAW,WAAXA,WAAWA,CAACC,GAAG,EAAE;MACfA,GAAE,GAAIA,GAAE,IAAK;MACb,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACE,WAAW,CAACC,GAAG;IACpC,CAAC;IACD;IACAC,YAAY,WAAZA,YAAYA,CAACC,IAAI,EAAE;MAAA,IAAAC,KAAA;MACjB,IAAID,IAAI,CAACxB,IAAI,CAAC0B,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,EAAE;QACtC,IAAI,CAACC,QAAQ,CAAC,gCAAgC;MAChD,OAAO;QACL,IAAMC,MAAK,GAAI,IAAIC,UAAU,CAAC;QAC9BD,MAAM,CAACE,aAAa,CAACN,IAAI;QACzBI,MAAM,CAACG,MAAK,GAAI,YAAM;UACpBN,KAAI,CAACpB,OAAO,CAACC,GAAE,GAAIsB,MAAM,CAACI,MAAK;QACjC;MACF;IACF,CAAC;IACD;IACAC,SAAS,WAATA,SAASA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACV,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACgB,WAAW,CAAC,UAAAjC,IAAG,EAAK;QACrC,IAAMkC,QAAO,GAAI,IAAIC,QAAQ,CAAC;QAC9BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEpC,IAAI;QAChCN,YAAY,CAACwC,QAAQ,CAAC,CAACG,IAAI,CAAC,UAAAC,QAAO,EAAK;UACtC,IAAIA,QAAQ,CAACC,IAAG,KAAM,GAAG,EAAE;YACzBP,MAAI,CAAC/B,IAAG,GAAI,KAAI;YAChB+B,MAAI,CAAC7B,OAAO,CAACC,GAAE,GAAIoC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAIJ,QAAQ,CAACtC,IAAG;YAC9DgC,MAAI,CAACW,UAAU,CAACL,QAAQ,CAACM,GAAG;UAC9B,OAAO;YACLZ,MAAI,CAACP,QAAQ,CAACa,QAAQ,CAACM,GAAG;UAC5B;UACAZ,MAAI,CAAChB,KAAK,CAACC,OAAO,CAAC4B,SAAS,CAAC;QAC/B,CAAC;MACH,CAAC;IACH,CAAC;IACD;IACAC,QAAQ,WAARA,QAAQA,CAAC9C,IAAI,EAAE;MACb,IAAI,CAACW,QAAO,GAAIX,IAAG;IACrB;EACF;AACF", "ignoreList": []}]}