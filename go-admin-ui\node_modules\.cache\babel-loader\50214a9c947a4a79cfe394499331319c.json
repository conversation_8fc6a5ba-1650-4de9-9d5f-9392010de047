{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\filters\\index.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\filters\\index.js", "mtime": 1753924830081}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["parseTime", "formatTime", "pluralize", "time", "label", "timeAgo", "between", "Date", "now", "Number", "numberF<PERSON>atter", "num", "digits", "si", "value", "symbol", "i", "length", "toFixed", "replace", "toString", "toThousandFilter", "m", "uppercaseFirst", "string", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/filters/index.js"], "sourcesContent": ["// import parseTime, formatTime and set to filter\r\nexport { parseTime, formatTime } from '@/utils'\r\n\r\n/**\r\n * Show plural label if time is plural number\r\n * @param {number} time\r\n * @param {string} label\r\n * @return {string}\r\n */\r\nfunction pluralize(time, label) {\r\n  if (time === 1) {\r\n    return time + label\r\n  }\r\n  return time + label + 's'\r\n}\r\n\r\n/**\r\n * @param {number} time\r\n */\r\nexport function timeAgo(time) {\r\n  const between = Date.now() / 1000 - Number(time)\r\n  if (between < 3600) {\r\n    return pluralize(~~(between / 60), ' minute')\r\n  } else if (between < 86400) {\r\n    return pluralize(~~(between / 3600), ' hour')\r\n  } else {\r\n    return pluralize(~~(between / 86400), ' day')\r\n  }\r\n}\r\n\r\n/**\r\n * Number formatting\r\n * like 10000 => 10k\r\n * @param {number} num\r\n * @param {number} digits\r\n */\r\nexport function numberFormatter(num, digits) {\r\n  const si = [\r\n    { value: 1E18, symbol: 'E' },\r\n    { value: 1E15, symbol: 'P' },\r\n    { value: 1E12, symbol: 'T' },\r\n    { value: 1E9, symbol: 'G' },\r\n    { value: 1E6, symbol: 'M' },\r\n    { value: 1E3, symbol: 'k' }\r\n  ]\r\n  for (let i = 0; i < si.length; i++) {\r\n    if (num >= si[i].value) {\r\n      return (num / si[i].value).toFixed(digits).replace(/\\.0+$|(\\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol\r\n    }\r\n  }\r\n  return num.toString()\r\n}\r\n\r\n/**\r\n * 10000 => \"10,000\"\r\n * @param {number} num\r\n */\r\nexport function toThousandFilter(num) {\r\n  return (+num || 0).toString().replace(/^-?\\d+/g, m => m.replace(/(?=(?!\\b)(\\d{3})+$)/g, ','))\r\n}\r\n\r\n/**\r\n * Upper case first char\r\n * @param {String} string\r\n */\r\nexport function uppercaseFirst(string) {\r\n  return string.charAt(0).toUpperCase() + string.slice(1)\r\n}\r\n"], "mappings": ";;;;;;;AAAA;AACA,SAASA,SAAS,EAAEC,UAAU,QAAQ,SAAS;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC9B,IAAID,IAAI,KAAK,CAAC,EAAE;IACd,OAAOA,IAAI,GAAGC,KAAK;EACrB;EACA,OAAOD,IAAI,GAAGC,KAAK,GAAG,GAAG;AAC3B;;AAEA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACF,IAAI,EAAE;EAC5B,IAAMG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAGC,MAAM,CAACN,IAAI,CAAC;EAChD,IAAIG,OAAO,GAAG,IAAI,EAAE;IAClB,OAAOJ,SAAS,CAAC,CAAC,EAAEI,OAAO,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC;EAC/C,CAAC,MAAM,IAAIA,OAAO,GAAG,KAAK,EAAE;IAC1B,OAAOJ,SAAS,CAAC,CAAC,EAAEI,OAAO,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC;EAC/C,CAAC,MAAM;IACL,OAAOJ,SAAS,CAAC,CAAC,EAAEI,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC;EAC/C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,eAAeA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC3C,IAAMC,EAAE,GAAG,CACT;IAAEC,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC5B;IAAED,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC3B;IAAED,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,EAC3B;IAAED,KAAK,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAI,CAAC,CAC5B;EACD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,IAAIL,GAAG,IAAIE,EAAE,CAACG,CAAC,CAAC,CAACF,KAAK,EAAE;MACtB,OAAO,CAACH,GAAG,GAAGE,EAAE,CAACG,CAAC,CAAC,CAACF,KAAK,EAAEI,OAAO,CAACN,MAAM,CAAC,CAACO,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAGN,EAAE,CAACG,CAAC,CAAC,CAACD,MAAM;IACrG;EACF;EACA,OAAOJ,GAAG,CAACS,QAAQ,CAAC,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACV,GAAG,EAAE;EACpC,OAAO,CAAC,CAACA,GAAG,IAAI,CAAC,EAAES,QAAQ,CAAC,CAAC,CAACD,OAAO,CAAC,SAAS,EAAE,UAAAG,CAAC;IAAA,OAAIA,CAAC,CAACH,OAAO,CAAC,sBAAsB,EAAE,GAAG,CAAC;EAAA,EAAC;AAC/F;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,cAAcA,CAACC,MAAM,EAAE;EACrC,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}]}