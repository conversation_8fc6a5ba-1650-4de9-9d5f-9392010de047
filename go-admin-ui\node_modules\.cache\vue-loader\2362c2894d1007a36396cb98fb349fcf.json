{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\RankList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\RankList\\index.vue", "mtime": 1753924830057}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSYW5rTGlzdCcsDQogIHByb3BzOiB7DQogICAgdGl0bGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICcnDQogICAgfSwNCiAgICBsaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\RankList\\index.vue"], "names": [], "mappings": ";AAcA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/RankList/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"rank\">\r\n    <h4 class=\"title\">{{ title }}</h4>\r\n    <ul class=\"list\">\r\n      <li v-for=\"(item, index) in list\" :key=\"index\">\r\n        <span :class=\"index < 3 ? 'active' : null\">{{ index + 1 }}</span>\r\n        <span>{{ item.name }}</span>\r\n        <span>{{ item.total }}</span>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RankList',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    list: {\r\n      type: Array,\r\n      default: null\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .rank {\r\n    padding: 0 32px 32px 72px;\r\n    .list {\r\n      margin: 25px 0 0;\r\n      padding: 0;\r\n      list-style: none;\r\n      li {\r\n        margin-top: 16px;\r\n        span {\r\n          color: rgba(0, 0, 0, .65);\r\n          font-size: 14px;\r\n          line-height: 22px;\r\n          &:first-child {\r\n            background-color: #f5f5f5;\r\n            border-radius: 20px;\r\n            display: inline-block;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n            margin-right: 24px;\r\n            height: 20px;\r\n            line-height: 20px;\r\n            width: 20px;\r\n            text-align: center;\r\n          }\r\n          &.active {\r\n            background-color: #314659;\r\n            color: #fff;\r\n          }\r\n          &:last-child {\r\n            float: right;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .mobile .rank {\r\n    padding: 0 32px 32px 32px;\r\n  }\r\n</style>\r\n"]}]}