{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Screenfull\\index.vue", "mtime": 1753924830059}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHNjcmVlbmZ1bGwgZnJvbSAnc2NyZWVuZnVsbCc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnU2NyZWVuZnVsbCcsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzRnVsbHNjcmVlbjogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0KCk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgdGhpcy5kZXN0cm95KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgIC8vIGlmICghc2NyZWVuZnVsbC5lbmFibGVkKSB7CiAgICAgIC8vICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgIC8vICAgICBtZXNzYWdlOiAneW91IGJyb3dzZXIgY2FuIG5vdCB3b3JrJywKICAgICAgLy8gICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAvLyAgIH0pCiAgICAgIC8vICAgcmV0dXJuIGZhbHNlCiAgICAgIC8vIH0KICAgICAgc2NyZWVuZnVsbC50b2dnbGUoKTsKICAgIH0sCiAgICBjaGFuZ2U6IGZ1bmN0aW9uIGNoYW5nZSgpIHsKICAgICAgdGhpcy5pc0Z1bGxzY3JlZW4gPSBzY3JlZW5mdWxsLmlzRnVsbHNjcmVlbjsKICAgIH0sCiAgICBpbml0OiBmdW5jdGlvbiBpbml0KCkgewogICAgICBpZiAoc2NyZWVuZnVsbC5lbmFibGVkKSB7CiAgICAgICAgc2NyZWVuZnVsbC5vbignY2hhbmdlJywgdGhpcy5jaGFuZ2UpOwogICAgICB9CiAgICB9LAogICAgZGVzdHJveTogZnVuY3Rpb24gZGVzdHJveSgpIHsKICAgICAgaWYgKHNjcmVlbmZ1bGwuZW5hYmxlZCkgewogICAgICAgIHNjcmVlbmZ1bGwub2ZmKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["screenfull", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "toggle", "change", "enabled", "on", "off"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Screenfull\\index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport screenfull from 'screenfull'\r\n\r\nexport default {\r\n  name: 'Screenfull',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    click() {\r\n      // if (!screenfull.enabled) {\r\n      //   this.$message({\r\n      //     message: 'you browser can not work',\r\n      //     type: 'warning'\r\n      //   })\r\n      //   return false\r\n      // }\r\n      screenfull.toggle()\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.enabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.enabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.screenfull-svg {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  fill: #5a5e66;;\r\n  width: 20px;\r\n  height: 20px;\r\n  vertical-align: 10px;\r\n}\r\n</style>\r\n"], "mappings": "AAOA,OAAOA,UAAS,MAAO,YAAW;AAElC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE;IAChB;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,IAAI,CAAC;EACZ,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,OAAO,CAAC;EACf,CAAC;EACDC,OAAO,EAAE;IACPC,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN;MACA;MACA;MACA;MACA;MACA;MACA;MACAT,UAAU,CAACU,MAAM,CAAC;IACpB,CAAC;IACDC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAACR,YAAW,GAAIH,UAAU,CAACG,YAAW;IAC5C,CAAC;IACDE,IAAI,WAAJA,IAAIA,CAAA,EAAG;MACL,IAAIL,UAAU,CAACY,OAAO,EAAE;QACtBZ,UAAU,CAACa,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACF,MAAM;MACrC;IACF,CAAC;IACDJ,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,IAAIP,UAAU,CAACY,OAAO,EAAE;QACtBZ,UAAU,CAACc,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACH,MAAM;MACtC;IACF;EACF;AACF", "ignoreList": []}]}