{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\tagsView.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\tagsView.js", "mtime": 1753924830230}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["state", "visitedViews", "cachedViews", "mutations", "ADD_VISITED_VIEW", "view", "some", "v", "path", "push", "Object", "assign", "title", "meta", "ADD_CACHED_VIEW", "includes", "name", "noCache", "DEL_VISITED_VIEW", "_iterator", "_createForOfIteratorHelper", "entries", "_step", "s", "n", "done", "_step$value", "_slicedToArray", "value", "i", "splice", "err", "e", "f", "DEL_CACHED_VIEW", "index", "indexOf", "DEL_OTHERS_VISITED_VIEWS", "filter", "affix", "DEL_OTHERS_CACHED_VIEWS", "length", "slice", "DEL_ALL_VISITED_VIEWS", "affixTags", "tag", "DEL_ALL_CACHED_VIEWS", "UPDATE_VISITED_VIEW", "_iterator2", "_step2", "actions", "add<PERSON><PERSON><PERSON>", "_ref", "dispatch", "addVisitedView", "_ref2", "commit", "add<PERSON><PERSON>d<PERSON>iew", "_ref3", "<PERSON><PERSON><PERSON><PERSON>", "_ref4", "Promise", "resolve", "_toConsumableArray", "delVisitedView", "_ref5", "del<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref6", "delOthersViews", "_ref7", "delOthersVisitedViews", "_ref8", "delOthersCachedViews", "_ref9", "delAllViews", "_ref0", "delAllVisitedViews", "_ref1", "delAllCachedViews", "_ref10", "updateVisitedView", "_ref11", "namespaced"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/modules/tagsView.js"], "sourcesContent": ["const state = {\r\n  visitedViews: [],\r\n  cachedViews: []\r\n}\r\n\r\nconst mutations = {\r\n  ADD_VISITED_VIEW: (state, view) => {\r\n    if (state.visitedViews.some(v => v.path === view.path)) return\r\n    state.visitedViews.push(\r\n      Object.assign({}, view, {\r\n        title: view.meta.title || 'no-name'\r\n      })\r\n    )\r\n  },\r\n  ADD_CACHED_VIEW: (state, view) => {\r\n    if (state.cachedViews.includes(view.name)) return\r\n    if (!view.meta.noCache) {\r\n      state.cachedViews.push(view.name)\r\n    }\r\n  },\r\n\r\n  DEL_VISITED_VIEW: (state, view) => {\r\n    for (const [i, v] of state.visitedViews.entries()) {\r\n      if (v.path === view.path) {\r\n        state.visitedViews.splice(i, 1)\r\n        break\r\n      }\r\n    }\r\n  },\r\n  DEL_CACHED_VIEW: (state, view) => {\r\n    const index = state.cachedViews.indexOf(view.name)\r\n    index > -1 && state.cachedViews.splice(index, 1)\r\n  },\r\n\r\n  DEL_OTHERS_VISITED_VIEWS: (state, view) => {\r\n    state.visitedViews = state.visitedViews.filter(v => {\r\n      return v.meta.affix || v.path === view.path\r\n    })\r\n  },\r\n  DEL_OTHERS_CACHED_VIEWS: (state, view) => {\r\n    if (state.cachedViews.length > 0) {\r\n      const index = state.cachedViews.indexOf(view.name)\r\n      if (index > -1) {\r\n        state.cachedViews = state.cachedViews.slice(index, index + 1)\r\n      } else {\r\n      // if index = -1, there is no cached tags\r\n        state.cachedViews = []\r\n      }\r\n    }\r\n  },\r\n\r\n  DEL_ALL_VISITED_VIEWS: state => {\r\n    // keep affix tags\r\n    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)\r\n    state.visitedViews = affixTags\r\n  },\r\n  DEL_ALL_CACHED_VIEWS: state => {\r\n    state.cachedViews = []\r\n  },\r\n\r\n  UPDATE_VISITED_VIEW: (state, view) => {\r\n    for (let v of state.visitedViews) {\r\n      if (v.path === view.path) {\r\n        v = Object.assign(v, view)\r\n        break\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  addView({ dispatch }, view) {\r\n    dispatch('addVisitedView', view)\r\n    dispatch('addCachedView', view)\r\n  },\r\n  addVisitedView({ commit }, view) {\r\n    commit('ADD_VISITED_VIEW', view)\r\n  },\r\n  addCachedView({ commit }, view) {\r\n    commit('ADD_CACHED_VIEW', view)\r\n  },\r\n\r\n  delView({ dispatch, state }, view) {\r\n    return new Promise(resolve => {\r\n      dispatch('delVisitedView', view)\r\n      dispatch('delCachedView', view)\r\n      resolve({\r\n        visitedViews: [...state.visitedViews],\r\n        cachedViews: [...state.cachedViews]\r\n      })\r\n    })\r\n  },\r\n  delVisitedView({ commit, state }, view) {\r\n    return new Promise(resolve => {\r\n      commit('DEL_VISITED_VIEW', view)\r\n      resolve([...state.visitedViews])\r\n    })\r\n  },\r\n  delCachedView({ commit, state }, view) {\r\n    return new Promise(resolve => {\r\n      commit('DEL_CACHED_VIEW', view)\r\n      resolve([...state.cachedViews])\r\n    })\r\n  },\r\n\r\n  delOthersViews({ dispatch, state }, view) {\r\n    return new Promise(resolve => {\r\n      dispatch('delOthersVisitedViews', view)\r\n      dispatch('delOthersCachedViews', view)\r\n      resolve({\r\n        visitedViews: [...state.visitedViews],\r\n        cachedViews: [...state.cachedViews]\r\n      })\r\n    })\r\n  },\r\n  delOthersVisitedViews({ commit, state }, view) {\r\n    return new Promise(resolve => {\r\n      commit('DEL_OTHERS_VISITED_VIEWS', view)\r\n      resolve([...state.visitedViews])\r\n    })\r\n  },\r\n  delOthersCachedViews({ commit, state }, view) {\r\n    return new Promise(resolve => {\r\n      commit('DEL_OTHERS_CACHED_VIEWS', view)\r\n      resolve([...state.cachedViews])\r\n    })\r\n  },\r\n\r\n  delAllViews({ dispatch, state }, view) {\r\n    return new Promise(resolve => {\r\n      dispatch('delAllVisitedViews', view)\r\n      dispatch('delAllCachedViews', view)\r\n      resolve({\r\n        visitedViews: [...state.visitedViews],\r\n        cachedViews: [...state.cachedViews]\r\n      })\r\n    })\r\n  },\r\n  delAllVisitedViews({ commit, state }) {\r\n    return new Promise(resolve => {\r\n      commit('DEL_ALL_VISITED_VIEWS')\r\n      resolve([...state.visitedViews])\r\n    })\r\n  },\r\n  delAllCachedViews({ commit, state }) {\r\n    return new Promise(resolve => {\r\n      commit('DEL_ALL_CACHED_VIEWS')\r\n      resolve([...state.cachedViews])\r\n    })\r\n  },\r\n\r\n  updateVisitedView({ commit }, view) {\r\n    commit('UPDATE_VISITED_VIEW', view)\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAMA,KAAK,GAAG;EACZC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE;AACf,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGJ,KAAK,EAAEK,IAAI,EAAK;IACjC,IAAIL,KAAK,CAACC,YAAY,CAACK,IAAI,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI;IAAA,EAAC,EAAE;IACxDR,KAAK,CAACC,YAAY,CAACQ,IAAI,CACrBC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,IAAI,EAAE;MACtBO,KAAK,EAAEP,IAAI,CAACQ,IAAI,CAACD,KAAK,IAAI;IAC5B,CAAC,CACH,CAAC;EACH,CAAC;EACDE,eAAe,EAAE,SAAjBA,eAAeA,CAAGd,KAAK,EAAEK,IAAI,EAAK;IAChC,IAAIL,KAAK,CAACE,WAAW,CAACa,QAAQ,CAACV,IAAI,CAACW,IAAI,CAAC,EAAE;IAC3C,IAAI,CAACX,IAAI,CAACQ,IAAI,CAACI,OAAO,EAAE;MACtBjB,KAAK,CAACE,WAAW,CAACO,IAAI,CAACJ,IAAI,CAACW,IAAI,CAAC;IACnC;EACF,CAAC;EAEDE,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGlB,KAAK,EAAEK,IAAI,EAAK;IAAA,IAAAc,SAAA,GAAAC,0BAAA,CACZpB,KAAK,CAACC,YAAY,CAACoB,OAAO,CAAC,CAAC;MAAAC,KAAA;IAAA;MAAjD,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA,GAAmD;QAAA,IAAAC,WAAA,GAAAC,cAAA,CAAAL,KAAA,CAAAM,KAAA;UAAvCC,CAAC,GAAAH,WAAA;UAAEnB,CAAC,GAAAmB,WAAA;QACd,IAAInB,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI,EAAE;UACxBR,KAAK,CAACC,YAAY,CAAC6B,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;UAC/B;QACF;MACF;IAAC,SAAAE,GAAA;MAAAZ,SAAA,CAAAa,CAAA,CAAAD,GAAA;IAAA;MAAAZ,SAAA,CAAAc,CAAA;IAAA;EACH,CAAC;EACDC,eAAe,EAAE,SAAjBA,eAAeA,CAAGlC,KAAK,EAAEK,IAAI,EAAK;IAChC,IAAM8B,KAAK,GAAGnC,KAAK,CAACE,WAAW,CAACkC,OAAO,CAAC/B,IAAI,CAACW,IAAI,CAAC;IAClDmB,KAAK,GAAG,CAAC,CAAC,IAAInC,KAAK,CAACE,WAAW,CAAC4B,MAAM,CAACK,KAAK,EAAE,CAAC,CAAC;EAClD,CAAC;EAEDE,wBAAwB,EAAE,SAA1BA,wBAAwBA,CAAGrC,KAAK,EAAEK,IAAI,EAAK;IACzCL,KAAK,CAACC,YAAY,GAAGD,KAAK,CAACC,YAAY,CAACqC,MAAM,CAAC,UAAA/B,CAAC,EAAI;MAClD,OAAOA,CAAC,CAACM,IAAI,CAAC0B,KAAK,IAAIhC,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI;IAC7C,CAAC,CAAC;EACJ,CAAC;EACDgC,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAGxC,KAAK,EAAEK,IAAI,EAAK;IACxC,IAAIL,KAAK,CAACE,WAAW,CAACuC,MAAM,GAAG,CAAC,EAAE;MAChC,IAAMN,KAAK,GAAGnC,KAAK,CAACE,WAAW,CAACkC,OAAO,CAAC/B,IAAI,CAACW,IAAI,CAAC;MAClD,IAAImB,KAAK,GAAG,CAAC,CAAC,EAAE;QACdnC,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACwC,KAAK,CAACP,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;MAC/D,CAAC,MAAM;QACP;QACEnC,KAAK,CAACE,WAAW,GAAG,EAAE;MACxB;IACF;EACF,CAAC;EAEDyC,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAE3C,KAAK,EAAI;IAC9B;IACA,IAAM4C,SAAS,GAAG5C,KAAK,CAACC,YAAY,CAACqC,MAAM,CAAC,UAAAO,GAAG;MAAA,OAAIA,GAAG,CAAChC,IAAI,CAAC0B,KAAK;IAAA,EAAC;IAClEvC,KAAK,CAACC,YAAY,GAAG2C,SAAS;EAChC,CAAC;EACDE,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAE9C,KAAK,EAAI;IAC7BA,KAAK,CAACE,WAAW,GAAG,EAAE;EACxB,CAAC;EAED6C,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAG/C,KAAK,EAAEK,IAAI,EAAK;IAAA,IAAA2C,UAAA,GAAA5B,0BAAA,CACtBpB,KAAK,CAACC,YAAY;MAAAgD,MAAA;IAAA;MAAhC,KAAAD,UAAA,CAAAzB,CAAA,MAAA0B,MAAA,GAAAD,UAAA,CAAAxB,CAAA,IAAAC,IAAA,GAAkC;QAAA,IAAzBlB,CAAC,GAAA0C,MAAA,CAAArB,KAAA;QACR,IAAIrB,CAAC,CAACC,IAAI,KAAKH,IAAI,CAACG,IAAI,EAAE;UACxBD,CAAC,GAAGG,MAAM,CAACC,MAAM,CAACJ,CAAC,EAAEF,IAAI,CAAC;UAC1B;QACF;MACF;IAAC,SAAA0B,GAAA;MAAAiB,UAAA,CAAAhB,CAAA,CAAAD,GAAA;IAAA;MAAAiB,UAAA,CAAAf,CAAA;IAAA;EACH;AACF,CAAC;AAED,IAAMiB,OAAO,GAAG;EACdC,OAAO,WAAPA,OAAOA,CAAAC,IAAA,EAAe/C,IAAI,EAAE;IAAA,IAAlBgD,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAChBA,QAAQ,CAAC,gBAAgB,EAAEhD,IAAI,CAAC;IAChCgD,QAAQ,CAAC,eAAe,EAAEhD,IAAI,CAAC;EACjC,CAAC;EACDiD,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAalD,IAAI,EAAE;IAAA,IAAhBmD,MAAM,GAAAD,KAAA,CAANC,MAAM;IACrBA,MAAM,CAAC,kBAAkB,EAAEnD,IAAI,CAAC;EAClC,CAAC;EACDoD,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAarD,IAAI,EAAE;IAAA,IAAhBmD,MAAM,GAAAE,KAAA,CAANF,MAAM;IACpBA,MAAM,CAAC,iBAAiB,EAAEnD,IAAI,CAAC;EACjC,CAAC;EAEDsD,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAsBvD,IAAI,EAAE;IAAA,IAAzBgD,QAAQ,GAAAO,KAAA,CAARP,QAAQ;MAAErD,KAAK,GAAA4D,KAAA,CAAL5D,KAAK;IACvB,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BT,QAAQ,CAAC,gBAAgB,EAAEhD,IAAI,CAAC;MAChCgD,QAAQ,CAAC,eAAe,EAAEhD,IAAI,CAAC;MAC/ByD,OAAO,CAAC;QACN7D,YAAY,EAAA8D,kBAAA,CAAM/D,KAAK,CAACC,YAAY,CAAC;QACrCC,WAAW,EAAA6D,kBAAA,CAAM/D,KAAK,CAACE,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD8D,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAoB5D,IAAI,EAAE;IAAA,IAAvBmD,MAAM,GAAAS,KAAA,CAANT,MAAM;MAAExD,KAAK,GAAAiE,KAAA,CAALjE,KAAK;IAC5B,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,kBAAkB,EAAEnD,IAAI,CAAC;MAChCyD,OAAO,CAAAC,kBAAA,CAAK/D,KAAK,CAACC,YAAY,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACDiE,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAoB9D,IAAI,EAAE;IAAA,IAAvBmD,MAAM,GAAAW,KAAA,CAANX,MAAM;MAAExD,KAAK,GAAAmE,KAAA,CAALnE,KAAK;IAC3B,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,iBAAiB,EAAEnD,IAAI,CAAC;MAC/ByD,OAAO,CAAAC,kBAAA,CAAK/D,KAAK,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAEDkE,cAAc,WAAdA,cAAcA,CAAAC,KAAA,EAAsBhE,IAAI,EAAE;IAAA,IAAzBgD,QAAQ,GAAAgB,KAAA,CAARhB,QAAQ;MAAErD,KAAK,GAAAqE,KAAA,CAALrE,KAAK;IAC9B,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BT,QAAQ,CAAC,uBAAuB,EAAEhD,IAAI,CAAC;MACvCgD,QAAQ,CAAC,sBAAsB,EAAEhD,IAAI,CAAC;MACtCyD,OAAO,CAAC;QACN7D,YAAY,EAAA8D,kBAAA,CAAM/D,KAAK,CAACC,YAAY,CAAC;QACrCC,WAAW,EAAA6D,kBAAA,CAAM/D,KAAK,CAACE,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDoE,qBAAqB,WAArBA,qBAAqBA,CAAAC,KAAA,EAAoBlE,IAAI,EAAE;IAAA,IAAvBmD,MAAM,GAAAe,KAAA,CAANf,MAAM;MAAExD,KAAK,GAAAuE,KAAA,CAALvE,KAAK;IACnC,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,0BAA0B,EAAEnD,IAAI,CAAC;MACxCyD,OAAO,CAAAC,kBAAA,CAAK/D,KAAK,CAACC,YAAY,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACDuE,oBAAoB,WAApBA,oBAAoBA,CAAAC,KAAA,EAAoBpE,IAAI,EAAE;IAAA,IAAvBmD,MAAM,GAAAiB,KAAA,CAANjB,MAAM;MAAExD,KAAK,GAAAyE,KAAA,CAALzE,KAAK;IAClC,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,yBAAyB,EAAEnD,IAAI,CAAC;MACvCyD,OAAO,CAAAC,kBAAA,CAAK/D,KAAK,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAEDwE,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAAsBtE,IAAI,EAAE;IAAA,IAAzBgD,QAAQ,GAAAsB,KAAA,CAARtB,QAAQ;MAAErD,KAAK,GAAA2E,KAAA,CAAL3E,KAAK;IAC3B,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BT,QAAQ,CAAC,oBAAoB,EAAEhD,IAAI,CAAC;MACpCgD,QAAQ,CAAC,mBAAmB,EAAEhD,IAAI,CAAC;MACnCyD,OAAO,CAAC;QACN7D,YAAY,EAAA8D,kBAAA,CAAM/D,KAAK,CAACC,YAAY,CAAC;QACrCC,WAAW,EAAA6D,kBAAA,CAAM/D,KAAK,CAACE,WAAW;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD0E,kBAAkB,WAAlBA,kBAAkBA,CAAAC,KAAA,EAAoB;IAAA,IAAjBrB,MAAM,GAAAqB,KAAA,CAANrB,MAAM;MAAExD,KAAK,GAAA6E,KAAA,CAAL7E,KAAK;IAChC,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,uBAAuB,CAAC;MAC/BM,OAAO,CAAAC,kBAAA,CAAK/D,KAAK,CAACC,YAAY,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;EACD6E,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAoB;IAAA,IAAjBvB,MAAM,GAAAuB,MAAA,CAANvB,MAAM;MAAExD,KAAK,GAAA+E,MAAA,CAAL/E,KAAK;IAC/B,OAAO,IAAI6D,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BN,MAAM,CAAC,sBAAsB,CAAC;MAC9BM,OAAO,CAAAC,kBAAA,CAAK/D,KAAK,CAACE,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC;EAED8E,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAa5E,IAAI,EAAE;IAAA,IAAhBmD,MAAM,GAAAyB,MAAA,CAANzB,MAAM;IACxBA,MAAM,CAAC,qBAAqB,EAAEnD,IAAI,CAAC;EACrC;AACF,CAAC;AAED,eAAe;EACb6E,UAAU,EAAE,IAAI;EAChBlF,KAAK,EAALA,KAAK;EACLG,SAAS,EAATA,SAAS;EACT+C,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}