{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-dept\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-dept\\index.vue", "mtime": 1753924830274}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiOwppbXBvcnQgeyBnZXREZXB0TGlzdCwgZ2V0RGVwdCwgZGVsRGVwdCwgYWRkRGVwdCwgdXBkYXRlRGVwdCB9IGZyb20gJ0AvYXBpL2FkbWluL3N5cy1kZXB0JzsKaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAnQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QnOwppbXBvcnQgJ0ByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTeXNEZXB0TWFuYWdlJywKICBjb21wb25lbnRzOiB7CiAgICBUcmVlc2VsZWN0OiBUcmVlc2VsZWN0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOihqOagvOagkeaVsOaNrgogICAgICBkZXB0TGlzdDogW10sCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQogICAgICBkZXB0T3B0aW9uczogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogJycsCiAgICAgIGlzRWRpdDogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g54q25oCB5pWw5o2u5a2X5YW4CiAgICAgIHN0YXR1c09wdGlvbnM6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAgIHN0YXR1czogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgcGFyZW50SWQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfkuIrnuqfpg6jpl6jkuI3og73kuLrnqbonLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgZGVwdE5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfpg6jpl6jlkI3np7DkuI3og73kuLrnqbonLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgc29ydDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+iPnOWNlemhuuW6j+S4jeiDveS4uuepuicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBsZWFkZXI6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfotJ/otKPkurrkuI3og73kuLrnqbonLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgZW1haWw6IFt7CiAgICAgICAgICB0eXBlOiAnZW1haWwnLAogICAgICAgICAgbWVzc2FnZTogIifor7fovpPlhaXmraPnoa7nmoTpgq7nrrHlnLDlnYAiLAogICAgICAgICAgdHJpZ2dlcjogWydibHVyJywgJ2NoYW5nZSddCiAgICAgICAgfV0sCiAgICAgICAgcGhvbmU6IFt7CiAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXREaWN0cygnc3lzX25vcm1hbF9kaXNhYmxlJykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgX3RoaXMuc3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lpg6jpl6jliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgZ2V0RGVwdExpc3QodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIuZGVwdExpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDovazmjaLpg6jpl6jmlbDmja7nu5PmnoQgKi9ub3JtYWxpemVyOiBmdW5jdGlvbiBub3JtYWxpemVyKG5vZGUpIHsKICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgIW5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7CiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47CiAgICAgIH0KICAgICAgcmV0dXJuIHsKICAgICAgICBpZDogbm9kZS5kZXB0SWQsCiAgICAgICAgbGFiZWw6IG5vZGUuZGVwdE5hbWUsCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4KICAgICAgfTsKICAgIH0sCiAgICAvKiog5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovZ2V0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0VHJlZXNlbGVjdChlKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICBnZXREZXB0TGlzdCgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMzLmRlcHRPcHRpb25zID0gW107CiAgICAgICAgaWYgKGUgPT09ICd1cGRhdGUnKSB7CiAgICAgICAgICB2YXIgZGVwdCA9IHsKICAgICAgICAgICAgZGVwdElkOiAwLAogICAgICAgICAgICBkZXB0TmFtZTogJ+S4u+exu+ebricsCiAgICAgICAgICAgIGNoaWxkcmVuOiBbXSwKICAgICAgICAgICAgaXNEaXNhYmxlZDogdHJ1ZQogICAgICAgICAgfTsKICAgICAgICAgIGRlcHQuY2hpbGRyZW4gPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgX3RoaXMzLmRlcHRPcHRpb25zLnB1c2goZGVwdCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHZhciBfZGVwdCA9IHsKICAgICAgICAgICAgZGVwdElkOiAwLAogICAgICAgICAgICBkZXB0TmFtZTogJ+S4u+exu+ebricsCiAgICAgICAgICAgIGNoaWxkcmVuOiBbXQogICAgICAgICAgfTsKICAgICAgICAgIF9kZXB0LmNoaWxkcmVuID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgIF90aGlzMy5kZXB0T3B0aW9ucy5wdXNoKF9kZXB0KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWtl+WFuOeKtuaAgeWtl+WFuOe/u+ivkQogICAgc3RhdHVzRm9ybWF0OiBmdW5jdGlvbiBzdGF0dXNGb3JtYXQocm93KSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdERpY3RMYWJlbCh0aGlzLnN0YXR1c09wdGlvbnMsIHBhcnNlSW50KHJvdy5zdGF0dXMpKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbDogZnVuY3Rpb24gY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsCiAgICAgICAgcGFyZW50SWQ6IHVuZGVmaW5lZCwKICAgICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLAogICAgICAgIHNvcnQ6IDEwLAogICAgICAgIGxlYWRlcjogdW5kZWZpbmVkLAogICAgICAgIHBob25lOiB1bmRlZmluZWQsCiAgICAgICAgZW1haWw6IHVuZGVmaW5lZCwKICAgICAgICBzdGF0dXM6ICcyJwogICAgICB9OwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi9oYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZChyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB0aGlzLmdldFRyZWVzZWxlY3QoJ2FkZCcpOwogICAgICBpZiAocm93ICE9PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSByb3cuZGVwdElkOwogICAgICB9CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg6YOo6ZeoJzsKICAgICAgdGhpcy5pc0VkaXQgPSBmYWxzZTsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuZ2V0VHJlZXNlbGVjdCgndXBkYXRlJyk7CiAgICAgIGdldERlcHQocm93LmRlcHRJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM0LmZvcm0uc3RhdHVzID0gU3RyaW5nKF90aGlzNC5mb3JtLnN0YXR1cyk7CiAgICAgICAgX3RoaXM0LmZvcm0uc29ydCA9IFN0cmluZyhfdGhpczQuZm9ybS5zb3J0KTsKICAgICAgICBfdGhpczQub3BlbiA9IHRydWU7CiAgICAgICAgX3RoaXM0LnRpdGxlID0gJ+S/ruaUuemDqOmXqCc7CiAgICAgICAgX3RoaXM0LmlzRWRpdCA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczUuZm9ybS5zdGF0dXMgPSBwYXJzZUludChfdGhpczUuZm9ybS5zdGF0dXMpOwogICAgICAgICAgX3RoaXM1LmZvcm0uc29ydCA9IHBhcnNlSW50KF90aGlzNS5mb3JtLnNvcnQpOwogICAgICAgICAgaWYgKF90aGlzNS5mb3JtLmRlcHRJZCAhPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHVwZGF0ZURlcHQoX3RoaXM1LmZvcm0sIF90aGlzNS5mb3JtLmRlcHRJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczUubXNnU3VjY2VzcyhyZXNwb25zZS5tc2cpOwogICAgICAgICAgICAgICAgX3RoaXM1Lm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzNS5tc2dFcnJvcihyZXNwb25zZS5tc2cpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGREZXB0KF90aGlzNS5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICAgIF90aGlzNS5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgICAgICBfdGhpczUub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgICAgX3RoaXM1LmdldExpc3QoKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXM1Lm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi9oYW5kbGVEZWxldGU6IGZ1bmN0aW9uIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHZhciBJZHMgPSByb3cuZGVwdElkICYmIFtyb3cuZGVwdElkXSB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZCN56ew5Li6IicgKyByb3cuZGVwdE5hbWUgKyAnIueahOaVsOaNrumhuT8nLCAn6K2m5ZGKJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIGRlbERlcHQoewogICAgICAgICAgJ2lkcyc6IElkcwogICAgICAgIH0pOwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIF90aGlzNi5tc2dTdWNjZXNzKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICBfdGhpczYub3BlbiA9IGZhbHNlOwogICAgICAgICAgX3RoaXM2LmdldExpc3QoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM2Lm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["getDeptList", "getDept", "delDept", "addDept", "updateDept", "Treeselect", "name", "components", "data", "loading", "deptList", "deptOptions", "title", "isEdit", "open", "statusOptions", "queryParams", "deptName", "undefined", "status", "form", "rules", "parentId", "required", "message", "trigger", "sort", "leader", "email", "type", "phone", "pattern", "created", "_this", "getList", "getDicts", "then", "response", "methods", "_this2", "normalizer", "node", "children", "length", "id", "deptId", "label", "getTreeselect", "e", "_this3", "dept", "isDisabled", "push", "statusFormat", "row", "selectDictLabel", "parseInt", "cancel", "reset", "handleQuery", "handleAdd", "handleUpdate", "_this4", "String", "submitForm", "_this5", "$refs", "validate", "valid", "code", "msgSuccess", "msg", "msgError", "handleDelete", "_this6", "Ids", "ids", "$confirm", "confirmButtonText", "cancelButtonText", "catch"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-dept\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form :inline=\"true\">\r\n          <el-form-item label=\"部门名称\">\r\n            <el-input\r\n              v-model=\"queryParams.deptName\"\r\n              placeholder=\"请输入部门名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"部门状态\" clearable size=\"small\">\r\n              <el-option\r\n                v-for=\"dict in statusOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button\r\n              class=\"filter-item\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n            >搜索</el-button>\r\n            <el-button\r\n              v-permisaction=\"['admin:sysDept:add']\"\r\n              class=\"filter-item\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n            >新增</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"deptList\"\r\n          row-key=\"deptId\"\r\n          default-expand-all\r\n          border\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n        >\r\n          <el-table-column prop=\"deptName\" label=\"部门名称\" />\r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"200\" />\r\n          <el-table-column prop=\"status\" label=\"状态\" :formatter=\"statusFormat\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.status === 1 ? 'danger' : 'success'\"\r\n                disable-transitions\r\n              >{{ statusFormat(scope.row) }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"200\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysDept:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysDept:add']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-plus\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增</el-button>\r\n              <el-button\r\n                v-if=\"scope.row.p_id != 0\"\r\n                v-permisaction=\"['admin:sysDept:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 添加或修改部门对话框 -->\r\n        <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" :close-on-click-modal=\"false\">\r\n          <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"上级部门\" prop=\"parentId\">\r\n                  <treeselect\r\n                    v-model=\"form.parentId\"\r\n                    :options=\"deptOptions\"\r\n                    :normalizer=\"normalizer\"\r\n                    :show-count=\"true\"\r\n                    placeholder=\"选择上级部门\"\r\n                    :is-disabled=\"isEdit\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n                  <el-input v-model=\"form.deptName\" placeholder=\"请输入部门名称\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n                  <el-input-number v-model=\"form.sort\" controls-position=\"right\" :min=\"0\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"负责人\" prop=\"leader\">\r\n                  <el-input v-model=\"form.leader\" placeholder=\"请输入负责人\" maxlength=\"20\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"联系电话\" prop=\"phone\">\r\n                  <el-input v-model=\"form.phone\" placeholder=\"请输入联系电话\" maxlength=\"11\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"邮箱\" prop=\"email\">\r\n                  <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"部门状态\">\r\n                  <el-radio-group v-model=\"form.status\">\r\n                    <el-radio\r\n                      v-for=\"dict in statusOptions\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.value\"\r\n                    >{{ dict.label }}</el-radio>\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n          <div slot=\"footer\" class=\"dialog-footer\">\r\n            <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n            <el-button @click=\"cancel\">取 消</el-button>\r\n          </div>\r\n        </el-dialog>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { getDeptList, getDept, delDept, addDept, updateDept } from '@/api/admin/sys-dept'\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\r\n\r\nexport default {\r\n  name: 'SysDeptManage',\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 表格树数据\r\n      deptList: [],\r\n      // 部门树选项\r\n      deptOptions: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      isEdit: false,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        deptName: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          { required: true, message: '上级部门不能为空', trigger: 'blur' }\r\n        ],\r\n        deptName: [\r\n          { required: true, message: '部门名称不能为空', trigger: 'blur' }\r\n        ],\r\n        sort: [\r\n          { required: true, message: '菜单顺序不能为空', trigger: 'blur' }\r\n        ],\r\n        leader: [\r\n          { required: true, message: '负责人不能为空', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          {\r\n            type: 'email',\r\n            message: \"'请输入正确的邮箱地址\",\r\n            trigger: ['blur', 'change']\r\n          }\r\n        ],\r\n        phone: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: '请输入正确的手机号码',\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getDicts('sys_normal_disable').then(response => {\r\n      this.statusOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询部门列表 */\r\n    getList() {\r\n      this.loading = true\r\n      getDeptList(this.queryParams).then(response => {\r\n        this.deptList = response.data\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 转换部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      }\r\n    },\r\n    /** 查询部门下拉树结构 */\r\n    getTreeselect(e) {\r\n      getDeptList().then(response => {\r\n        this.deptOptions = []\r\n\r\n        if (e === 'update') {\r\n          const dept = { deptId: 0, deptName: '主类目', children: [], isDisabled: true }\r\n          dept.children = response.data\r\n          this.deptOptions.push(dept)\r\n        } else {\r\n          const dept = { deptId: 0, deptName: '主类目', children: [] }\r\n          dept.children = response.data\r\n          this.deptOptions.push(dept)\r\n        }\r\n      })\r\n    },\r\n    // 字典状态字典翻译\r\n    statusFormat(row) {\r\n      return this.selectDictLabel(this.statusOptions, parseInt(row.status))\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        deptId: undefined,\r\n        parentId: undefined,\r\n        deptName: undefined,\r\n        sort: 10,\r\n        leader: undefined,\r\n        phone: undefined,\r\n        email: undefined,\r\n        status: '2'\r\n      }\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset()\r\n      this.getTreeselect('add')\r\n      if (row !== undefined) {\r\n        this.form.parentId = row.deptId\r\n      }\r\n      this.open = true\r\n      this.title = '添加部门'\r\n      this.isEdit = false\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.getTreeselect('update')\r\n\r\n      getDept(row.deptId).then(response => {\r\n        this.form = response.data\r\n        this.form.status = String(this.form.status)\r\n        this.form.sort = String(this.form.sort)\r\n        this.open = true\r\n        this.title = '修改部门'\r\n        this.isEdit = true\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          this.form.status = parseInt(this.form.status)\r\n          this.form.sort = parseInt(this.form.sort)\r\n          if (this.form.deptId !== undefined) {\r\n            updateDept(this.form, this.form.deptId).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addDept(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const Ids = (row.deptId && [row.deptId]) || this.ids\r\n      this.$confirm(\r\n        '是否确认删除名称为\"' + row.deptName + '\"的数据项?',\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }\r\n      )\r\n        .then(function() {\r\n          return delDept({ 'ids': Ids })\r\n        }).then((response) => {\r\n          if (response.code === 200) {\r\n            this.msgSuccess(response.msg)\r\n            this.open = false\r\n            this.getList()\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n        }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;AA+JA,SAASA,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAS,QAAS,sBAAqB;AACxF,OAAOC,UAAS,MAAO,yBAAwB;AAC/C,OAAO,iDAAgD;AAEvD,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IAAEF,UAAS,EAATA;EAAW,CAAC;EAC1BG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,QAAQ,EAAE,EAAE;MACZ;MACAC,WAAW,EAAE,EAAE;MACf;MACAC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,KAAK;MACb;MACAC,IAAI,EAAE,KAAK;MACX;MACAC,aAAa,EAAE,EAAE;MACjB;MACAC,WAAW,EAAE;QACXC,QAAQ,EAAEC,SAAS;QACnBC,MAAM,EAAED;MACV,CAAC;MACD;MACAE,IAAI,EAAE,CACN,CAAC;MACD;MACAC,KAAK,EAAE;QACLC,QAAQ,EAAE,CACR;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDR,QAAQ,EAAE,CACR;UAAEM,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDC,IAAI,EAAE,CACJ;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDE,MAAM,EAAE,CACN;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,EACvD;QACDG,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,OAAO;UACbL,OAAO,EAAE,aAAa;UACtBC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAC5B,EACD;QACDK,KAAK,EAAE,CACL;UACEC,OAAO,EAAE,8BAA8B;UACvCP,OAAO,EAAE,YAAY;UACrBC,OAAO,EAAE;QACX;MAEJ;IACF;EACF,CAAC;EACDO,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,OAAO,CAAC;IACb,IAAI,CAACC,QAAQ,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAC,UAAAC,QAAO,EAAK;MACnDJ,KAAI,CAAClB,aAAY,GAAIsB,QAAQ,CAAC7B,IAAG;IACnC,CAAC;EACH,CAAC;EACD8B,OAAO,EAAE;IACP,aACAJ,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAK,MAAA;MACR,IAAI,CAAC9B,OAAM,GAAI,IAAG;MAClBT,WAAW,CAAC,IAAI,CAACgB,WAAW,CAAC,CAACoB,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC7CE,MAAI,CAAC7B,QAAO,GAAI2B,QAAQ,CAAC7B,IAAG;QAC5B+B,MAAI,CAAC9B,OAAM,GAAI,KAAI;MACrB,CAAC;IACH,CAAC;IACD,eACA+B,UAAU,WAAVA,UAAUA,CAACC,IAAI,EAAE;MACf,IAAIA,IAAI,CAACC,QAAO,IAAK,CAACD,IAAI,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1C,OAAOF,IAAI,CAACC,QAAO;MACrB;MACA,OAAO;QACLE,EAAE,EAAEH,IAAI,CAACI,MAAM;QACfC,KAAK,EAAEL,IAAI,CAACxB,QAAQ;QACpByB,QAAQ,EAAED,IAAI,CAACC;MACjB;IACF,CAAC;IACD,gBACAK,aAAa,WAAbA,aAAaA,CAACC,CAAC,EAAE;MAAA,IAAAC,MAAA;MACfjD,WAAW,CAAC,CAAC,CAACoC,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC7BY,MAAI,CAACtC,WAAU,GAAI,EAAC;QAEpB,IAAIqC,CAAA,KAAM,QAAQ,EAAE;UAClB,IAAME,IAAG,GAAI;YAAEL,MAAM,EAAE,CAAC;YAAE5B,QAAQ,EAAE,KAAK;YAAEyB,QAAQ,EAAE,EAAE;YAAES,UAAU,EAAE;UAAK;UAC1ED,IAAI,CAACR,QAAO,GAAIL,QAAQ,CAAC7B,IAAG;UAC5ByC,MAAI,CAACtC,WAAW,CAACyC,IAAI,CAACF,IAAI;QAC5B,OAAO;UACL,IAAMA,KAAG,GAAI;YAAEL,MAAM,EAAE,CAAC;YAAE5B,QAAQ,EAAE,KAAK;YAAEyB,QAAQ,EAAE;UAAG;UACxDQ,KAAI,CAACR,QAAO,GAAIL,QAAQ,CAAC7B,IAAG;UAC5ByC,MAAI,CAACtC,WAAW,CAACyC,IAAI,CAACF,KAAI;QAC5B;MACF,CAAC;IACH,CAAC;IACD;IACAG,YAAY,WAAZA,YAAYA,CAACC,GAAG,EAAE;MAChB,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACxC,aAAa,EAAEyC,QAAQ,CAACF,GAAG,CAACnC,MAAM,CAAC;IACtE,CAAC;IACD;IACAsC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC3C,IAAG,GAAI,KAAI;MAChB,IAAI,CAAC4C,KAAK,CAAC;IACb,CAAC;IACD;IACAA,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACtC,IAAG,GAAI;QACVyB,MAAM,EAAE3B,SAAS;QACjBI,QAAQ,EAAEJ,SAAS;QACnBD,QAAQ,EAAEC,SAAS;QACnBQ,IAAI,EAAE,EAAE;QACRC,MAAM,EAAET,SAAS;QACjBY,KAAK,EAAEZ,SAAS;QAChBU,KAAK,EAAEV,SAAS;QAChBC,MAAM,EAAE;MACV;IACF,CAAC;IACD,aACAwC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACzB,OAAO,CAAC;IACf,CAAC;IACD,aACA0B,SAAS,WAATA,SAASA,CAACN,GAAG,EAAE;MACb,IAAI,CAACI,KAAK,CAAC;MACX,IAAI,CAACX,aAAa,CAAC,KAAK;MACxB,IAAIO,GAAE,KAAMpC,SAAS,EAAE;QACrB,IAAI,CAACE,IAAI,CAACE,QAAO,GAAIgC,GAAG,CAACT,MAAK;MAChC;MACA,IAAI,CAAC/B,IAAG,GAAI,IAAG;MACf,IAAI,CAACF,KAAI,GAAI,MAAK;MAClB,IAAI,CAACC,MAAK,GAAI,KAAI;IACpB,CAAC;IACD,aACAgD,YAAY,WAAZA,YAAYA,CAACP,GAAG,EAAE;MAAA,IAAAQ,MAAA;MAChB,IAAI,CAACJ,KAAK,CAAC;MACX,IAAI,CAACX,aAAa,CAAC,QAAQ;MAE3B9C,OAAO,CAACqD,GAAG,CAACT,MAAM,CAAC,CAACT,IAAI,CAAC,UAAAC,QAAO,EAAK;QACnCyB,MAAI,CAAC1C,IAAG,GAAIiB,QAAQ,CAAC7B,IAAG;QACxBsD,MAAI,CAAC1C,IAAI,CAACD,MAAK,GAAI4C,MAAM,CAACD,MAAI,CAAC1C,IAAI,CAACD,MAAM;QAC1C2C,MAAI,CAAC1C,IAAI,CAACM,IAAG,GAAIqC,MAAM,CAACD,MAAI,CAAC1C,IAAI,CAACM,IAAI;QACtCoC,MAAI,CAAChD,IAAG,GAAI,IAAG;QACfgD,MAAI,CAAClD,KAAI,GAAI,MAAK;QAClBkD,MAAI,CAACjD,MAAK,GAAI,IAAG;MACnB,CAAC;IACH,CAAC;IACD;IACAmD,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAa;MAAA,IAAAC,MAAA;MACrB,IAAI,CAACC,KAAK,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAIA,KAAK,EAAE;UACTH,MAAI,CAAC7C,IAAI,CAACD,MAAK,GAAIqC,QAAQ,CAACS,MAAI,CAAC7C,IAAI,CAACD,MAAM;UAC5C8C,MAAI,CAAC7C,IAAI,CAACM,IAAG,GAAI8B,QAAQ,CAACS,MAAI,CAAC7C,IAAI,CAACM,IAAI;UACxC,IAAIuC,MAAI,CAAC7C,IAAI,CAACyB,MAAK,KAAM3B,SAAS,EAAE;YAClCd,UAAU,CAAC6D,MAAI,CAAC7C,IAAI,EAAE6C,MAAI,CAAC7C,IAAI,CAACyB,MAAM,CAAC,CAACT,IAAI,CAAC,UAAAC,QAAO,EAAK;cACvD,IAAIA,QAAQ,CAACgC,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAACjC,QAAQ,CAACkC,GAAG;gBAC5BN,MAAI,CAACnD,IAAG,GAAI,KAAI;gBAChBmD,MAAI,CAAC/B,OAAO,CAAC;cACf,OAAO;gBACL+B,MAAI,CAACO,QAAQ,CAACnC,QAAQ,CAACkC,GAAG;cAC5B;YACF,CAAC;UACH,OAAO;YACLpE,OAAO,CAAC8D,MAAI,CAAC7C,IAAI,CAAC,CAACgB,IAAI,CAAC,UAAAC,QAAO,EAAK;cAClC,IAAIA,QAAQ,CAACgC,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAACjC,QAAQ,CAACkC,GAAG;gBAC5BN,MAAI,CAACnD,IAAG,GAAI,KAAI;gBAChBmD,MAAI,CAAC/B,OAAO,CAAC;cACf,OAAO;gBACL+B,MAAI,CAACO,QAAQ,CAACnC,QAAQ,CAACkC,GAAG;cAC5B;YACF,CAAC;UACH;QACF;MACF,CAAC;IACH,CAAC;IACD,aACAE,YAAY,WAAZA,YAAYA,CAACnB,GAAG,EAAE;MAAA,IAAAoB,MAAA;MAChB,IAAMC,GAAE,GAAKrB,GAAG,CAACT,MAAK,IAAK,CAACS,GAAG,CAACT,MAAM,CAAC,IAAK,IAAI,CAAC+B,GAAE;MACnD,IAAI,CAACC,QAAQ,CACX,YAAW,GAAIvB,GAAG,CAACrC,QAAO,GAAI,QAAQ,EACtC,IAAI,EACJ;QACE6D,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBlD,IAAI,EAAE;MACR,CACF,EACGO,IAAI,CAAC,YAAW;QACf,OAAOlC,OAAO,CAAC;UAAE,KAAK,EAAEyE;QAAI,CAAC;MAC/B,CAAC,CAAC,CAACvC,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAACgC,IAAG,KAAM,GAAG,EAAE;UACzBK,MAAI,CAACJ,UAAU,CAACjC,QAAQ,CAACkC,GAAG;UAC5BG,MAAI,CAAC5D,IAAG,GAAI,KAAI;UAChB4D,MAAI,CAACxC,OAAO,CAAC;QACf,OAAO;UACLwC,MAAI,CAACF,QAAQ,CAACnC,QAAQ,CAACkC,GAAG;QAC5B;MACF,CAAC,CAAC,CAACS,KAAK,CAAC,YAAW,CAAC,CAAC;IAC1B;EACF;AACF", "ignoreList": []}]}