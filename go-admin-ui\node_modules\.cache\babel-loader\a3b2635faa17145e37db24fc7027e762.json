{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\auth.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\auth.js", "mtime": 1753924830245}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IENvb2tpZXMgZnJvbSAnanMtY29va2llJzsKdmFyIFRva2VuS2V5ID0gJ0FkbWluLVRva2VuJzsKZXhwb3J0IGZ1bmN0aW9uIGdldFRva2VuKCkgewogIHJldHVybiBDb29raWVzLmdldChUb2tlbktleSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIHNldFRva2VuKHRva2VuKSB7CiAgcmV0dXJuIENvb2tpZXMuc2V0KFRva2VuS2V5LCB0b2tlbik7Cn0KZXhwb3J0IGZ1bmN0aW9uIHJlbW92ZVRva2VuKCkgewogIHJldHVybiBDb29raWVzLnJlbW92ZShUb2tlbktleSk7Cn0="}, {"version": 3, "names": ["Cookies", "TokenKey", "getToken", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/auth.js"], "sourcesContent": ["import Cookies from 'js-cookie'\r\n\r\nconst TokenKey = 'Admin-Token'\r\n\r\nexport function getToken() {\r\n  return Cookies.get(TokenKey)\r\n}\r\n\r\nexport function setToken(token) {\r\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token)\r\n}\r\n\r\nexport function removeToken() {\r\n  return Cookies.remove(TokenKey)\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,QAAQ,GAAG,aAAa;AAE9B,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOF,OAAO,CAACG,GAAG,CAACF,QAAQ,CAAC;AAC9B;AAEA,OAAO,SAASG,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOL,OAAO,CAACM,GAAG,CAACL,QAAQ,EAAEI,KAAK,CAAC;AACrC;AAEA,OAAO,SAASE,WAAWA,CAAA,EAAG;EAC5B,OAAOP,OAAO,CAACQ,MAAM,CAACP,QAAQ,CAAC;AACjC", "ignoreList": []}]}