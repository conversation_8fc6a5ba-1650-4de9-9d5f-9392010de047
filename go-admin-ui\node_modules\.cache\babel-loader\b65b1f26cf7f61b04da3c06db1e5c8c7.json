{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\errorLog.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\errorLog.js", "mtime": 1753924830226}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNwbGljZS5qcyI7CnZhciBzdGF0ZSA9IHsKICBsb2dzOiBbXQp9Owp2YXIgbXV0YXRpb25zID0gewogIEFERF9FUlJPUl9MT0c6IGZ1bmN0aW9uIEFERF9FUlJPUl9MT0coc3RhdGUsIGxvZykgewogICAgc3RhdGUubG9ncy5wdXNoKGxvZyk7CiAgfSwKICBDTEVBUl9FUlJPUl9MT0c6IGZ1bmN0aW9uIENMRUFSX0VSUk9SX0xPRyhzdGF0ZSkgewogICAgc3RhdGUubG9ncy5zcGxpY2UoMCk7CiAgfQp9Owp2YXIgYWN0aW9ucyA9IHsKICBhZGRFcnJvckxvZzogZnVuY3Rpb24gYWRkRXJyb3JMb2coX3JlZiwgbG9nKSB7CiAgICB2YXIgY29tbWl0ID0gX3JlZi5jb21taXQ7CiAgICBjb21taXQoJ0FERF9FUlJPUl9MT0cnLCBsb2cpOwogIH0sCiAgY2xlYXJFcnJvckxvZzogZnVuY3Rpb24gY2xlYXJFcnJvckxvZyhfcmVmMikgewogICAgdmFyIGNvbW1pdCA9IF9yZWYyLmNvbW1pdDsKICAgIGNvbW1pdCgnQ0xFQVJfRVJST1JfTE9HJyk7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZXNwYWNlZDogdHJ1ZSwKICBzdGF0ZTogc3RhdGUsCiAgbXV0YXRpb25zOiBtdXRhdGlvbnMsCiAgYWN0aW9uczogYWN0aW9ucwp9Ow=="}, {"version": 3, "names": ["state", "logs", "mutations", "ADD_ERROR_LOG", "log", "push", "CLEAR_ERROR_LOG", "splice", "actions", "addErrorLog", "_ref", "commit", "clearErrorLog", "_ref2", "namespaced"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/modules/errorLog.js"], "sourcesContent": ["const state = {\r\n  logs: []\r\n}\r\n\r\nconst mutations = {\r\n  ADD_ERROR_LOG: (state, log) => {\r\n    state.logs.push(log)\r\n  },\r\n  CLEAR_ERROR_LOG: (state) => {\r\n    state.logs.splice(0)\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  addErrorLog({ commit }, log) {\r\n    commit('ADD_ERROR_LOG', log)\r\n  },\r\n  clearErrorLog({ commit }) {\r\n    commit('CLEAR_ERROR_LOG')\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;AAAA,IAAMA,KAAK,GAAG;EACZC,IAAI,EAAE;AACR,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,aAAa,EAAE,SAAfA,aAAaA,CAAGH,KAAK,EAAEI,GAAG,EAAK;IAC7BJ,KAAK,CAACC,IAAI,CAACI,IAAI,CAACD,GAAG,CAAC;EACtB,CAAC;EACDE,eAAe,EAAE,SAAjBA,eAAeA,CAAGN,KAAK,EAAK;IAC1BA,KAAK,CAACC,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;EACtB;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,WAAW,WAAXA,WAAWA,CAAAC,IAAA,EAAaN,GAAG,EAAE;IAAA,IAAfO,MAAM,GAAAD,IAAA,CAANC,MAAM;IAClBA,MAAM,CAAC,eAAe,EAAEP,GAAG,CAAC;EAC9B,CAAC;EACDQ,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAa;IAAA,IAAVF,MAAM,GAAAE,KAAA,CAANF,MAAM;IACpBA,MAAM,CAAC,iBAAiB,CAAC;EAC3B;AACF,CAAC;AAED,eAAe;EACbG,UAAU,EAAE,IAAI;EAChBd,KAAK,EAALA,KAAK;EACLE,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}