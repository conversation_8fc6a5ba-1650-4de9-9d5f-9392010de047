{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-opera-log.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-opera-log.js", "mtime": 1753924829931}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOa4heepuuaTjeS9nOaXpeW/lwpleHBvcnQgZnVuY3Rpb24gY2xlYW5PcGVybG9nKCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvb3BlcmxvZy9jbGVhbicsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOafpeivolN5c09wZXJsb2fliJfooagKZXhwb3J0IGZ1bmN0aW9uIGxpc3RTeXNPcGVybG9nKHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9zeXMtb3BlcmEtbG9nJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOWIoOmZpFN5c09wZXJsb2cKZXhwb3J0IGZ1bmN0aW9uIGRlbFN5c09wZXJsb2coZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvc3lzLW9wZXJhLWxvZycsCiAgICBtZXRob2Q6ICdkZWxldGUnLAogICAgZGF0YTogZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "cleanOperlog", "url", "method", "listSysOperlog", "query", "params", "delSysOperlog", "data"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-opera-log.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 清空操作日志\r\nexport function cleanOperlog() {\r\n  return request({\r\n    url: '/api/v1/operlog/clean',\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 查询SysOperlog列表\r\nexport function listSysOperlog(query) {\r\n  return request({\r\n    url: '/api/v1/sys-opera-log',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 删除SysOperlog\r\nexport function delSysOperlog(data) {\r\n  return request({\r\n    url: '/api/v1/sys-opera-log',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC7B,OAAOD,OAAO,CAAC;IACbE,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOL,OAAO,CAAC;IACbE,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbG,MAAM,EAAED;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAOR,OAAO,CAAC;IACbE,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}