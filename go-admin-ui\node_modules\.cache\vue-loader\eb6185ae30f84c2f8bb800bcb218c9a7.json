{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue", "mtime": 1753924830064}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue"], "names": [], "mappings": ";AA+BA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAExC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACpB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC9D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACnE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB;QACF,CAAC;MACH;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/TopNav/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item\r\n        v-if=\"index < visibleNumber\"\r\n        :key=\"index\"\r\n        :index=\"item.path\"\r\n      ><svg-icon :icon-class=\"item.meta.icon\" />\r\n        {{ item.meta.title }}</el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu v-if=\"topMenus.length > visibleNumber\" index=\"more\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          v-if=\"index >= visibleNumber\"\r\n          :key=\"index\"\r\n          :index=\"item.path\"\r\n        ><svg-icon :icon-class=\"item.meta.icon\" />\r\n          {{ item.meta.title }}</el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from '@/router'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 是否为首次加载\r\n      isFrist: false\r\n    }\r\n  },\r\n  computed: {\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      return this.routers.map((menu) => ({\r\n        ...menu,\r\n        children: undefined\r\n      }))\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = []\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            router.children[item].parentPath = router.path\r\n          }\r\n          childrenMenus.push(router.children[item])\r\n        }\r\n      })\r\n      return constantRoutes.concat(childrenMenus)\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path\r\n      let activePath = this.routers[0].path\r\n      if (path.lastIndexOf('/') > 0) {\r\n        const tmpPath = path.substring(1, path.length)\r\n        activePath = '/' + tmpPath.substring(0, tmpPath.indexOf('/'))\r\n      } else if (path === '/index' || path === '') {\r\n        if (!this.isFrist) {\r\n          // eslint-disable-next-line vue/no-side-effects-in-computed-properties\r\n          this.isFrist = true\r\n        } else {\r\n          activePath = 'index'\r\n        }\r\n      }\r\n      this.activeRoutes(activePath)\r\n      return activePath\r\n    }\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber()\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width - 200\r\n      const elWidth = this.$el.getBoundingClientRect().width\r\n      const menuItemNodes = this.$el.children\r\n      const menuWidth = Array.from(menuItemNodes).map(\r\n        (i) => i.getBoundingClientRect().width\r\n      )\r\n      this.visibleNumber = (\r\n        parseInt(width - elWidth) / parseInt(menuWidth)\r\n      ).toFixed(0)\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      if (key.indexOf('http://') !== -1 || key.indexOf('https://') !== -1) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, '_blank')\r\n      } else {\r\n        this.activeRoutes(key)\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = []\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key === item.parentPath || (key === 'index' && item.path === '')) {\r\n            routes.push(item)\r\n          }\r\n        })\r\n      }\r\n      this.$store.commit('permission/SET_SIDEBAR_ROUTERS', routes)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  margin: 0;\r\n  border-bottom: 3px solid transparent;\r\n  color: #999093;\r\n  padding: 0 5px;\r\n  margin: 0 10px;\r\n}\r\n\r\n.el-menu--horizontal > .el-menu-item.is-active {\r\n  border-bottom: 3px solid #409eff;\r\n  color: #303133;\r\n}\r\n</style>\r\n"]}]}