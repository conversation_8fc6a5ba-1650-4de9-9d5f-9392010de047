{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue?vue&type=template&id=35f3a2c1&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue", "mtime": 1753924830064}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxlbC1tZW51DQogICAgOmRlZmF1bHQtYWN0aXZlPSJhY3RpdmVNZW51Ig0KICAgIG1vZGU9Imhvcml6b250YWwiDQogICAgQHNlbGVjdD0iaGFuZGxlU2VsZWN0Ig0KICA+DQogICAgPHRlbXBsYXRlIHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIHRvcE1lbnVzIj4NCiAgICAgIDxlbC1tZW51LWl0ZW0NCiAgICAgICAgdi1pZj0iaW5kZXggPCB2aXNpYmxlTnVtYmVyIg0KICAgICAgICA6a2V5PSJpbmRleCINCiAgICAgICAgOmluZGV4PSJpdGVtLnBhdGgiDQogICAgICA+PHN2Zy1pY29uIDppY29uLWNsYXNzPSJpdGVtLm1ldGEuaWNvbiIgLz4NCiAgICAgICAge3sgaXRlbS5tZXRhLnRpdGxlIH19PC9lbC1tZW51LWl0ZW0+DQogICAgPC90ZW1wbGF0ZT4NCg0KICAgIDwhLS0g6aG26YOo6I+c5Y2V6LaF5Ye65pWw6YeP5oqY5Y+gIC0tPg0KICAgIDxlbC1zdWJtZW51IHYtaWY9InRvcE1lbnVzLmxlbmd0aCA+IHZpc2libGVOdW1iZXIiIGluZGV4PSJtb3JlIj4NCiAgICAgIDx0ZW1wbGF0ZSBzbG90PSJ0aXRsZSI+5pu05aSa6I+c5Y2VPC90ZW1wbGF0ZT4NCiAgICAgIDx0ZW1wbGF0ZSB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiB0b3BNZW51cyI+DQogICAgICAgIDxlbC1tZW51LWl0ZW0NCiAgICAgICAgICB2LWlmPSJpbmRleCA+PSB2aXNpYmxlTnVtYmVyIg0KICAgICAgICAgIDprZXk9ImluZGV4Ig0KICAgICAgICAgIDppbmRleD0iaXRlbS5wYXRoIg0KICAgICAgICA+PHN2Zy1pY29uIDppY29uLWNsYXNzPSJpdGVtLm1ldGEuaWNvbiIgLz4NCiAgICAgICAgICB7eyBpdGVtLm1ldGEudGl0bGUgfX08L2VsLW1lbnUtaXRlbT4NCiAgICAgIDwvdGVtcGxhdGU+DQogICAgPC9lbC1zdWJtZW51Pg0KICA8L2VsLW1lbnU+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB;IACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/TopNav/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item\r\n        v-if=\"index < visibleNumber\"\r\n        :key=\"index\"\r\n        :index=\"item.path\"\r\n      ><svg-icon :icon-class=\"item.meta.icon\" />\r\n        {{ item.meta.title }}</el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu v-if=\"topMenus.length > visibleNumber\" index=\"more\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          v-if=\"index >= visibleNumber\"\r\n          :key=\"index\"\r\n          :index=\"item.path\"\r\n        ><svg-icon :icon-class=\"item.meta.icon\" />\r\n          {{ item.meta.title }}</el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from '@/router'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 是否为首次加载\r\n      isFrist: false\r\n    }\r\n  },\r\n  computed: {\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      return this.routers.map((menu) => ({\r\n        ...menu,\r\n        children: undefined\r\n      }))\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = []\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            router.children[item].parentPath = router.path\r\n          }\r\n          childrenMenus.push(router.children[item])\r\n        }\r\n      })\r\n      return constantRoutes.concat(childrenMenus)\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path\r\n      let activePath = this.routers[0].path\r\n      if (path.lastIndexOf('/') > 0) {\r\n        const tmpPath = path.substring(1, path.length)\r\n        activePath = '/' + tmpPath.substring(0, tmpPath.indexOf('/'))\r\n      } else if (path === '/index' || path === '') {\r\n        if (!this.isFrist) {\r\n          // eslint-disable-next-line vue/no-side-effects-in-computed-properties\r\n          this.isFrist = true\r\n        } else {\r\n          activePath = 'index'\r\n        }\r\n      }\r\n      this.activeRoutes(activePath)\r\n      return activePath\r\n    }\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber()\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width - 200\r\n      const elWidth = this.$el.getBoundingClientRect().width\r\n      const menuItemNodes = this.$el.children\r\n      const menuWidth = Array.from(menuItemNodes).map(\r\n        (i) => i.getBoundingClientRect().width\r\n      )\r\n      this.visibleNumber = (\r\n        parseInt(width - elWidth) / parseInt(menuWidth)\r\n      ).toFixed(0)\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      if (key.indexOf('http://') !== -1 || key.indexOf('https://') !== -1) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, '_blank')\r\n      } else {\r\n        this.activeRoutes(key)\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = []\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key === item.parentPath || (key === 'index' && item.path === '')) {\r\n            routes.push(item)\r\n          }\r\n        })\r\n      }\r\n      this.$store.commit('permission/SET_SIDEBAR_ROUTERS', routes)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  margin: 0;\r\n  border-bottom: 3px solid transparent;\r\n  color: #999093;\r\n  padding: 0 5px;\r\n  margin: 0 10px;\r\n}\r\n\r\n.el-menu--horizontal > .el-menu-item.is-active {\r\n  border-bottom: 3px solid #409eff;\r\n  color: #303133;\r\n}\r\n</style>\r\n"]}]}