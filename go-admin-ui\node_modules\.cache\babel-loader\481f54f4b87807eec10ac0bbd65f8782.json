{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-post.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-post.js", "mtime": 1753924829932}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWyl+S9jeWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFBvc3QocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3Bvc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5bKX5L2N6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXRQb3N0KHBvc3RJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcG9zdC8nICsgcG9zdElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lspfkvY0KZXhwb3J0IGZ1bmN0aW9uIGFkZFBvc3QoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcG9zdCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55bKX5L2NCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVQb3N0KGRhdGEsIGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9wb3N0LycgKyBpZCwKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWyl+S9jQpleHBvcnQgZnVuY3Rpb24gZGVsUG9zdChwb3N0SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3Bvc3QnLAogICAgbWV0aG9kOiAnZGVsZXRlJywKICAgIGRhdGE6IHBvc3RJZAogIH0pOwp9"}, {"version": 3, "names": ["request", "listPost", "query", "url", "method", "params", "getPost", "postId", "addPost", "data", "updatePost", "id", "delPost"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-post.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询岗位列表\r\nexport function listPost(query) {\r\n  return request({\r\n    url: '/api/v1/post',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询岗位详细\r\nexport function getPost(postId) {\r\n  return request({\r\n    url: '/api/v1/post/' + postId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增岗位\r\nexport function addPost(data) {\r\n  return request({\r\n    url: '/api/v1/post',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改岗位\r\nexport function updatePost(data, id) {\r\n  return request({\r\n    url: '/api/v1/post/' + id,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除岗位\r\nexport function delPost(postId) {\r\n  return request({\r\n    url: '/api/v1/post',\r\n    method: 'delete',\r\n    data: postId\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAEE,EAAE,EAAE;EACnC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGQ,EAAE;IACzBP,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEF;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}