{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\editTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\editTable.vue", "mtime": 1753924830297}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\editTable.vue"], "names": [], "mappings": ";AAqLA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACT;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1D;UACF,CAAC;QACH,CAAC;MACH,CAAC;;MAED,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC;IACH;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrE,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpE;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACzE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC9F;QACF;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACrC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrE,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpE;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC9F;QACF;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACrE,CAAC;UACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpE;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC9F;QACF;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzD;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb;UACF,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACzD,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;IACH,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACvE;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/gen/editTable.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card>\r\n    <el-tabs v-model=\"activeName\">\r\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"字段信息\" name=\"cloum\">\r\n        <el-alert\r\n          title=\"⚠️表字段中的id、create_by、update_by、created_at、updated_at、deleted_at的字段在此列表中已经隐藏\"\r\n          type=\"warning\"\r\n          show-icon\r\n        />\r\n        <el-table :data=\"columns\" :max-height=\"tableHeight\" style=\"width: 100%\">\r\n          <el-table-column fixed label=\"序号\" type=\"index\" width=\"50\" />\r\n          <el-table-column\r\n            fixed\r\n            label=\"字段列名\"\r\n            prop=\"columnName\"\r\n            width=\"150\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column fixed label=\"字段描述\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.columnComment\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"物理类型\"\r\n            prop=\"columnType\"\r\n            width=\"120\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"go类型\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.goType\">\r\n                <el-option label=\"int64\" value=\"int64\" />\r\n                <el-option label=\"string\" value=\"string\" />\r\n                <!-- <el-option label=\"int\" value=\"int\" />\r\n                <el-option label=\"bool\" value=\"bool\" /> -->\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"go属性\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.goField\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"json属性\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.jsonField\" />\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"编辑\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox v-model=\"scope.row.isInsert\" true-label=\"1\" false-label=\"0\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column label=\"编辑\" width=\"70\" :render-header=\"renderHeadeUpdate\" :cell-style=\"{'text-align':'center'}\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox v-model=\"scope.row.isEdit\" true-label=\"1\" false-label=\"0\" />\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column label=\"列表\" width=\"70\" :render-header=\"renderHeadeList\" :cell-style=\"{'text-align':'center'}\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox v-model=\"scope.row.isList\" true-label=\"1\" false-label=\"0\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询\" width=\"70\" :render-header=\"renderHeadeSearch\" :cell-style=\"{'text-align':'center'}\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox v-model=\"scope.row.isQuery\" true-label=\"1\" false-label=\"0\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询方式\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.queryType\">\r\n                <el-option label=\"=\" value=\"EQ\" />\r\n                <el-option label=\"!=\" value=\"NE\" />\r\n                <el-option label=\">\" value=\"GT\" />\r\n                <el-option label=\">=\" value=\"GTE\" />\r\n                <el-option label=\"<\" value=\"LT\" />\r\n                <el-option label=\"<=\" value=\"LTE\" />\r\n                <el-option label=\"LIKE\" value=\"LIKE\" />\r\n                <!-- <el-option label=\"BETWEEN\" value=\"BETWEEN\" /> -->\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"必填\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox v-model=\"scope.row.isRequired\" true-label=\"1\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示类型\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.htmlType\">\r\n                <el-option label=\"文本框\" value=\"input\" />\r\n                <el-option label=\"下拉框\" value=\"select\" />\r\n                <el-option label=\"单选框\" value=\"radio\" />\r\n                <!-- <el-option label=\"文件选择\" value=\"file\" /> -->\r\n                <!-- <el-option label=\"复选框\" value=\"checkbox\" />\r\n                <el-option label=\"日期控件\" value=\"datetime\" />-->\r\n                <el-option label=\"文本域\" value=\"textarea\" />\r\n\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"字典类型\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"dict in dictOptions\"\r\n                  :key=\"dict.dictType\"\r\n                  :label=\"dict.dictName\"\r\n                  :value=\"dict.dictType\"\r\n                >\r\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"关系表\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.fkTableName\" clearable filterable placeholder=\"请选择\" @change=\"handleChangeConfig(scope.row,scope.$index)\">\r\n                <el-option\r\n                  v-for=\"table in tableTree\"\r\n                  :key=\"table.tableName\"\r\n                  :label=\"table.tableName\"\r\n                  :value=\"table.tableName\"\r\n                >\r\n                  <span style=\"float: left\">{{ table.tableName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ table.tableComment }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"关系表key\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.fkLabelId\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"column in scope.row.fkCol\"\r\n                  :key=\"column.columnName\"\r\n                  :label=\"column.columnName\"\r\n                  :value=\"column.jsonField\"\r\n                >\r\n                  <span style=\"float: left\">{{ column.jsonField }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ column.columnComment }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"关系表value\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.fkLabelName\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"column in scope.row.fkCol\"\r\n                  :key=\"column.columnName\"\r\n                  :label=\"column.columnName\"\r\n                  :value=\"column.jsonField\"\r\n                >\r\n                  <span style=\"float: left\">{{ column.jsonField }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ column.columnComment }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\r\n        <gen-info-form ref=\"genInfo\" :info=\"info\" />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </el-card>\r\n</template>\r\n<script>\r\nimport { getGenTable, updateGenTable, getTableTree } from '@/api/tools/gen'\r\n// import { listTable } from '@/api/tools/gen'\r\nimport { optionselect as getDictOptionselect } from '@/api/admin/dict/type'\r\nimport basicInfoForm from './basicInfoForm'\r\nimport genInfoForm from './genInfoForm'\r\nexport default {\r\n  name: 'GenEdit',\r\n  components: {\r\n    basicInfoForm,\r\n    genInfoForm\r\n  },\r\n  data() {\r\n    return {\r\n      // 选中选项卡的 name\r\n      activeName: 'cloum',\r\n      // 表格的高度\r\n      tableHeight: document.documentElement.scrollHeight - 245 + 'px',\r\n      // 表列信息\r\n      columns: [],\r\n      tableTree: [],\r\n      // 字典信息\r\n      dictOptions: [],\r\n      // 表详细信息\r\n      info: {}\r\n    }\r\n  },\r\n\r\n  beforeCreate() {\r\n    getTableTree().then(response => {\r\n      this.tableTree = response.data\r\n      this.tableTree.unshift({ tableId: 0, className: '请选择' })\r\n    })\r\n    const { tableId } = this.$route.query\r\n    if (tableId) {\r\n      // 获取表详细信息\r\n      getGenTable(tableId).then(res => {\r\n        this.columns = res.data.list\r\n        this.info = res.data.info\r\n\r\n        this.info.isDataScope = this.info.isDataScope.toString()\r\n        this.info.isActions = this.info.isActions.toString()\r\n        this.info.isAuth = this.info.isAuth.toString()\r\n\r\n        this.columns.forEach(item => {\r\n          this.tableTree.filter(function(e) {\r\n            if (e.tableId === item.fkTableNameClass) {\r\n              item.fkCol = e.columns || [{ columnId: 0, columnName: '请选择' }]\r\n              // item.fkCol.unshift({ columnId: 0, columnName: '请选择' })\r\n            }\r\n          })\r\n        })\r\n      })\r\n\r\n      /** 查询字典下拉列表 */\r\n      getDictOptionselect().then(response => {\r\n        this.dictOptions = response.data\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    renderHeadeUpdate(h, { column, $index }) {\r\n      // h 是一个渲染函数       column 是一个对象表示当前列      $index 第几列\r\n      return h('div', [\r\n        h('span', column.label + '  ', { align: 'center', marginTop: '0px' }),\r\n        h(\r\n          'el-popover',\r\n          { props: { placement: 'top-start', width: '270', trigger: 'hover' }},\r\n          [\r\n            h('p', '是否在表单编辑时能够编辑，打√表示需要', { class: 'text-align: center; margin: 0' }),\r\n            // 生成 i 标签 ，添加icon 设置 样式，slot 必填\r\n            h('i', { class: 'el-icon-question', style: 'color:#ccc,padding-top:5px', slot: 'reference' })\r\n          ]\r\n        )\r\n      ])\r\n    },\r\n    renderHeadeList(h, { column, $index }) {\r\n      // h 是一个渲染函数       column 是一个对象表示当前列      $index 第几列\r\n      return h('div', [\r\n        h('span', column.label + '  ', { align: 'center', marginTop: '0px' }),\r\n        h(\r\n          'el-popover',\r\n          { props: { placement: 'top-start', width: '260', trigger: 'hover' }},\r\n          [\r\n            h('p', '是否在列表中展示，打√表示需要展示', { class: 'text-align: center; margin: 0' }),\r\n            h('i', { class: 'el-icon-question', style: 'color:#ccc,padding-top:5px', slot: 'reference' })\r\n          ]\r\n        )\r\n      ])\r\n    },\r\n    renderHeadeSearch(h, { column, $index }) {\r\n      return h('div', [\r\n        h('span', column.label + '  ', { align: 'center', marginTop: '0px' }),\r\n        h(\r\n          'el-popover',\r\n          { props: { placement: 'top-start', width: '270', trigger: 'hover' }},\r\n          [\r\n            h('p', '是都当做搜索条件，打√表示做为搜索条件', { class: 'text-align: center; margin: 0' }),\r\n            h('i', { class: 'el-icon-question', style: 'color:#ccc,padding-top:5px', slot: 'reference' })\r\n          ]\r\n        )\r\n      ])\r\n    },\r\n    handleChangeConfig(row, index) {\r\n      this.tableTree.filter(function(item) {\r\n        if (item.tableName === row.fkTableName) {\r\n          row.fkCol = item.columns\r\n          // row.fkCol.unshift({ columnId: 0, columnName: '请选择' })\r\n        }\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm\r\n      const genForm = this.$refs.genInfo.$refs.genInfoForm\r\n\r\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\r\n        const validateResult = res.every(item => !!item)\r\n        if (validateResult) {\r\n          const genTable = Object.assign({}, basicForm.model, genForm.model)\r\n          genTable.columns = this.columns\r\n          genTable.params = {\r\n            treeCode: genTable.treeCode,\r\n            treeName: genTable.treeName,\r\n            treeParentCode: genTable.treeParentCode\r\n          }\r\n          genTable.isDataScope = JSON.parse(genTable.isDataScope)\r\n          genTable.isActions = JSON.parse(genTable.isActions)\r\n          genTable.isAuth = JSON.parse(genTable.isAuth)\r\n          updateGenTable(genTable).then(res => {\r\n            this.msgSuccess(res.msg)\r\n            if (res.code === 200) {\r\n              this.close()\r\n            }\r\n          })\r\n        } else {\r\n          this.msgError('表单校验未通过，请重新检查提交内容')\r\n        }\r\n      })\r\n    },\r\n    getTables() {\r\n      getTableTree().then(response => {\r\n        this.tableTree = response.data\r\n        this.tableTree.unshift({ tableId: 0, className: '请选择' })\r\n      })\r\n    },\r\n    getTablesCol(tableName) {\r\n      return this.tableTree.filter(function(item) {\r\n        if (item.tableName === tableName) {\r\n          return item.columns\r\n        }\r\n      })\r\n    },\r\n    getFormPromise(form) {\r\n      return new Promise(resolve => {\r\n        form.validate(res => {\r\n          resolve(res)\r\n        })\r\n      })\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      this.$store.dispatch('tagsView/delView', this.$route)\r\n      this.$router.push({ path: '/dev-tools/gen', query: { t: Date.now() }})\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}