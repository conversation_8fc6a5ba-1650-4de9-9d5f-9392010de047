{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\tools\\gen.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\tools\\gen.js", "mtime": 1753924829940}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "listTable", "query", "url", "method", "params", "listDbTable", "getGenTable", "tableId", "getGenTableInfo", "tablename", "updateGenTable", "data", "importTable", "previewTable", "delTable", "toProjectTable", "apiToFile", "toProjectTableCheckRole", "ischeckrole", "toDBTable", "getTableTree"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/tools/gen.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询生成表数据\r\nexport function listTable(query) {\r\n  return request({\r\n    url: '/api/v1/sys/tables/page',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n// 查询db数据库列表\r\nexport function listDbTable(query) {\r\n  return request({\r\n    url: '/api/v1/db/tables/page',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询表详细信息\r\nexport function getGenTable(tableId) {\r\n  return request({\r\n    url: '/api/v1/sys/tables/info/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getGenTableInfo(tablename) {\r\n  return request({\r\n    url: '/api/v1/sys/tables?tableName=' + tablename,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改代码生成信息\r\nexport function updateGenTable(data) {\r\n  return request({\r\n    url: '/api/v1/sys/tables/info',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 导入表\r\nexport function importTable(data) {\r\n  return request({\r\n    url: '/api/v1/sys/tables/info',\r\n    method: 'post',\r\n    params: data\r\n  })\r\n}\r\n// 预览生成代码\r\nexport function previewTable(tableId) {\r\n  return request({\r\n    url: '/api/v1/gen/preview/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n// 删除表数据\r\nexport function delTable(tableId) {\r\n  return request({\r\n    url: '/api/v1/sys/tables/info/' + tableId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 生成代码到项目\r\nexport function toProjectTable(tableId) {\r\n  return request({\r\n    url: '/api/v1/gen/toproject/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 生成接口数据到迁移脚本\r\nexport function apiToFile(tableId) {\r\n  return request({\r\n    url: '/api/v1/gen/apitofile/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function toProjectTableCheckRole(tableId, ischeckrole) {\r\n  return request({\r\n    url: '/api/v1/gen/toproject/' + tableId + '?ischeckrole=' + ischeckrole,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 生成菜单到数据库\r\nexport function toDBTable(tableId) {\r\n  return request({\r\n    url: '/api/v1/gen/todb/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getTableTree() {\r\n  return request({\r\n    url: '/api/v1/gen/tabletree',\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASI,WAAWA,CAACJ,KAAK,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B,GAAGK,OAAO;IACzCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASK,eAAeA,CAACC,SAAS,EAAE;EACzC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,+BAA+B,GAAGO,SAAS;IAChDN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEO;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASE,YAAYA,CAACN,OAAO,EAAE;EACpC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB,GAAGK,OAAO;IACrCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASW,QAAQA,CAACP,OAAO,EAAE;EAChC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B,GAAGK,OAAO;IACzCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,cAAcA,CAACR,OAAO,EAAE;EACtC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB,GAAGK,OAAO;IACvCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASa,SAASA,CAACT,OAAO,EAAE;EACjC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB,GAAGK,OAAO;IACvCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASc,uBAAuBA,CAACV,OAAO,EAAEW,WAAW,EAAE;EAC5D,OAAOnB,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB,GAAGK,OAAO,GAAG,eAAe,GAAGW,WAAW;IACvEf,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASgB,SAASA,CAACZ,OAAO,EAAE;EACjC,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB,GAAGK,OAAO;IAClCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASiB,YAAYA,CAAA,EAAG;EAC7B,OAAOrB,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}