{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue?vue&type=template&id=3f2df4d6&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue", "mtime": 1753924830023}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImNlbGwiPg0KICAgIDxkaXYgY2xhc3M9ImNlbGwtaW5uZXIiIDpjbGFzcz0iIGJvcmRlciA/ICdib3JkZXInIDogJycgIj4NCiAgICAgIDxkaXYgY2xhc3M9ImNlbGwtaXRlbSI+DQogICAgICAgIDxkaXYgY2xhc3M9ImNlbGwtaXRlbS1sYWJlbCI+DQogICAgICAgICAgPHNwYW4gdi1pZj0ibGFiZWwiPg0KICAgICAgICAgICAge3sgbGFiZWwgfX0NCiAgICAgICAgICA8L3NwYW4+DQogICAgICAgICAgPHNwYW4gdi1lbHNlPjxzbG90IG5hbWU9ImxhYmVsIiAvPg0KICAgICAgICAgIDwvc3Bhbj48L2Rpdj4NCiAgICAgICAgPGRpdiBjbGFzcz0iY2VsbC1pdGVtLXZhbHVlIj4NCiAgICAgICAgICA8c3BhbiB2LWlmPSJ2YWx1ZSI+DQogICAgICAgICAgICB7eyB2YWx1ZSB9fQ0KICAgICAgICAgIDwvc3Bhbj4NCiAgICAgICAgICA8c3BhbiB2LWVsc2U+DQogICAgICAgICAgICA8c2xvdCBuYW1lPSJ2YWx1ZSIgLz4NCiAgICAgICAgICA8L3NwYW4+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJjZWxsLWl0ZW0gbXQ1Ij4NCiAgICAgICAgPHNwYW4gdi1pZj0iZXh0cmEiPnt7IGV4dHJhIH19PC9zcGFuPg0KICAgICAgICA8c3BhbiB2LWVsc2U+PHNsb3QgbmFtZT0iZXh0cmEiIC8+PC9zcGFuPg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogIDwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;MACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/Cell/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"cell\">\r\n    <div class=\"cell-inner\" :class=\" border ? 'border' : '' \">\r\n      <div class=\"cell-item\">\r\n        <div class=\"cell-item-label\">\r\n          <span v-if=\"label\">\r\n            {{ label }}\r\n          </span>\r\n          <span v-else><slot name=\"label\" />\r\n          </span></div>\r\n        <div class=\"cell-item-value\">\r\n          <span v-if=\"value\">\r\n            {{ value }}\r\n          </span>\r\n          <span v-else>\r\n            <slot name=\"value\" />\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell-item mt5\">\r\n        <span v-if=\"extra\">{{ extra }}</span>\r\n        <span v-else><slot name=\"extra\" /></span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Cell',\r\n  props: {\r\n    border: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    label: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    extra: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cell-inner{\r\n  box-sizing: border-box;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  color: #323233;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  background-color: #fff;\r\n  padding: 10px 0;\r\n\r\n  .cell-item{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n  .cell-item-label{\r\n    span{\r\n      color: #323233;\r\n      font-size: 14px;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .cell-item-value{\r\n    color: #969799;\r\n  }\r\n}\r\n\r\n.border{\r\n  position: relative;\r\n  &::after{\r\n    position: absolute;\r\n    box-sizing: border-box;\r\n    content: ' ';\r\n    pointer-events: none;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    border-bottom: 1px solid #e6ebf5;\r\n  }\r\n}\r\n</style>\r\n"]}]}