{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\settings.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\settings.js", "mtime": 1753924830222}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSB7CiAgdGl0bGU6ICdnby1hZG1pbuWQjuWPsOeuoeeQhuezu+e7nycsCiAgLyoqDQogICAqIEB0eXBlIHtib29sZWFufSB0cnVlIHwgZmFsc2UNCiAgICogQGRlc2NyaXB0aW9uIFdoZXRoZXIgc2hvdyB0aGUgc2V0dGluZ3MgcmlnaHQtcGFuZWwNCiAgICovCiAgc2hvd1NldHRpbmdzOiB0cnVlLAogIC8qKg0KICAgKiDmmK/lkKbmmL7npLrpobbpg6jlr7zoiKoNCiAgICovCiAgdG9wTmF2OiB0cnVlLAogIC8qKg0KICAgKiBAdHlwZSB7Ym9vbGVhbn0gdHJ1ZSB8IGZhbHNlDQogICAqIEBkZXNjcmlwdGlvbiBXaGV0aGVyIG5lZWQgdGFnc1ZpZXcNCiAgICovCiAgdGFnc1ZpZXc6IHRydWUsCiAgLyoqDQogICAqIEB0eXBlIHtib29sZWFufSB0cnVlIHwgZmFsc2UNCiAgICogQGRlc2NyaXB0aW9uIFdoZXRoZXIgZml4IHRoZSBoZWFkZXINCiAgICovCiAgZml4ZWRIZWFkZXI6IHRydWUsCiAgLyoqDQogICAqIEB0eXBlIHtib29sZWFufSB0cnVlIHwgZmFsc2UNCiAgICogQGRlc2NyaXB0aW9uIFdoZXRoZXIgc2hvdyB0aGUgbG9nbyBpbiBzaWRlYmFyDQogICAqLwogIHNpZGViYXJMb2dvOiB0cnVlLAogIC8qKg0KICAgKiBAdHlwZSB7c3RyaW5nIHwgYXJyYXl9ICdwcm9kdWN0aW9uJyB8IFsncHJvZHVjdGlvbicsICdkZXZlbG9wbWVudCddDQogICAqIEBkZXNjcmlwdGlvbiBOZWVkIHNob3cgZXJyIGxvZ3MgY29tcG9uZW50Lg0KICAgKiBUaGUgZGVmYXVsdCBpcyBvbmx5IHVzZWQgaW4gdGhlIHByb2R1Y3Rpb24gZW52DQogICAqIElmIHlvdSB3YW50IHRvIGFsc28gdXNlIGl0IGluIGRldiwgeW91IGNhbiBwYXNzIFsncHJvZHVjdGlvbicsICdkZXZlbG9wbWVudCddDQogICAqLwogIGVycm9yTG9nOiAncHJvZHVjdGlvbicsCiAgdGhlbWVTdHlsZTogJ2RhcmsnCn07"}, {"version": 3, "names": ["module", "exports", "title", "showSettings", "topNav", "tagsView", "fixedHeader", "sidebarLogo", "errorLog", "themeStyle"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/settings.js"], "sourcesContent": ["module.exports = {\r\n  title: 'go-admin后台管理系统',\r\n\r\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether show the settings right-panel\r\n   */\r\n  showSettings: true,\r\n\r\n  /**\r\n   * 是否显示顶部导航\r\n   */\r\n  topNav: true,\r\n\r\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether need tagsView\r\n   */\r\n  tagsView: true,\r\n\r\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether fix the header\r\n   */\r\n  fixedHeader: true,\r\n\r\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether show the logo in sidebar\r\n   */\r\n  sidebarLogo: true,\r\n\r\n  /**\r\n   * @type {string | array} 'production' | ['production', 'development']\r\n   * @description Need show err logs component.\r\n   * The default is only used in the production env\r\n   * If you want to also use it in dev, you can pass ['production', 'development']\r\n   */\r\n  errorLog: 'production',\r\n\r\n  themeStyle: 'dark'\r\n}\r\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EACfC,KAAK,EAAE,gBAAgB;EAEvB;AACF;AACA;AACA;EACEC,YAAY,EAAE,IAAI;EAElB;AACF;AACA;EACEC,MAAM,EAAE,IAAI;EAEZ;AACF;AACA;AACA;EACEC,QAAQ,EAAE,IAAI;EAEd;AACF;AACA;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;AACA;EACEC,WAAW,EAAE,IAAI;EAEjB;AACF;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE,YAAY;EAEtBC,UAAU,EAAE;AACd,CAAC", "ignoreList": []}]}