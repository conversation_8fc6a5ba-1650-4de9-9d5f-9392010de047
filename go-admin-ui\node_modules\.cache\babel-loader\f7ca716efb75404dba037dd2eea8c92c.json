{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue", "mtime": 1753924830023}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdDZWxsJywKICBwcm9wczogewogICAgYm9yZGVyOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgbGFiZWw6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICBleHRyYTogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "props", "border", "type", "Boolean", "default", "label", "String", "value", "extra"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Cell\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"cell\">\r\n    <div class=\"cell-inner\" :class=\" border ? 'border' : '' \">\r\n      <div class=\"cell-item\">\r\n        <div class=\"cell-item-label\">\r\n          <span v-if=\"label\">\r\n            {{ label }}\r\n          </span>\r\n          <span v-else><slot name=\"label\" />\r\n          </span></div>\r\n        <div class=\"cell-item-value\">\r\n          <span v-if=\"value\">\r\n            {{ value }}\r\n          </span>\r\n          <span v-else>\r\n            <slot name=\"value\" />\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div class=\"cell-item mt5\">\r\n        <span v-if=\"extra\">{{ extra }}</span>\r\n        <span v-else><slot name=\"extra\" /></span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Cell',\r\n  props: {\r\n    border: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    label: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    extra: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cell-inner{\r\n  box-sizing: border-box;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  color: #323233;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n  background-color: #fff;\r\n  padding: 10px 0;\r\n\r\n  .cell-item{\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n  .cell-item-label{\r\n    span{\r\n      color: #323233;\r\n      font-size: 14px;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .cell-item-value{\r\n    color: #969799;\r\n  }\r\n}\r\n\r\n.border{\r\n  position: relative;\r\n  &::after{\r\n    position: absolute;\r\n    box-sizing: border-box;\r\n    content: ' ';\r\n    pointer-events: none;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    border-bottom: 1px solid #e6ebf5;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA4BA,eAAe;EACbA,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDG,KAAK,EAAE;MACLL,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX,CAAC;IACDI,KAAK,EAAE;MACLN,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF;AACF", "ignoreList": []}]}