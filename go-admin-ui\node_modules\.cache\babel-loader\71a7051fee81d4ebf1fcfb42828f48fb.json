{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\LineChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\LineChart.vue", "mtime": 1753924830284}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "require", "resize", "mixins", "props", "className", "type", "String", "default", "width", "height", "autoResize", "Boolean", "chartData", "Object", "required", "data", "chart", "watch", "deep", "handler", "val", "setOptions", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "_ref", "arguments", "length", "undefined", "expectedData", "actualData", "setOption", "xAxis", "boundaryGap", "axisTick", "show", "grid", "left", "right", "bottom", "top", "containLabel", "tooltip", "trigger", "axisPointer", "padding", "yAxis", "legend", "series", "name", "itemStyle", "normal", "color", "lineStyle", "smooth", "animationDuration", "animationEasing", "areaStyle"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\LineChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '350px'\r\n    },\r\n    autoResize: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chartData: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      deep: true,\r\n      handler(val) {\r\n        this.setOptions(val)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n      this.setOptions(this.chartData)\r\n    },\r\n    setOptions({ expectedData, actualData } = {}) {\r\n      this.chart.setOption({\r\n        xAxis: {\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          boundaryGap: false,\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 10,\r\n          right: 10,\r\n          bottom: 20,\r\n          top: 30,\r\n          containLabel: true\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          padding: [5, 10]\r\n        },\r\n        yAxis: {\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['expected', 'actual']\r\n        },\r\n        series: [{\r\n          name: 'expected', itemStyle: {\r\n            normal: {\r\n              color: '#FF005A',\r\n              lineStyle: {\r\n                color: '#FF005A',\r\n                width: 2\r\n              }\r\n            }\r\n          },\r\n          smooth: true,\r\n          type: 'line',\r\n          data: expectedData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'cubicInOut'\r\n        },\r\n        {\r\n          name: 'actual',\r\n          smooth: true,\r\n          type: 'line',\r\n          itemStyle: {\r\n            normal: {\r\n              color: '#3888fa',\r\n              lineStyle: {\r\n                color: '#3888fa',\r\n                width: 2\r\n              },\r\n              areaStyle: {\r\n                color: '#f3f8ff'\r\n              }\r\n            }\r\n          },\r\n          data: actualData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'quadraticOut'\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAKA,OAAOA,OAAM,MAAO,SAAQ;AAC5BC,OAAO,CAAC,wBAAwB,GAAE;AAClC,OAAOC,MAAK,MAAO,iBAAgB;AAEnC,eAAe;EACbC,MAAM,EAAE,CAACD,MAAM,CAAC;EAChBE,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDE,MAAM,EAAE;MACNJ,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDG,UAAU,EAAE;MACVL,IAAI,EAAEM,OAAO;MACbJ,OAAO,EAAE;IACX,CAAC;IACDK,SAAS,EAAE;MACTP,IAAI,EAAEQ,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,KAAK,EAAE;IACLL,SAAS,EAAE;MACTM,IAAI,EAAE,IAAI;MACVC,OAAO,WAAPA,OAAOA,CAACC,GAAG,EAAE;QACX,IAAI,CAACC,UAAU,CAACD,GAAG;MACrB;IACF;EACF,CAAC;EACDE,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,SAAS,CAAC,YAAM;MACnBD,KAAI,CAACE,SAAS,CAAC;IACjB,CAAC;EACH,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACV,KAAK,EAAE;MACf;IACF;IACA,IAAI,CAACA,KAAK,CAACW,OAAO,CAAC;IACnB,IAAI,CAACX,KAAI,GAAI,IAAG;EAClB,CAAC;EACDY,OAAO,EAAE;IACPH,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACT,KAAI,GAAIjB,OAAO,CAAC8B,IAAI,CAAC,IAAI,CAACC,GAAG,EAAE,UAAU;MAC9C,IAAI,CAACT,UAAU,CAAC,IAAI,CAACT,SAAS;IAChC,CAAC;IACDS,UAAU,WAAVA,UAAUA,CAAA,EAAoC;MAAA,IAAAU,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;QAA/BG,YAAY,GAAAJ,IAAA,CAAZI,YAAY;QAAEC,UAAS,GAAAL,IAAA,CAATK,UAAS;MAClC,IAAI,CAACpB,KAAK,CAACqB,SAAS,CAAC;QACnBC,KAAK,EAAE;UACLvB,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;UACvDwB,WAAW,EAAE,KAAK;UAClBC,QAAQ,EAAE;YACRC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,EAAE;UACPC,YAAY,EAAE;QAChB,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACX7C,IAAI,EAAE;UACR,CAAC;UACD8C,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC;QACDC,KAAK,EAAE;UACLZ,QAAQ,EAAE;YACRC,IAAI,EAAE;UACR;QACF,CAAC;QACDY,MAAM,EAAE;UACNtC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ;QAC7B,CAAC;QACDuC,MAAM,EAAE,CAAC;UACPC,IAAI,EAAE,UAAU;UAAEC,SAAS,EAAE;YAC3BC,MAAM,EAAE;cACNC,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE;gBACTD,KAAK,EAAE,SAAS;gBAChBlD,KAAK,EAAE;cACT;YACF;UACF,CAAC;UACDoD,MAAM,EAAE,IAAI;UACZvD,IAAI,EAAE,MAAM;UACZU,IAAI,EAAEoB,YAAY;UAClB0B,iBAAiB,EAAE,IAAI;UACvBC,eAAe,EAAE;QACnB,CAAC,EACD;UACEP,IAAI,EAAE,QAAQ;UACdK,MAAM,EAAE,IAAI;UACZvD,IAAI,EAAE,MAAM;UACZmD,SAAS,EAAE;YACTC,MAAM,EAAE;cACNC,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE;gBACTD,KAAK,EAAE,SAAS;gBAChBlD,KAAK,EAAE;cACT,CAAC;cACDuD,SAAS,EAAE;gBACTL,KAAK,EAAE;cACT;YACF;UACF,CAAC;UACD3C,IAAI,EAAEqB,UAAU;UAChByB,iBAAiB,EAAE,IAAI;UACvBC,eAAe,EAAE;QACnB,CAAC;MACH,CAAC;IACH;EACF;AACF", "ignoreList": []}]}