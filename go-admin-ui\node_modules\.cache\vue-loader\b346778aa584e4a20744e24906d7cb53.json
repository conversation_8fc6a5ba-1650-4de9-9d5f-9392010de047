{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-particles@1.0.9\\node_modules\\vue-particles\\src\\vue-particles\\vue-particles.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-particles@1.0.9\\node_modules\\vue-particles\\src\\vue-particles\\vue-particles.vue", "mtime": 1496175041000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-particles@1.0.9\\node_modules\\vue-particles\\src\\vue-particles\\vue-particles.vue"], "names": [], "mappings": ";EAqBE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACZ,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,EAAE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAClB;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cAChB;YACF;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;cAEpB;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACb;cACF,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACX,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAChB,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClB,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClB;YACF;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC;MACJ;;IAEF;EACF;EACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/node_modules/.store/vue-particles@1.0.9/node_modules/vue-particles/src/vue-particles/vue-particles.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div\n    id=\"particles-js\"\n    :color=\"color\"\n    :particleOpacity=\"particleOpacity\"\n    :linesColor=\"linesColor\"\n    :particlesNumber=\"particlesNumber\"\n    :shapeType=\"shapeType\"\n    :particleSize=\"particleSize\"\n    :linesWidth=\"linesWidth\"\n    :lineLinked=\"lineLinked\"\n    :lineOpacity=\"lineOpacity\"\n    :linesDistance=\"linesDistance\"\n    :moveSpeed=\"moveSpeed\"\n    :hoverEffect=\"hoverEffect\"\n    :hoverMode=\"hoverMode\"\n    :clickEffect=\"clickEffect\"\n    :clickMode=\"clickMode\"\n  ></div>\n</template>\n<script>\n  /* eslint-disable */\n  export default {\n    name: 'vue-particles',\n    props: {\n      color: {\n        type: String,\n        default: '#dedede'\n      },\n      particleOpacity: {\n        type: Number,\n        default: 0.7\n      },\n      particlesNumber: {\n        type: Number,\n        default: 80\n      },\n      shapeType: {\n        type: String,\n        default: 'circle'\n      },\n      particleSize: {\n        type: Number,\n        default: 4\n      },\n      linesColor: {\n        type: String,\n        default: '#dedede'\n      },\n      linesWidth: {\n        type: Number,\n        default: 1\n      },\n      lineLinked: {\n        type: Boolean,\n        default: true\n      },\n      lineOpacity: {\n        type: Number,\n        default: 0.4\n      },\n      linesDistance: {\n        type: Number,\n        default: 150\n      },\n      moveSpeed: {\n        type: Number,\n        default: 3\n      },\n      hoverEffect: {\n        type: Boolean,\n        default: true\n      },\n      hoverMode: {\n        type: String,\n        default: 'grab'\n      },\n      clickEffect: {\n        type: Boolean,\n        default: true\n      },\n      clickMode: {\n        type: String,\n        default: 'push'\n      }\n    },\n    mounted () {\n      // import particle.js only on client-side\n      require('particles.js')\n      this.$nextTick(() => {\n        this.initParticleJS(\n          this.color,\n          this.particleOpacity,\n          this.particlesNumber,\n          this.shapeType,\n          this.particleSize,\n          this.linesColor,\n          this.linesWidth,\n          this.lineLinked,\n          this.lineOpacity,\n          this.linesDistance,\n          this.moveSpeed,\n          this.hoverEffect,\n          this.hoverMode,\n          this.clickEffect,\n          this.clickMode\n        )\n      })\n    },\n    methods: {\n      initParticleJS (\n        color,\n        particleOpacity,\n        particlesNumber,\n        shapeType,\n        particleSize,\n        linesColor,\n        linesWidth,\n        lineLinked,\n        lineOpacity,\n        linesDistance,\n        moveSpeed,\n        hoverEffect,\n        hoverMode,\n        clickEffect,\n        clickMode\n      ) {\n        particlesJS('particles-js', {\n          \"particles\": {\n            \"number\": {\n              \"value\": particlesNumber,\n              \"density\": {\n                \"enable\": true,\n                \"value_area\": 800\n              }\n            },\n            \"color\": {\n              \"value\": color\n            },\n            \"shape\": {\n              // circle, edge, triangle, polygon, star, image\n              \"type\": shapeType,\n              \"stroke\": {\n                \"width\": 0,\n                \"color\": \"#192231\"\n              },\n              \"polygon\": {\n                \"nb_sides\": 5\n              }\n            },\n            \"opacity\": {\n              \"value\": particleOpacity,\n              \"random\": false,\n              \"anim\": {\n                \"enable\": false,\n                \"speed\": 1,\n                \"opacity_min\": 0.1,\n                \"sync\": false\n              }\n            },\n            \"size\": {\n              \"value\": particleSize,\n              \"random\": true,\n              \"anim\": {\n                \"enable\": false,\n                \"speed\": 40,\n                \"size_min\": 0.1,\n                \"sync\": false\n              }\n            },\n            \"line_linked\": {\n              \"enable\": lineLinked,\n              \"distance\": linesDistance,\n              \"color\": linesColor,\n              \"opacity\": lineOpacity,\n              \"width\": linesWidth\n            },\n            \"move\": {\n              \"enable\": true,\n              \"speed\": moveSpeed,\n              \"direction\": \"none\",\n              \"random\": false,\n              \"straight\": false,\n              \"out_mode\": \"out\",\n              \"bounce\": false,\n              \"attract\": {\n                \"enable\": false,\n                \"rotateX\": 600,\n                \"rotateY\": 1200\n              }\n            }\n          },\n          \"interactivity\": {\n            \"detect_on\": \"canvas\",\n            \"events\": {\n              \"onhover\": {\n                \"enable\": hoverEffect,\n                \"mode\": hoverMode\n              },\n              \"onclick\": {\n                \"enable\": clickEffect,\n                \"mode\": clickMode\n              },\n              \"onresize\": {\n\n                \"enable\": true,\n                \"density_auto\": true,\n                \"density_area\": 400\n\n              }\n            },\n            \"modes\": {\n              \"grab\": {\n                \"distance\": 140,\n                \"line_linked\": {\n                  \"opacity\": 1\n                }\n              },\n              \"bubble\": {\n                \"distance\": 400,\n                \"size\": 40,\n                \"duration\": 2,\n                \"opacity\": 8,\n                \"speed\": 3\n              },\n              \"repulse\": {\n                \"distance\": 200,\n                \"duration\": 0.4\n              },\n              \"push\": {\n                \"particles_nb\": 4\n              },\n              \"remove\": {\n                \"particles_nb\": 2\n              }\n            }\n          },\n          \"retina_detect\": true\n        });\n      }\n\n    }\n  }\n  /* eslint-disable */\n</script>\n"]}]}