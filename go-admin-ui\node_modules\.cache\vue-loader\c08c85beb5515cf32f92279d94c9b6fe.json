{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1753924830212}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}