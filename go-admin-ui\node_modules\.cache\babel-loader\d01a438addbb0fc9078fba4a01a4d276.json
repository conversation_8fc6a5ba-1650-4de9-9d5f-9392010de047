{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue", "mtime": 1753924830481}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["unWsLogout", "name", "data", "websock", "arrs", "id", "undefined", "group", "created", "guid", "initWebSocket", "destroyed", "console", "log", "close", "then", "response", "methods", "$store", "state", "user", "token", "w<PERSON>ri", "WebSocket", "onmessage", "websocketonmessage", "onopen", "websocketonopen", "onerror", "websocketonerror", "onclose", "websocketclose", "e", "unshift", "websocketsend", "Data", "replace", "c", "r", "Math", "random", "v", "toString"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\schedule\\log.vue"], "sourcesContent": ["\r\n<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form>\r\n          <el-form-item>\r\n            <el-button type=\"success\" icon=\"el-icon-search\" size=\"mini\">状态</el-button>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\">清空</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row ref=\"log\" :gutter=\"10\" class=\"mb8\">\r\n          <el-scrollbar style=\"height:500px;background-color: black;color: cornflowerblue;\">\r\n            <ul\r\n              style=\"line-height: 25px;padding-top: 15px;padding-bottom: 15px;min-height: 500px; margin: 0;list-style-type: none;\"\r\n            >\r\n              <li v-for=\"(item,index) in arrs\" :key=\"index\">\r\n\r\n                {{ item }}\r\n              </li>\r\n            </ul>\r\n          </el-scrollbar>\r\n        </el-row>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n\r\n</template>\r\n\r\n<script>\r\n\r\nimport { unWsLogout } from '@/api/ws'\r\nexport default {\r\n  name: 'SysJobLogManage',\r\n  data() {\r\n    return {\r\n      websock: null,\r\n      arrs: [],\r\n      id: undefined,\r\n      group: undefined\r\n    }\r\n  },\r\n  created() {\r\n    this.id = this.guid()\r\n    this.group = 'log'\r\n    this.initWebSocket()\r\n  },\r\n  destroyed() {\r\n    console.log('断开websocket连接')\r\n    this.websock.close() // 离开路由之后断开websocket连接\r\n    unWsLogout(this.id, this.group).then(response => {\r\n      console.log(response.data)\r\n    }\r\n    )\r\n  },\r\n  methods: {\r\n    initWebSocket() { // 初始化weosocket\r\n      console.log(this.$store.state.user.token)\r\n      const wsuri = 'ws://127.0.0.1:8000/ws/' + this.id + '/' + this.group + '?token=' + this.$store.state.user.token\r\n      this.websock = new WebSocket(wsuri)\r\n      this.websock.onmessage = this.websocketonmessage\r\n      this.websock.onopen = this.websocketonopen\r\n      this.websock.onerror = this.websocketonerror\r\n      this.websock.onclose = this.websocketclose\r\n    },\r\n    websocketonopen() { // 连接建立之后执行send方法发送数据\r\n      console.log('连接打开')\r\n    //   const actions = { 'test': '12345' }\r\n    //   this.websocketsend(JSON.stringify(actions))\r\n    },\r\n    websocketonerror() { // 连接建立失败重连\r\n      this.initWebSocket()\r\n    },\r\n    websocketonmessage(e) { // 数据接收\r\n      console.log(e.data)\r\n      //   console.log(this.binaryAgent(e))\r\n      //   const redata = JSON.parse(e.data)\r\n      //   console.log(redata)\r\n      //   this.$refs.log.innerText = e.data + '\\n' + this.$refs.log.innerText\r\n      this.arrs.unshift(e.data)\r\n    },\r\n    websocketsend(Data) { // 数据发送\r\n    //   this.websock.send(Data)\r\n    },\r\n    websocketclose(e) { // 关闭\r\n      unWsLogout(this.id, this.group).then(response => {\r\n        console.log(response.data)\r\n      }\r\n      )\r\n      console.log('断开连接', e)\r\n    },\r\n    guid() {\r\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n        var r = Math.random() * 16 | 0; var v = c === 'x' ? r : (r & 0x3 | 0x8)\r\n        return v.toString(16)\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n</script>\r\n"], "mappings": ";;;;AA+BA,SAASA,UAAS,QAAS,UAAS;AACpC,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,EAAE;MACRC,EAAE,EAAEC,SAAS;MACbC,KAAK,EAAED;IACT;EACF,CAAC;EACDE,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACH,EAAC,GAAI,IAAI,CAACI,IAAI,CAAC;IACpB,IAAI,CAACF,KAAI,GAAI,KAAI;IACjB,IAAI,CAACG,aAAa,CAAC;EACrB,CAAC;EACDC,SAAS,WAATA,SAASA,CAAA,EAAG;IACVC,OAAO,CAACC,GAAG,CAAC,eAAe;IAC3B,IAAI,CAACV,OAAO,CAACW,KAAK,CAAC,GAAE;IACrBd,UAAU,CAAC,IAAI,CAACK,EAAE,EAAE,IAAI,CAACE,KAAK,CAAC,CAACQ,IAAI,CAAC,UAAAC,QAAO,EAAK;MAC/CJ,OAAO,CAACC,GAAG,CAACG,QAAQ,CAACd,IAAI;IAC3B,CACA;EACF,CAAC;EACDe,OAAO,EAAE;IACPP,aAAa,WAAbA,aAAaA,CAAA,EAAG;MAAE;MAChBE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACK,MAAM,CAACC,KAAK,CAACC,IAAI,CAACC,KAAK;MACxC,IAAMC,KAAI,GAAI,yBAAwB,GAAI,IAAI,CAACjB,EAAC,GAAI,GAAE,GAAI,IAAI,CAACE,KAAI,GAAI,SAAQ,GAAI,IAAI,CAACW,MAAM,CAACC,KAAK,CAACC,IAAI,CAACC,KAAI;MAC9G,IAAI,CAAClB,OAAM,GAAI,IAAIoB,SAAS,CAACD,KAAK;MAClC,IAAI,CAACnB,OAAO,CAACqB,SAAQ,GAAI,IAAI,CAACC,kBAAiB;MAC/C,IAAI,CAACtB,OAAO,CAACuB,MAAK,GAAI,IAAI,CAACC,eAAc;MACzC,IAAI,CAACxB,OAAO,CAACyB,OAAM,GAAI,IAAI,CAACC,gBAAe;MAC3C,IAAI,CAAC1B,OAAO,CAAC2B,OAAM,GAAI,IAAI,CAACC,cAAa;IAC3C,CAAC;IACDJ,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAAE;MAClBf,OAAO,CAACC,GAAG,CAAC,MAAM;MACpB;MACA;IACA,CAAC;IACDgB,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MAAE;MACnB,IAAI,CAACnB,aAAa,CAAC;IACrB,CAAC;IACDe,kBAAkB,WAAlBA,kBAAkBA,CAACO,CAAC,EAAE;MAAE;MACtBpB,OAAO,CAACC,GAAG,CAACmB,CAAC,CAAC9B,IAAI;MAClB;MACA;MACA;MACA;MACA,IAAI,CAACE,IAAI,CAAC6B,OAAO,CAACD,CAAC,CAAC9B,IAAI;IAC1B,CAAC;IACDgC,aAAa,WAAbA,aAAaA,CAACC,IAAI,EAAE,CAAE;MACtB;IAAA,CACC;IACDJ,cAAc,WAAdA,cAAcA,CAACC,CAAC,EAAE;MAAE;MAClBhC,UAAU,CAAC,IAAI,CAACK,EAAE,EAAE,IAAI,CAACE,KAAK,CAAC,CAACQ,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC/CJ,OAAO,CAACC,GAAG,CAACG,QAAQ,CAACd,IAAI;MAC3B,CACA;MACAU,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEmB,CAAC;IACvB,CAAC;IACDvB,IAAI,WAAJA,IAAIA,CAAA,EAAG;MACL,OAAO,sCAAsC,CAAC2B,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC,EAAE;QACzE,IAAIC,CAAA,GAAIC,IAAI,CAACC,MAAM,CAAC,IAAI,EAAC,GAAI,CAAC;QAAE,IAAIC,CAAA,GAAIJ,CAAA,KAAM,GAAE,GAAIC,CAAA,GAAKA,CAAA,GAAI,GAAE,GAAI,GAAG;QACtE,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE;MACtB,CAAC;IACH;EACF;AACF", "ignoreList": []}]}