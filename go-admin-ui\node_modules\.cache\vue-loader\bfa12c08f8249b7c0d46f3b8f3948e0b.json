{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue?vue&type=style&index=0&id=4dfc1336&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1753924830293}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIC5lbXB0eUdpZiB7DQogICAgZGlzcGxheTogYmxvY2s7DQogICAgd2lkdGg6IDQ1JTsNCiAgICBtYXJnaW46IDAgYXV0bzsNCiAgfQ0KDQogIC5kYXNoYm9hcmQtZWRpdG9yLWNvbnRhaW5lciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZTNlMzsNCiAgICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAgICBwYWRkaW5nOiA1MHB4IDYwcHggMHB4Ow0KICAgIC5wYW4taW5mby1yb2xlcyB7DQogICAgICBmb250LXNpemU6IDEycHg7DQogICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgY29sb3I6ICMzMzM7DQogICAgICBkaXNwbGF5OiBibG9jazsNCiAgICB9DQogICAgLmluZm8tY29udGFpbmVyIHsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgIG1hcmdpbi1sZWZ0OiAxOTBweDsNCiAgICAgIGhlaWdodDogMTUwcHg7DQogICAgICBsaW5lLWhlaWdodDogMjAwcHg7DQogICAgICAuZGlzcGxheV9uYW1lIHsNCiAgICAgICAgZm9udC1zaXplOiA0OHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogNDhweDsNCiAgICAgICAgY29sb3I6ICMyMTIxMjE7DQogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgdG9wOiAyNXB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue"], "names": [], "mappings": ";EA2CE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX;IACF;EACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/editor/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <div class=\" clearfix\">\r\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\r\n        Your roles:\r\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\r\n      </pan-thumb>\r\n      <github-corner style=\"position: absolute; top: 0px; border: 0; right: 0;\" />\r\n      <div class=\"info-container\">\r\n        <span class=\"display_name\">{{ name }}</span>\r\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <img :src=\"emptyGif\" class=\"emptyGif\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport PanThumb from '@/components/PanThumb'\r\nimport GithubCorner from '@/components/GithubCorner'\r\n\r\nexport default {\r\n  name: 'DashboardEditor',\r\n  components: { PanThumb, GithubCorner },\r\n  data() {\r\n    return {\r\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'name',\r\n      'avatar',\r\n      'roles'\r\n    ])\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .emptyGif {\r\n    display: block;\r\n    width: 45%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .dashboard-editor-container {\r\n    background-color: #e3e3e3;\r\n    min-height: 100vh;\r\n    padding: 50px 60px 0px;\r\n    .pan-info-roles {\r\n      font-size: 12px;\r\n      font-weight: 700;\r\n      color: #333;\r\n      display: block;\r\n    }\r\n    .info-container {\r\n      position: relative;\r\n      margin-left: 190px;\r\n      height: 150px;\r\n      line-height: 200px;\r\n      .display_name {\r\n        font-size: 48px;\r\n        line-height: 48px;\r\n        color: #212121;\r\n        position: absolute;\r\n        top: 25px;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}