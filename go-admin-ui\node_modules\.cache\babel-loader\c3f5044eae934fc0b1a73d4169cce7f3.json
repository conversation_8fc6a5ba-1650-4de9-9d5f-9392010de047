{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue", "mtime": 1753924830300}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["listTable", "previewTable", "delTable", "toDBTable", "toProjectTableCheckRole", "apiToFile", "importTable", "downLoadFile", "codemirror", "require", "name", "components", "data", "cmOptions", "tabSize", "theme", "mode", "lineNumbers", "line", "codestr", "loading", "uniqueId", "ids", "tableNames", "single", "multiple", "total", "tableList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageIndex", "pageSize", "tableName", "undefined", "tableComment", "preview", "open", "title", "activeName", "created", "getList", "activated", "time", "$route", "query", "t", "reset<PERSON><PERSON>y", "methods", "_this", "addDateRange", "then", "response", "list", "count", "codeChange", "e", "indexOf", "handleQuery", "handleGenTable", "row", "tableId", "msgError", "openImportTable", "$refs", "importTB", "show", "resetForm", "handlePreview", "_this2", "handleToProject", "_this3", "msgSuccess", "msg", "catch", "handleToApiFile", "_this4", "handleToDB", "_this5", "handleSelectionChange", "selection", "map", "item", "length", "handleEditTable", "$router", "push", "path", "handleDelete", "_this6", "tableIds", "$confirm", "confirmButtonText", "cancelButtonText", "type", "code", "handleSingleDelete", "_this7"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-position=\"left\">\r\n          <el-form-item label=\"表名称\" prop=\"tableName\">\r\n            <el-input\r\n              v-model=\"queryParams.tableName\"\r\n              placeholder=\"请输入表名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"菜单名称\" prop=\"tableComment\">\r\n            <el-input\r\n              v-model=\"queryParams.tableComment\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleGenTable\"\r\n            >生成</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"info\"\r\n              icon=\"el-icon-upload\"\r\n              size=\"mini\"\r\n              @click=\"openImportTable\"\r\n            >导入</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"success\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleEditTable\"\r\n            >修改</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" />\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"tableId\" width=\"50px\" />\r\n          <el-table-column\r\n            label=\"表名称\"\r\n            align=\"center\"\r\n            prop=\"tableName\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column\r\n            label=\"菜单名称\"\r\n            align=\"center\"\r\n            prop=\"tableComment\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column\r\n            label=\"模型名称\"\r\n            align=\"center\"\r\n            prop=\"className\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"165\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleEditTable(scope.row)\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handlePreview(scope.row)\"\r\n              >预览</el-button>\r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"handleToProject(scope.row)\"\r\n                >代码生成</el-button>\r\n\r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"handleToDB(scope.row)\"\r\n                >生成配置</el-button>\r\n\r\n     \r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                   @click=\"handleToApiFile(scope.row)\"\r\n                >生成迁移脚本</el-button>\r\n                \r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleSingleDelete(scope.row)\"\r\n                >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 预览界面 -->\r\n\r\n      <el-dialog class=\"preview\" :title=\"preview.title\" :visible.sync=\"preview.open\" :close-on-click-modal=\"false\" fullscreen>\r\n        <div class=\"el-dialog-container\">\r\n          <div class=\"tag-group\">\r\n            <!-- eslint-disable-next-line vue/valid-v-for -->\r\n            <el-tag v-for=\"(value, key) in preview.data\" @click=\"codeChange(key)\">\r\n              <template>\r\n                {{ key.substring(key.lastIndexOf('/')+1,key.indexOf('.go.template')) }}\r\n              </template>\r\n            </el-tag>\r\n          </div>\r\n          <div id=\"codemirror\">\r\n            <codemirror ref=\"cmEditor\" :value=\"codestr\" :options=\"cmOptions\" />\r\n          </div>\r\n          <!-- <el-tabs v-model=\"preview.activeName\" tab-position=\"left\">\r\n            <el-tab-pane\r\n              v-for=\"(value, key) in preview.data\"\r\n              :key=\"key\"\r\n              :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.template'))\"\r\n              :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.template'))\"\r\n            >\r\n\r\n              <pre class=\"pre\"/>\r\n\r\n            </el-tab-pane>\r\n            </el-tabs> -->\r\n        </div>\r\n\r\n      </el-dialog>\r\n      <import-table ref=\"importTB\" @ok=\"handleQuery\" />\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listTable, previewTable, delTable, toDBTable, toProjectTableCheckRole, apiToFile } from '@/api/tools/gen'\r\nimport importTable from './importTable'\r\nimport { downLoadFile } from '@/utils/zipdownload'\r\nimport { codemirror } from 'vue-codemirror'\r\nimport 'codemirror/theme/material-palenight.css'\r\n\r\nrequire('codemirror/mode/javascript/javascript')\r\nimport 'codemirror/mode/javascript/javascript'\r\nimport 'codemirror/mode/go/go'\r\nimport 'codemirror/mode/vue/vue'\r\n\r\nexport default {\r\n  name: 'Gen',\r\n  components: { importTable, codemirror },\r\n  data() {\r\n    return {\r\n      cmOptions: {\r\n        tabSize: 4,\r\n        theme: 'material-palenight',\r\n        mode: 'text/javascript',\r\n        lineNumbers: true,\r\n        line: true\r\n        // more CodeMirror options...\r\n      },\r\n      codestr: '',\r\n      // 遮罩层\r\n      loading: true,\r\n      // 唯一标识符\r\n      uniqueId: '',\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中表数组\r\n      tableNames: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      tableList: [],\r\n      // 日期范围\r\n      dateRange: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      },\r\n      // 预览参数\r\n      preview: {\r\n        open: false,\r\n        title: '代码预览',\r\n        data: {},\r\n        activeName: 'api.go'\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  activated() {\r\n    const time = this.$route.query.t\r\n    if (time !== null && time !== this.uniqueId) {\r\n      this.uniqueId = time\r\n      this.resetQuery()\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询表集合 */\r\n    getList() {\r\n      this.loading = true\r\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.tableList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    codeChange(e) {\r\n      if (e.indexOf('js') > -1) {\r\n        this.cmOptions.mode = 'text/javascript'\r\n      }\r\n      if (e.indexOf('model') > -1 || e.indexOf('router') > -1 || e.indexOf('api') > -1 || e.indexOf('service') > -1 || e.indexOf('dto') > -1) {\r\n        this.cmOptions.mode = 'text/x-go'\r\n      }\r\n      if (e.indexOf('vue') > -1) {\r\n        this.cmOptions.mode = 'text/x-vue'\r\n      }\r\n      this.codestr = this.preview.data[e]\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 生成代码操作 */\r\n    handleGenTable(row) {\r\n      const ids = row.tableId || this.ids\r\n      if (ids === '') {\r\n        this.msgError('请选择要生成的数据')\r\n        return\r\n      }\r\n      downLoadFile('/api/v1/gen/gencode/' + ids)\r\n    },\r\n    /** 打开导入表弹窗 */\r\n    openImportTable() {\r\n      this.$refs.importTB.show()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 预览按钮 */\r\n    handlePreview(row) {\r\n      previewTable(row.tableId).then(response => {\r\n        this.preview.data = response.data\r\n        this.preview.open = true\r\n        this.codeChange('template/api.go.template')\r\n      })\r\n    },\r\n    handleToProject(row) {\r\n      toProjectTableCheckRole(row.tableId, false).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    handleToApiFile(row) {\r\n      apiToFile(row.tableId, true).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    handleToDB(row) {\r\n      toDBTable(row.tableId).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.tableId)\r\n      this.tableNames = selection.map(item => item.tableName)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleEditTable(row) {\r\n      const tableId = row.tableId || this.ids[0]\r\n      this.$router.push({ path: '/dev-tools/editTable', query: { tableId: tableId }})\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const tableIds = row.tableId || this.ids\r\n      this.$confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delTable(tableIds)\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    handleSingleDelete(row) {\r\n      const tableIds = row.tableId || this.ids\r\n      delTable(tableIds).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n .el-dialog-container ::v-deep{\r\n   overflow: hidden;\r\n   .el-scrollbar__view{\r\n     height: 100%;\r\n   }\r\n   .pre{\r\n     height: 546px;\r\n      overflow: hidden;\r\n      .el-scrollbar{\r\n        height: 100%;\r\n      }\r\n   }\r\n   .el-scrollbar__wrap::-webkit-scrollbar{\r\n     display: none;\r\n   }\r\n }\r\n ::v-deep .el-dialog__body{\r\n    padding: 0 20px;\r\n    margin:0;\r\n  }\r\n  .tag-group {\r\n    margin: 0 0 10px -10px;\r\n  }\r\n  .tag-group .el-tag{\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .el-tag {\r\n    cursor: pointer;\r\n  }\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n  #codemirror {\r\n      height: auto;\r\n      margin: 0;\r\n      overflow: auto;\r\n    }\r\n  .CodeMirror {\r\n      border: 1px solid #eee;\r\n      height: 600px;\r\n    }\r\n</style>\r\n"], "mappings": ";;;;;AA+LA,SAASA,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,SAAQ,QAAS,iBAAgB;AACjH,OAAOC,WAAU,MAAO,eAAc;AACtC,SAASC,YAAW,QAAS,qBAAoB;AACjD,SAASC,UAAS,QAAS,gBAAe;AAC1C,OAAO,yCAAwC;AAE/CC,OAAO,CAAC,uCAAuC;AAC/C,OAAO,uCAAsC;AAC7C,OAAO,uBAAsB;AAC7B,OAAO,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IAAEL,WAAW,EAAXA,WAAW;IAAEE,UAAS,EAATA;EAAW,CAAC;EACvCI,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE;QACTC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,oBAAoB;QAC3BC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,IAAI;QACjBC,IAAI,EAAE;QACN;MACF,CAAC;MACDC,OAAO,EAAE,EAAE;MACX;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,QAAQ,EAAE,EAAE;MACZ;MACAC,GAAG,EAAE,EAAE;MACP;MACAC,UAAU,EAAE,EAAE;MACd;MACAC,MAAM,EAAE,IAAI;MACZ;MACAC,QAAQ,EAAE,IAAI;MACd;MACAC,KAAK,EAAE,CAAC;MACR;MACAC,SAAS,EAAE,EAAE;MACb;MACAC,SAAS,EAAE,EAAE;MACb;MACAC,WAAW,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAEC,SAAS;QACpBC,YAAY,EAAED;MAChB,CAAC;MACD;MACAE,OAAO,EAAE;QACPC,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,MAAM;QACbzB,IAAI,EAAE,CAAC,CAAC;QACR0B,UAAU,EAAE;MACd;IACF;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC;EACf,CAAC;EACDC,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,IAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,CAAA;IAC/B,IAAIH,IAAG,KAAM,IAAG,IAAKA,IAAG,KAAM,IAAI,CAACrB,QAAQ,EAAE;MAC3C,IAAI,CAACA,QAAO,GAAIqB,IAAG;MACnB,IAAI,CAACI,UAAU,CAAC;IAClB;EACF,CAAC;EACDC,OAAO,EAAE;IACP,YACAP,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAQ,KAAA;MACR,IAAI,CAAC5B,OAAM,GAAI,IAAG;MAClBpB,SAAS,CAAC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACpB,WAAW,EAAE,IAAI,CAACD,SAAS,CAAC,CAAC,CAACsB,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC9EH,KAAI,CAACrB,SAAQ,GAAIwB,QAAQ,CAACvC,IAAI,CAACwC,IAAG;QAClCJ,KAAI,CAACtB,KAAI,GAAIyB,QAAQ,CAACvC,IAAI,CAACyC,KAAI;QAC/BL,KAAI,CAAC5B,OAAM,GAAI,KAAI;MACrB,CACA;IACF,CAAC;IACDkC,UAAU,WAAVA,UAAUA,CAACC,CAAC,EAAE;MACZ,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE;QACxB,IAAI,CAAC3C,SAAS,CAACG,IAAG,GAAI,iBAAgB;MACxC;MACA,IAAIuC,CAAC,CAACC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAKD,CAAC,CAACC,OAAO,CAAC,QAAQ,IAAI,CAAC,KAAKD,CAAC,CAACC,OAAO,CAAC,KAAK,IAAI,CAAC,KAAKD,CAAC,CAACC,OAAO,CAAC,SAAS,IAAI,CAAC,KAAKD,CAAC,CAACC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;QACtI,IAAI,CAAC3C,SAAS,CAACG,IAAG,GAAI,WAAU;MAClC;MACA,IAAIuC,CAAC,CAACC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;QACzB,IAAI,CAAC3C,SAAS,CAACG,IAAG,GAAI,YAAW;MACnC;MACA,IAAI,CAACG,OAAM,GAAI,IAAI,CAACgB,OAAO,CAACvB,IAAI,CAAC2C,CAAC;IACpC,CAAC;IACD,aACAE,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC5B,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAACU,OAAO,CAAC;IACf,CAAC;IACD,aACAkB,cAAc,WAAdA,cAAcA,CAACC,GAAG,EAAE;MAClB,IAAMrC,GAAE,GAAIqC,GAAG,CAACC,OAAM,IAAK,IAAI,CAACtC,GAAE;MAClC,IAAIA,GAAE,KAAM,EAAE,EAAE;QACd,IAAI,CAACuC,QAAQ,CAAC,WAAW;QACzB;MACF;MACAtD,YAAY,CAAC,sBAAqB,GAAIe,GAAG;IAC3C,CAAC;IACD,cACAwC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC3B,CAAC;IACD,aACAnB,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAAClB,SAAQ,GAAI,EAAC;MAClB,IAAI,CAACsC,SAAS,CAAC,WAAW;MAC1B,IAAI,CAACT,WAAW,CAAC;IACnB,CAAC;IACD,WACAU,aAAa,WAAbA,aAAaA,CAACR,GAAG,EAAE;MAAA,IAAAS,MAAA;MACjBnE,YAAY,CAAC0D,GAAG,CAACC,OAAO,CAAC,CAACV,IAAI,CAAC,UAAAC,QAAO,EAAK;QACzCiB,MAAI,CAACjC,OAAO,CAACvB,IAAG,GAAIuC,QAAQ,CAACvC,IAAG;QAChCwD,MAAI,CAACjC,OAAO,CAACC,IAAG,GAAI,IAAG;QACvBgC,MAAI,CAACd,UAAU,CAAC,0BAA0B;MAC5C,CAAC;IACH,CAAC;IACDe,eAAe,WAAfA,eAAeA,CAACV,GAAG,EAAE;MAAA,IAAAW,MAAA;MACnBlE,uBAAuB,CAACuD,GAAG,CAACC,OAAO,EAAE,KAAK,CAAC,CAACV,IAAI,CAAC,UAACC,QAAQ,EAAK;QAC7DmB,MAAI,CAACC,UAAU,CAACpB,QAAQ,CAACqB,GAAG;MAC9B,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACDC,eAAe,WAAfA,eAAeA,CAACf,GAAG,EAAE;MAAA,IAAAgB,MAAA;MACnBtE,SAAS,CAACsD,GAAG,CAACC,OAAO,EAAE,IAAI,CAAC,CAACV,IAAI,CAAC,UAACC,QAAQ,EAAK;QAC9CwB,MAAI,CAACJ,UAAU,CAACpB,QAAQ,CAACqB,GAAG;MAC9B,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACDG,UAAU,WAAVA,UAAUA,CAACjB,GAAG,EAAE;MAAA,IAAAkB,MAAA;MACd1E,SAAS,CAACwD,GAAG,CAACC,OAAO,CAAC,CAACV,IAAI,CAAC,UAACC,QAAQ,EAAK;QACxC0B,MAAI,CAACN,UAAU,CAACpB,QAAQ,CAACqB,GAAG;MAC9B,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACD;IACAK,qBAAqB,WAArBA,qBAAqBA,CAACC,SAAS,EAAE;MAC/B,IAAI,CAACzD,GAAE,GAAIyD,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACrB,OAAO;MAAA;MAC7C,IAAI,CAACrC,UAAS,GAAIwD,SAAS,CAACC,GAAG,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACjD,SAAS;MAAA;MACtD,IAAI,CAACR,MAAK,GAAIuD,SAAS,CAACG,MAAK,KAAM;MACnC,IAAI,CAACzD,QAAO,GAAI,CAACsD,SAAS,CAACG,MAAK;IAClC,CAAC;IACD,aACAC,eAAe,WAAfA,eAAeA,CAACxB,GAAG,EAAE;MACnB,IAAMC,OAAM,GAAID,GAAG,CAACC,OAAM,IAAK,IAAI,CAACtC,GAAG,CAAC,CAAC;MACzC,IAAI,CAAC8D,OAAO,CAACC,IAAI,CAAC;QAAEC,IAAI,EAAE,sBAAsB;QAAE1C,KAAK,EAAE;UAAEgB,OAAO,EAAEA;QAAQ;MAAC,CAAC;IAChF,CAAC;IACD,aACA2B,YAAY,WAAZA,YAAYA,CAAC5B,GAAG,EAAE;MAAA,IAAA6B,MAAA;MAChB,IAAMC,QAAO,GAAI9B,GAAG,CAACC,OAAM,IAAK,IAAI,CAACtC,GAAE;MACvC,IAAI,CAACoE,QAAQ,CAAC,aAAY,GAAID,QAAO,GAAI,QAAQ,EAAE,IAAI,EAAE;QACvDE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC3C,IAAI,CAAC,YAAW;QACjB,OAAOhD,QAAQ,CAACuF,QAAQ;MAC1B,CAAC,CAAC,CAACvC,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAAC2C,IAAG,KAAM,GAAG,EAAE;UACzBN,MAAI,CAACjB,UAAU,CAACpB,QAAQ,CAACqB,GAAG;UAC5BgB,MAAI,CAACpD,IAAG,GAAI,KAAI;UAChBoD,MAAI,CAAChD,OAAO,CAAC;QACf,OAAO;UACLgD,MAAI,CAAC3B,QAAQ,CAACV,QAAQ,CAACqB,GAAG;QAC5B;MACF,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB,CAAC;IACDsB,kBAAkB,WAAlBA,kBAAkBA,CAACpC,GAAG,EAAE;MAAA,IAAAqC,MAAA;MACtB,IAAMP,QAAO,GAAI9B,GAAG,CAACC,OAAM,IAAK,IAAI,CAACtC,GAAE;MACvCpB,QAAQ,CAACuF,QAAQ,CAAC,CAACvC,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpC,IAAIA,QAAQ,CAAC2C,IAAG,KAAM,GAAG,EAAE;UACzBE,MAAI,CAACzB,UAAU,CAACpB,QAAQ,CAACqB,GAAG;UAC5BwB,MAAI,CAAC5D,IAAG,GAAI,KAAI;UAChB4D,MAAI,CAACxD,OAAO,CAAC;QACf,OAAO;UACLwD,MAAI,CAACnC,QAAQ,CAACV,QAAQ,CAACqB,GAAG;QAC5B;MACF,CAAC,CAAC,CAACC,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB;EACF;AACF", "ignoreList": []}]}