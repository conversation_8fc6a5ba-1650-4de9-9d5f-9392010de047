{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue?vue&type=template&id=38230808", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue", "mtime": 1753924830277}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvB,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpB;kBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/I,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAE1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrB,CAAC;kBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC;sBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC;wBACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC;sBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC;sBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClC,CAAC;sBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvB;sBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/admin/sys-menu/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form :inline=\"true\">\r\n          <el-form-item label=\"菜单名称\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"queryParams.visible\" placeholder=\"菜单状态\" clearable size=\"small\">\r\n              <el-option\r\n                v-for=\"dict in visibleOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button\r\n              v-permisaction=\"['admin:sysMenu:add']\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n            >新增</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"menuList\"\r\n          border\r\n          row-key=\"menuId\"\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"180px\" />\r\n          <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100px\">\r\n            <template slot-scope=\"scope\">\r\n              <svg-icon :icon-class=\"scope.row.icon\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"60px\" />\r\n          <el-table-column prop=\"permission\" label=\"权限标识\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover v-if=\"scope.row.sysApi.length>0\" trigger=\"hover\" placement=\"top\">\r\n                <el-table\r\n                  :data=\"scope.row.sysApi\"\r\n                  border\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-table-column\r\n                    prop=\"title\"\r\n                    label=\"title\"\r\n                    width=\"260px\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.type=='SYS' && scope.row.title!=''\"><el-tag type=\"success\">{{ '['+scope.row.type +'] '+ scope.row.title }}</el-tag></span>\r\n                      <span v-if=\"scope.row.type!='SYS' && scope.row.title!=''\"><el-tag type=\"\">{{ '['+scope.row.type +'] '+scope.row.title }}</el-tag></span>\r\n                      <span v-if=\"scope.row.title==''\"><el-tag type=\"danger\">暂无</el-tag></span>\r\n\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"path\"\r\n                    label=\"path\"\r\n                    width=\"270px\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <el-tag v-if=\"scope.row.action=='GET'\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='POST'\" type=\"success\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='PUT'\" type=\"warning\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='DELETE'\" type=\"danger\">{{ scope.row.action }}</el-tag>\r\n                      {{ scope.row.path }}\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                </el-table>\r\n                <div slot=\"reference\" class=\"name-wrapper\">\r\n                  <span v-if=\"scope.row.permission==''\">-</span>\r\n                  <span v-else>{{ scope.row.permission }}</span>\r\n                </div>\r\n              </el-popover>\r\n              <span v-else>\r\n                <span v-if=\"scope.row.permission==''\">-</span>\r\n                <span v-else>{{ scope.row.permission }}</span>\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"path\" label=\"组件路径\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.menuType=='A'\">{{ scope.row.path }}</span>\r\n              <span v-else>{{ scope.row.component }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"visible\" label=\"可见\" :formatter=\"visibleFormat\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.visible === '1' ? 'danger' : 'success'\"\r\n                disable-transitions\r\n              >{{ visibleFormat(scope.row) }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:add']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-plus\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 添加或修改菜单对话框 -->\r\n        <el-drawer\r\n          ref=\"drawer\"\r\n          :title=\"title\"\r\n          :before-close=\"cancel\"\r\n          :visible.sync=\"open\"\r\n          direction=\"rtl\"\r\n          custom-class=\"demo-drawer\"\r\n          size=\"830px\"\r\n        >\r\n          <div class=\"demo-drawer__content\">\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-position=\"top\" label-width=\"106px\">\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"parentId\">\r\n                    <span slot=\"label\">\r\n                      上级菜单\r\n                      <el-tooltip content=\"指当前菜单停靠的菜单归属\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <treeselect\r\n                      v-model=\"form.parentId\"\r\n                      :options=\"menuOptions\"\r\n                      :normalizer=\"normalizer\"\r\n                      :show-count=\"true\"\r\n                      placeholder=\"选择上级菜单\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item prop=\"title\">\r\n                    <span slot=\"label\">\r\n                      菜单标题\r\n                      <el-tooltip content=\"菜单位置显示的说明信息\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.title\" placeholder=\"请输入菜单标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item prop=\"sort\">\r\n                    <span slot=\"label\">\r\n                      显示排序\r\n                      <el-tooltip content=\"根据序号升序排列\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input-number v-model=\"form.sort\" controls-position=\"right\" :min=\"0\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"menuType\">\r\n                    <span slot=\"label\">\r\n                      菜单类型\r\n                      <el-tooltip content=\"包含目录：以及菜单或者菜单组，菜单：具体对应某一个页面，按钮：功能才做按钮；\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.menuType\">\r\n                      <el-radio label=\"M\">目录</el-radio>\r\n                      <el-radio label=\"C\">菜单</el-radio>\r\n                      <el-radio label=\"F\">按钮</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"菜单图标\">\r\n                    <el-popover\r\n                      placement=\"bottom-start\"\r\n                      width=\"460\"\r\n                      trigger=\"click\"\r\n                      @show=\"$refs['iconSelect'].reset()\"\r\n                    >\r\n                      <IconSelect ref=\"iconSelect\" @selected=\"selected\" />\r\n                      <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                        <svg-icon\r\n                          v-if=\"form.icon\"\r\n                          slot=\"prefix\"\r\n                          :icon-class=\"form.icon\"\r\n                          class=\"el-input__icon\"\r\n                          style=\"height: 32px;width: 16px;\"\r\n                        />\r\n                        <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\r\n                      </el-input>\r\n                    </el-popover>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'M' || form.menuType == 'C'\" prop=\"menuName\">\r\n                    <span slot=\"label\">\r\n                      路由名称\r\n                      <el-tooltip content=\"需要和页面name保持一致，对应页面即可选择缓存\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.menuName\" placeholder=\"请输入路由名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col v-if=\"form.menuType == 'M' || form.menuType == 'C'\" :span=\"12\">\r\n                  <el-form-item prop=\"component\">\r\n                    <span slot=\"label\">\r\n                      组件路径\r\n                      <el-tooltip content=\"菜单对应的具体vue页面文件路径views的下级路径/admin/sys-api/index；目录类型：填写Layout，如何有二级目录请参照日志目录填写；\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'M' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      是否外链\r\n                      <el-tooltip content=\"可以通过iframe打开指定地址\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.isFrame\">\r\n                      <el-radio label=\"0\">是</el-radio>\r\n                      <el-radio label=\"1\">否</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType != 'F'\" prop=\"path\">\r\n                    <span slot=\"label\">\r\n                      路由地址\r\n                      <el-tooltip content=\"访问此页面自定义的url地址，建议/开头书写，例如 /app-name/menu-name\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'F' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      权限标识\r\n                      <el-tooltip content=\"前端权限控制按钮是否显示\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.permission\" placeholder=\"请权限标识\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType != 'F'\">\r\n                    <span slot=\"label\">\r\n                      菜单状态\r\n                      <el-tooltip content=\"需要显示在菜单列表的菜单设置为显示，否则设置为隐藏\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.visible\">\r\n                      <el-radio\r\n                        v-for=\"dict in visibleOptions\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.value\"\r\n                      >{{ dict.label }}</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item v-if=\"form.menuType == 'F' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      api权限\r\n                      <el-tooltip content=\"配置在这个才做上需要使用到的接口，否则在设置用户角色时，接口将无权访问。\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-transfer\r\n                      v-model=\"form.apis\"\r\n                      style=\"text-align: left; display: inline-block\"\r\n                      filterable\r\n                      :props=\"{\r\n                        key: 'id',\r\n                        label: 'title'\r\n                      }\"\r\n                      :titles=\"['未授权', '已授权']\"\r\n                      :button-texts=\"['收回', '授权 ']\"\r\n                      :format=\"{\r\n                        noChecked: '${total}',\r\n                        hasChecked: '${checked}/${total}'\r\n                      }\"\r\n                      class=\"panel\"\r\n                      :data=\"sysapiList\"\r\n                      @change=\"handleChange\"\r\n                    >\r\n                      <span slot-scope=\"{ option }\">{{ option.title }}</span>\r\n                    </el-transfer>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n            <div class=\"demo-drawer__footer\">\r\n              <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n              <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n          </div>\r\n\r\n        </el-drawer>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from '@/api/admin/sys-menu'\r\nimport { listSysApi } from '@/api/admin/sys-api'\r\n\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\r\nimport IconSelect from '@/components/IconSelect'\r\n\r\nexport default {\r\n  name: 'SysMenuManage',\r\n  components: { Treeselect, IconSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      sysapiList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 菜单状态数据字典\r\n      visibleOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        title: undefined,\r\n        visible: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        apis: [],\r\n        sysApi: []\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        title: [{ required: true, message: '菜单标题不能为空', trigger: 'blur' }],\r\n        sort: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n\r\n    this.getApiList()\r\n    this.getDicts('sys_show_hide').then(response => {\r\n      this.visibleOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    handleChange(value, direction, movedKeys) {\r\n      console.log(value, direction, movedKeys)\r\n      const list = this.form.sysApi\r\n      this.form.apis = value\r\n      if (direction === 'right') {\r\n        for (let x = 0; x < movedKeys.length; x++) {\r\n          for (let index = 0; index < this.sysapiList.length; index++) {\r\n            const element = this.sysapiList[index]\r\n            if (element.id === movedKeys[x]) {\r\n              list.push(element)\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.form.sysApi = list\r\n      } else if (direction === 'left') {\r\n        const l = []\r\n        for (let index = 0; index < movedKeys.length; index++) {\r\n          const element = movedKeys[index]\r\n          for (let x = 0; x < list.length; x++) {\r\n            const e = list[x]\r\n            if (element !== e.id) {\r\n              l.push()\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.form.sysApi = l\r\n      }\r\n      // this.setApis(this.form.SysApi)\r\n      console.log(this.form.sysApi)\r\n    },\r\n    getApiList() {\r\n      this.loading = true\r\n      listSysApi({ 'pageSize': 10000, 'type': 'BUS' }).then(response => {\r\n        this.sysapiList = response.data.list\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    handleClose(done) {\r\n      // if (this.loading) {\r\n      //   return\r\n      // }\r\n      // this.$confirm('需要提交表单吗？')\r\n      //   .then(_ => {\r\n      //     this.loading = true\r\n      //     this.timer = setTimeout(() => {\r\n      //       done()\r\n      //       // 动画关闭需要一定的时间\r\n      //       setTimeout(() => {\r\n      //         this.loading = false\r\n      //       }, 400)\r\n      //     }, 1000)\r\n      //   })\r\n      //   .catch(_ => {})\r\n    },\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = response.data\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.title,\r\n        children: node.children\r\n      }\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = []\r\n        const menu = { menuId: 0, title: '主类目', children: [] }\r\n        menu.children = response.data\r\n        this.menuOptions.push(menu)\r\n      })\r\n    },\r\n    // 菜单显示状态字典翻译\r\n    visibleFormat(row) {\r\n      if (row.menuType === 'F') {\r\n        return '-- --'\r\n      }\r\n      return this.selectDictLabel(this.visibleOptions, row.visible)\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: undefined,\r\n        parentId: 0,\r\n        menuName: undefined,\r\n        icon: undefined,\r\n        menuType: 'M',\r\n        apis: [],\r\n        sort: 0,\r\n        action: this.form.menuType === 'A' ? this.form.action : '',\r\n        isFrame: '1',\r\n        visible: '0'\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset()\r\n      this.getTreeselect()\r\n      if (row != null) {\r\n        this.form.parentId = row.menuId\r\n      }\r\n      this.open = true\r\n      this.title = '添加菜单'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.getTreeselect()\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改菜单'\r\n      })\r\n    },\r\n    setApis(apiArray) {\r\n      var l = []\r\n      for (var index = 0; index < apiArray.length; index++) {\r\n        const element = apiArray[index]\r\n        l.push(element.id)\r\n      }\r\n      this.form.apis = l\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId !== undefined) {\r\n            updateMenu(this.form, this.form.menuId).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除名称为\"' + row.title + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        var Ids = (row.menuId && [row.menuId]) || this.ids\r\n        return delMenu({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"css\">\r\n.panel .el-transfer__buttons{\r\n  width: 150px;\r\n}\r\n.panel .el-transfer__buttons .el-button + .el-button{\r\n  margin-left:0;\r\n}\r\n.panel .el-transfer-panel{\r\n  width: 300px;\r\n}\r\n\r\n.el-col {\r\npadding: 0 5px;\r\n}\r\n.el-drawer__header{\r\nmargin-bottom: 0;\r\n}\r\n</style>\r\n"]}]}