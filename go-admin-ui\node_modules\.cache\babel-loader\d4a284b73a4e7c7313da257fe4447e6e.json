{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\dict\\type.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\dict\\type.js", "mtime": 1753924829926}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWtl+WFuOexu+Wei+WIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFR5cGUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvdHlwZScsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LlrZflhbjnsbvlnovor6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldFR5cGUoZGljdElkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9kaWN0L3R5cGUvJyArIGRpY3RJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5a2X5YW457G75Z6LCmV4cG9ydCBmdW5jdGlvbiBhZGRUeXBlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvdHlwZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55a2X5YW457G75Z6LCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVUeXBlKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvdHlwZS8nICsgZGF0YS5pZCwKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWtl+WFuOexu+WeiwpleHBvcnQgZnVuY3Rpb24gZGVsVHlwZShkaWN0SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvdHlwZScsCiAgICBtZXRob2Q6ICdkZWxldGUnLAogICAgZGF0YTogZGljdElkCiAgfSk7Cn0KCi8vIOWvvOWHuuWtl+WFuOexu+WeiwpleHBvcnQgZnVuY3Rpb24gZXhwb3J0VHlwZShxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvZGljdC90eXBlL2V4cG9ydCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDojrflj5blrZflhbjpgInmi6nmoYbliJfooagKZXhwb3J0IGZ1bmN0aW9uIG9wdGlvbnNlbGVjdCgpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RpY3QvdHlwZS1vcHRpb24tc2VsZWN0JywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "listType", "query", "url", "method", "params", "getType", "dictId", "addType", "data", "updateType", "id", "delType", "exportType", "optionselect"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/dict/type.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询字典类型列表\r\nexport function listType(query) {\r\n  return request({\r\n    url: '/api/v1/dict/type',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询字典类型详细\r\nexport function getType(dictId) {\r\n  return request({\r\n    url: '/api/v1/dict/type/' + dictId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增字典类型\r\nexport function addType(data) {\r\n  return request({\r\n    url: '/api/v1/dict/type',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改字典类型\r\nexport function updateType(data) {\r\n  return request({\r\n    url: '/api/v1/dict/type/' + data.id,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除字典类型\r\nexport function delType(dictId) {\r\n  return request({\r\n    url: '/api/v1/dict/type',\r\n    method: 'delete',\r\n    data: dictId\r\n  })\r\n}\r\n\r\n// 导出字典类型\r\nexport function exportType(query) {\r\n  return request({\r\n    url: '/api/v1/dict/type/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获取字典选择框列表\r\nexport function optionselect() {\r\n  return request({\r\n    url: '/api/v1/dict/type-option-select',\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGI,MAAM;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGM,IAAI,CAACE,EAAE;IACnCP,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACL,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEF;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,UAAUA,CAACX,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,YAAYA,CAAA,EAAG;EAC7B,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}