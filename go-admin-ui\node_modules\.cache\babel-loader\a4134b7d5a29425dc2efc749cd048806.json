{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-role.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-role.js", "mtime": 1753924829932}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouinkuiJsuWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFJvbGUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3JvbGUnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6KeS6Imy6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXRSb2xlKHJvbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZS8nICsgcm9sZUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7op5LoibIKZXhwb3J0IGZ1bmN0aW9uIGFkZFJvbGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS56KeS6ImyCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVSb2xlKGRhdGEsIHJvbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZS8nICsgcm9sZUlkLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g6KeS6Imy5pWw5o2u5p2D6ZmQCmV4cG9ydCBmdW5jdGlvbiBkYXRhU2NvcGUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZWRhdGFzY29wZScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDop5LoibLnirbmgIHkv67mlLkKZXhwb3J0IGZ1bmN0aW9uIGNoYW5nZVJvbGVTdGF0dXMocm9sZUlkLCBzdGF0dXMpIHsKICB2YXIgZGF0YSA9IHsKICAgIHJvbGVJZDogcm9sZUlkLAogICAgc3RhdHVzOiBzdGF0dXMKICB9OwogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZS1zdGF0dXMnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6KeS6ImyCmV4cG9ydCBmdW5jdGlvbiBkZWxSb2xlKHJvbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZScsCiAgICBtZXRob2Q6ICdkZWxldGUnLAogICAgZGF0YTogcm9sZUlkCiAgfSk7Cn0KZXhwb3J0IGZ1bmN0aW9uIGdldExpc3Ryb2xlKGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9tZW51L3JvbGUvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiBnZXRSb3V0ZXMoKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9tZW51cm9sZScsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIGV4cG9ydCBmdW5jdGlvbiBnZXRNZW51TmFtZXMoKSB7Ci8vICAgcmV0dXJuIHJlcXVlc3QoewovLyAgICAgdXJsOiAnL2FwaS92MS9tZW51aWRzJywKLy8gICAgIG1ldGhvZDogJ2dldCcKLy8gICB9KQovLyB9"}, {"version": 3, "names": ["request", "listRole", "query", "url", "method", "params", "getRole", "roleId", "addRole", "data", "updateRole", "dataScope", "changeRoleStatus", "status", "delRole", "getListrole", "id", "getRoutes"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-role.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询角色列表\r\nexport function listRole(query) {\r\n  return request({\r\n    url: '/api/v1/role',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询角色详细\r\nexport function getRole(roleId) {\r\n  return request({\r\n    url: '/api/v1/role/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增角色\r\nexport function addRole(data) {\r\n  return request({\r\n    url: '/api/v1/role',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改角色\r\nexport function updateRole(data, roleId) {\r\n  return request({\r\n    url: '/api/v1/role/' + roleId,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 角色数据权限\r\nexport function dataScope(data) {\r\n  return request({\r\n    url: '/api/v1/roledatascope',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 角色状态修改\r\nexport function changeRoleStatus(roleId, status) {\r\n  const data = {\r\n    roleId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/api/v1/role-status',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除角色\r\nexport function delRole(roleId) {\r\n  return request({\r\n    url: '/api/v1/role',\r\n    method: 'delete',\r\n    data: roleId\r\n  })\r\n}\r\n\r\nexport function getListrole(id) {\r\n  return request({\r\n    url: '/api/v1/menu/role/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getRoutes() {\r\n  return request({\r\n    url: '/api/v1/menurole',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// export function getMenuNames() {\r\n//   return request({\r\n//     url: '/api/v1/menuids',\r\n//     method: 'get'\r\n//   })\r\n// }\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAEF,MAAM,EAAE;EACvC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,gBAAgBA,CAACL,MAAM,EAAEM,MAAM,EAAE;EAC/C,IAAMJ,IAAI,GAAG;IACXF,MAAM,EAANA,MAAM;IACNM,MAAM,EAANA;EACF,CAAC;EACD,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEF;EACR,CAAC,CAAC;AACJ;AAEA,OAAO,SAASQ,WAAWA,CAACC,EAAE,EAAE;EAC9B,OAAOhB,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGa,EAAE;IAC9BZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASa,SAASA,CAAA,EAAG;EAC1B,OAAOjB,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}]}