{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\AutoWidthOption.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\AutoWidthOption.vue", "mtime": 1753924830409}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgdmFsdWU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIGF1dG9XaWR0aDogew0KICAgICAgZ2V0KCkgew0KICAgICAgICByZXR1cm4gdGhpcy52YWx1ZQ0KICAgICAgfSwNCiAgICAgIHNldCh2YWwpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgnaW5wdXQnLCB2YWwpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\AutoWidthOption.vue"], "names": [], "mappings": ";AAeA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACzB;IACF;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/excel/components/AutoWidthOption.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div style=\"display:inline-block;\">\r\n    <label class=\"radio-label\">Cell Auto-Width: </label>\r\n    <el-radio-group v-model=\"autoWidth\">\r\n      <el-radio :label=\"true\" border>\r\n        True\r\n      </el-radio>\r\n      <el-radio :label=\"false\" border>\r\n        False\r\n      </el-radio>\r\n    </el-radio-group>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  computed: {\r\n    autoWidth: {\r\n      get() {\r\n        return this.value\r\n      },\r\n      set(val) {\r\n        this.$emit('input', val)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}