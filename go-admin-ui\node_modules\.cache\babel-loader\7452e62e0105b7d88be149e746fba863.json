{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue", "mtime": 1753924830064}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["constantRoutes", "data", "visibleNumber", "isFrist", "computed", "topMenus", "routers", "map", "menu", "_objectSpread", "children", "undefined", "$store", "state", "permission", "topbarRouters", "childrenMenus", "router", "item", "parentPath", "path", "push", "concat", "activeMenu", "$route", "activePath", "lastIndexOf", "tmpPath", "substring", "length", "indexOf", "activeRoutes", "mounted", "setVisibleNumber", "methods", "width", "document", "body", "getBoundingClientRect", "<PERSON><PERSON><PERSON><PERSON>", "$el", "menuItemNodes", "menuWidth", "Array", "from", "i", "parseInt", "toFixed", "handleSelect", "key", "keyP<PERSON>", "window", "open", "routes", "commit"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue"], "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item\r\n        v-if=\"index < visibleNumber\"\r\n        :key=\"index\"\r\n        :index=\"item.path\"\r\n      ><svg-icon :icon-class=\"item.meta.icon\" />\r\n        {{ item.meta.title }}</el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu v-if=\"topMenus.length > visibleNumber\" index=\"more\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          v-if=\"index >= visibleNumber\"\r\n          :key=\"index\"\r\n          :index=\"item.path\"\r\n        ><svg-icon :icon-class=\"item.meta.icon\" />\r\n          {{ item.meta.title }}</el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from '@/router'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 是否为首次加载\r\n      isFrist: false\r\n    }\r\n  },\r\n  computed: {\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      return this.routers.map((menu) => ({\r\n        ...menu,\r\n        children: undefined\r\n      }))\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = []\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            router.children[item].parentPath = router.path\r\n          }\r\n          childrenMenus.push(router.children[item])\r\n        }\r\n      })\r\n      return constantRoutes.concat(childrenMenus)\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path\r\n      let activePath = this.routers[0].path\r\n      if (path.lastIndexOf('/') > 0) {\r\n        const tmpPath = path.substring(1, path.length)\r\n        activePath = '/' + tmpPath.substring(0, tmpPath.indexOf('/'))\r\n      } else if (path === '/index' || path === '') {\r\n        if (!this.isFrist) {\r\n          // eslint-disable-next-line vue/no-side-effects-in-computed-properties\r\n          this.isFrist = true\r\n        } else {\r\n          activePath = 'index'\r\n        }\r\n      }\r\n      this.activeRoutes(activePath)\r\n      return activePath\r\n    }\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber()\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width - 200\r\n      const elWidth = this.$el.getBoundingClientRect().width\r\n      const menuItemNodes = this.$el.children\r\n      const menuWidth = Array.from(menuItemNodes).map(\r\n        (i) => i.getBoundingClientRect().width\r\n      )\r\n      this.visibleNumber = (\r\n        parseInt(width - elWidth) / parseInt(menuWidth)\r\n      ).toFixed(0)\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      if (key.indexOf('http://') !== -1 || key.indexOf('https://') !== -1) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, '_blank')\r\n      } else {\r\n        this.activeRoutes(key)\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = []\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key === item.parentPath || (key === 'index' && item.path === '')) {\r\n            routes.push(item)\r\n          }\r\n        })\r\n      }\r\n      this.$store.commit('permission/SET_SIDEBAR_ROUTERS', routes)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  margin: 0;\r\n  border-bottom: 3px solid transparent;\r\n  color: #999093;\r\n  padding: 0 5px;\r\n  margin: 0 10px;\r\n}\r\n\r\n.el-menu--horizontal > .el-menu-item.is-active {\r\n  border-bottom: 3px solid #409eff;\r\n  color: #303133;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AA+BA,SAASA,cAAa,QAAS,UAAS;AAExC,eAAe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,aAAa,EAAE,CAAC;MAChB;MACAC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,QAAQ,EAAE;IACR;IACAC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,OAAO,CAACC,GAAG,CAAC,UAACC,IAAI;QAAA,OAAAC,aAAA,CAAAA,aAAA,KACxBD,IAAI;UACPE,QAAQ,EAAEC;QAAQ;MAAA,CAClB;IACJ,CAAC;IACD;IACAL,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACM,MAAM,CAACC,KAAK,CAACC,UAAU,CAACC,aAAY;IAClD,CAAC;IACD;IACAC,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAIA,aAAY,GAAI,EAAC;MACrB,IAAI,CAACV,OAAO,CAACC,GAAG,CAAC,UAACU,MAAM,EAAK;QAC3B,KAAK,IAAIC,IAAG,IAAKD,MAAM,CAACP,QAAQ,EAAE;UAChC,IAAIO,MAAM,CAACP,QAAQ,CAACQ,IAAI,CAAC,CAACC,UAAS,KAAMR,SAAS,EAAE;YAClDM,MAAM,CAACP,QAAQ,CAACQ,IAAI,CAAC,CAACC,UAAS,GAAIF,MAAM,CAACG,IAAG;UAC/C;UACAJ,aAAa,CAACK,IAAI,CAACJ,MAAM,CAACP,QAAQ,CAACQ,IAAI,CAAC;QAC1C;MACF,CAAC;MACD,OAAOlB,cAAc,CAACsB,MAAM,CAACN,aAAa;IAC5C,CAAC;IACD;IACAO,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAMH,IAAG,GAAI,IAAI,CAACI,MAAM,CAACJ,IAAG;MAC5B,IAAIK,UAAS,GAAI,IAAI,CAACnB,OAAO,CAAC,CAAC,CAAC,CAACc,IAAG;MACpC,IAAIA,IAAI,CAACM,WAAW,CAAC,GAAG,IAAI,CAAC,EAAE;QAC7B,IAAMC,OAAM,GAAIP,IAAI,CAACQ,SAAS,CAAC,CAAC,EAAER,IAAI,CAACS,MAAM;QAC7CJ,UAAS,GAAI,GAAE,GAAIE,OAAO,CAACC,SAAS,CAAC,CAAC,EAAED,OAAO,CAACG,OAAO,CAAC,GAAG,CAAC;MAC9D,OAAO,IAAIV,IAAG,KAAM,QAAO,IAAKA,IAAG,KAAM,EAAE,EAAE;QAC3C,IAAI,CAAC,IAAI,CAACjB,OAAO,EAAE;UACjB;UACA,IAAI,CAACA,OAAM,GAAI,IAAG;QACpB,OAAO;UACLsB,UAAS,GAAI,OAAM;QACrB;MACF;MACA,IAAI,CAACM,YAAY,CAACN,UAAU;MAC5B,OAAOA,UAAS;IAClB;EACF,CAAC;EACDO,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,gBAAgB,CAAC;EACxB,CAAC;EACDC,OAAO,EAAE;IACP;IACAD,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAME,KAAI,GAAIC,QAAQ,CAACC,IAAI,CAACC,qBAAqB,CAAC,CAAC,CAACH,KAAI,GAAI,GAAE;MAC9D,IAAMI,OAAM,GAAI,IAAI,CAACC,GAAG,CAACF,qBAAqB,CAAC,CAAC,CAACH,KAAI;MACrD,IAAMM,aAAY,GAAI,IAAI,CAACD,GAAG,CAAC9B,QAAO;MACtC,IAAMgC,SAAQ,GAAIC,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC,CAAClC,GAAG,CAC7C,UAACsC,CAAC;QAAA,OAAKA,CAAC,CAACP,qBAAqB,CAAC,CAAC,CAACH,KAAI;MAAA,CACvC;MACA,IAAI,CAACjC,aAAY,GAAI,CACnB4C,QAAQ,CAACX,KAAI,GAAII,OAAO,IAAIO,QAAQ,CAACJ,SAAS,GAC9CK,OAAO,CAAC,CAAC;IACb,CAAC;IACD;IACAC,YAAY,WAAZA,YAAYA,CAACC,GAAG,EAAEC,OAAO,EAAE;MACzB,IAAID,GAAG,CAACnB,OAAO,CAAC,SAAS,MAAM,CAAC,KAAKmB,GAAG,CAACnB,OAAO,CAAC,UAAU,MAAM,CAAC,CAAC,EAAE;QACnE;QACAqB,MAAM,CAACC,IAAI,CAACH,GAAG,EAAE,QAAQ;MAC3B,OAAO;QACL,IAAI,CAAClB,YAAY,CAACkB,GAAG;MACvB;IACF,CAAC;IACD;IACAlB,YAAY,WAAZA,YAAYA,CAACkB,GAAG,EAAE;MAChB,IAAII,MAAK,GAAI,EAAC;MACd,IAAI,IAAI,CAACrC,aAAY,IAAK,IAAI,CAACA,aAAa,CAACa,MAAK,GAAI,CAAC,EAAE;QACvD,IAAI,CAACb,aAAa,CAACT,GAAG,CAAC,UAACW,IAAI,EAAK;UAC/B,IAAI+B,GAAE,KAAM/B,IAAI,CAACC,UAAS,IAAM8B,GAAE,KAAM,OAAM,IAAK/B,IAAI,CAACE,IAAG,KAAM,EAAG,EAAE;YACpEiC,MAAM,CAAChC,IAAI,CAACH,IAAI;UAClB;QACF,CAAC;MACH;MACA,IAAI,CAACN,MAAM,CAAC0C,MAAM,CAAC,gCAAgC,EAAED,MAAM;IAC7D;EACF;AACF", "ignoreList": []}]}