{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\index.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\index.js", "mtime": 1753924830224}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucmVkdWNlLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5yZWR1Y2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiOwppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmltcG9ydCBWdWV4IGZyb20gJ3Z1ZXgnOwppbXBvcnQgZ2V0dGVycyBmcm9tICcuL2dldHRlcnMnOwpWdWUudXNlKFZ1ZXgpOwoKLy8gaHR0cHM6Ly93ZWJwYWNrLmpzLm9yZy9ndWlkZXMvZGVwZW5kZW5jeS1tYW5hZ2VtZW50LyNyZXF1aXJlY29udGV4dAp2YXIgbW9kdWxlc0ZpbGVzID0gcmVxdWlyZS5jb250ZXh0KCcuL21vZHVsZXMnLCB0cnVlLCAvXC5qcyQvKTsKCi8vIHlvdSBkbyBub3QgbmVlZCBgaW1wb3J0IGFwcCBmcm9tICcuL21vZHVsZXMvYXBwJ2AKLy8gaXQgd2lsbCBhdXRvIHJlcXVpcmUgYWxsIHZ1ZXggbW9kdWxlIGZyb20gbW9kdWxlcyBmaWxlCnZhciBtb2R1bGVzID0gbW9kdWxlc0ZpbGVzLmtleXMoKS5yZWR1Y2UoZnVuY3Rpb24gKG1vZHVsZXMsIG1vZHVsZVBhdGgpIHsKICAvLyBzZXQgJy4vYXBwLmpzJyA9PiAnYXBwJwogIHZhciBtb2R1bGVOYW1lID0gbW9kdWxlUGF0aC5yZXBsYWNlKC9eXC5cLyguKilcLlx3KyQvLCAnJDEnKTsKICB2YXIgdmFsdWUgPSBtb2R1bGVzRmlsZXMobW9kdWxlUGF0aCk7CiAgbW9kdWxlc1ttb2R1bGVOYW1lXSA9IHZhbHVlLmRlZmF1bHQ7CiAgcmV0dXJuIG1vZHVsZXM7Cn0sIHt9KTsKdmFyIHN0b3JlID0gbmV3IFZ1ZXguU3RvcmUoewogIG1vZHVsZXM6IG1vZHVsZXMsCiAgZ2V0dGVyczogZ2V0dGVycwp9KTsKZXhwb3J0IGRlZmF1bHQgc3RvcmU7"}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "getters", "use", "modulesFiles", "require", "context", "modules", "keys", "reduce", "modulePath", "moduleName", "replace", "value", "default", "store", "Store"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Vuex from 'vuex'\r\nimport getters from './getters'\r\n\r\nVue.use(Vuex)\r\n\r\n// https://webpack.js.org/guides/dependency-management/#requirecontext\r\nconst modulesFiles = require.context('./modules', true, /\\.js$/)\r\n\r\n// you do not need `import app from './modules/app'`\r\n// it will auto require all vuex module from modules file\r\nconst modules = modulesFiles.keys().reduce((modules, modulePath) => {\r\n  // set './app.js' => 'app'\r\n  const moduleName = modulePath.replace(/^\\.\\/(.*)\\.\\w+$/, '$1')\r\n  const value = modulesFiles(modulePath)\r\n  modules[moduleName] = value.default\r\n  return modules\r\n}, {})\r\n\r\nconst store = new Vuex.Store({\r\n  modules,\r\n  getters\r\n})\r\n\r\nexport default store\r\n"], "mappings": ";;;;;;;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,WAAW;AAE/BF,GAAG,CAACG,GAAG,CAACF,IAAI,CAAC;;AAEb;AACA,IAAMG,YAAY,GAAGC,OAAO,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;;AAEhE;AACA;AACA,IAAMC,OAAO,GAAGH,YAAY,CAACI,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,UAACF,OAAO,EAAEG,UAAU,EAAK;EAClE;EACA,IAAMC,UAAU,GAAGD,UAAU,CAACE,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC;EAC9D,IAAMC,KAAK,GAAGT,YAAY,CAACM,UAAU,CAAC;EACtCH,OAAO,CAACI,UAAU,CAAC,GAAGE,KAAK,CAACC,OAAO;EACnC,OAAOP,OAAO;AAChB,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,IAAMQ,KAAK,GAAG,IAAId,IAAI,CAACe,KAAK,CAAC;EAC3BT,OAAO,EAAPA,OAAO;EACPL,OAAO,EAAPA;AACF,CAAC,CAAC;AAEF,eAAea,KAAK", "ignoreList": []}]}