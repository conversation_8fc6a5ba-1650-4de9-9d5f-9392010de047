{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-config.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-config.js", "mtime": 1753924829928}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouWPguaVsOWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdENvbmZpZyhxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvY29uZmlnJywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWPguaVsOivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0Q29uZmlnKGNvbmZpZ0lkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9jb25maWcvJyArIGNvbmZpZ0lkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmoLnmja7lj4LmlbDplK7lkI3mn6Xor6Llj4LmlbDlgLwKZXhwb3J0IGZ1bmN0aW9uIGdldENvbmZpZ0tleShjb25maWdLZXkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2NvbmZpZ0tleS8nICsgY29uZmlnS2V5LAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lj4LmlbDphY3nva4KZXhwb3J0IGZ1bmN0aW9uIGFkZENvbmZpZyhkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9jb25maWcnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueWPguaVsOmFjee9rgpleHBvcnQgZnVuY3Rpb24gdXBkYXRlQ29uZmlnKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2NvbmZpZy8nICsgZGF0YS5pZCwKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWPguaVsOmFjee9rgpleHBvcnQgZnVuY3Rpb24gZGVsQ29uZmlnKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2NvbmZpZycsCiAgICBtZXRob2Q6ICdkZWxldGUnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiBnZXRTZXRDb25maWcocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3NldC1jb25maWcnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVTZXRDb25maWcoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvc2V0LWNvbmZpZycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9"}, {"version": 3, "names": ["request", "listConfig", "query", "url", "method", "params", "getConfig", "configId", "getConfigKey", "config<PERSON><PERSON>", "addConfig", "data", "updateConfig", "id", "delConfig", "getSetConfig", "updateSetConfig"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-config.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询参数列表\r\nexport function listConfig(query) {\r\n  return request({\r\n    url: '/api/v1/config',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询参数详细\r\nexport function getConfig(configId) {\r\n  return request({\r\n    url: '/api/v1/config/' + configId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据参数键名查询参数值\r\nexport function getConfigKey(configKey) {\r\n  return request({\r\n    url: '/api/v1/configKey/' + configKey,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增参数配置\r\nexport function addConfig(data) {\r\n  return request({\r\n    url: '/api/v1/config',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改参数配置\r\nexport function updateConfig(data) {\r\n  return request({\r\n    url: '/api/v1/config/' + data.id,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除参数配置\r\nexport function delConfig(data) {\r\n  return request({\r\n    url: '/api/v1/config',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function getSetConfig(query) {\r\n  return request({\r\n    url: '/api/v1/set-config',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function updateSetConfig(data) {\r\n  return request({\r\n    url: '/api/v1/set-config',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB,GAAGM,SAAS;IACrCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB,GAAGQ,IAAI,CAACE,EAAE;IAChCT,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,SAASA,CAACH,IAAI,EAAE;EAC9B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,QAAQ;IAChBO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEA,OAAO,SAASI,YAAYA,CAACb,KAAK,EAAE;EAClC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASY,eAAeA,CAACL,IAAI,EAAE;EACpC,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}