{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniBar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniBar\\index.vue", "mtime": 1753924830052}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBtb21lbnQgZnJvbSAnbW9tZW50JzsKdmFyIF9kYXRhID0gW107CnZhciBiZWdpbkRheSA9IG5ldyBEYXRlKCkuZ2V0VGltZSgpOwpmb3IgKHZhciBpID0gMDsgaSA8IDEwOyBpKyspIHsKICBfZGF0YS5wdXNoKHsKICAgIHg6IG1vbWVudChuZXcgRGF0ZShiZWdpbkRheSArIDEwMDAgKiA2MCAqIDYwICogMjQgKiBpKSkuZm9ybWF0KCdZWVlZLU1NLUREJyksCiAgICB5OiBNYXRoLnJvdW5kKE1hdGgucmFuZG9tKCkgKiAxMCkKICB9KTsKfQp2YXIgdG9vbHRpcCA9IFsneCp5JywgZnVuY3Rpb24gKHgsIHkpIHsKICByZXR1cm4gewogICAgbmFtZTogeCwKICAgIHZhbHVlOiB5CiAgfTsKfV07CnZhciBzY2FsZSA9IFt7CiAgZGF0YUtleTogJ3gnLAogIG1pbjogMgp9LCB7CiAgZGF0YUtleTogJ3knLAogIHRpdGxlOiAn5pe26Ze0JywKICBtaW46IDEsCiAgbWF4OiAzMAp9XTsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdNaW5pQmFyJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGF0YTogX2RhdGEsCiAgICAgIHRvb2x0aXA6IHRvb2x0aXAsCiAgICAgIHNjYWxlOiBzY2FsZSwKICAgICAgaGVpZ2h0OiAxMDAKICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["moment", "data", "beginDay", "Date", "getTime", "i", "push", "x", "format", "y", "Math", "round", "random", "tooltip", "name", "value", "scale", "dataKey", "min", "title", "max", "height"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniBar\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"antv-chart-mini\">\r\n    <div class=\"chart-wrapper\" :style=\"{ height: 46 }\">\r\n      <v-chart :force-fit=\"true\" :height=\"height\" :data=\"data\" :padding=\"[36, 5, 18, 5]\">\r\n        <v-tooltip />\r\n        <v-bar position=\"x*y\" />\r\n      </v-chart>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport moment from 'moment'\r\nconst data = []\r\nconst beginDay = new Date().getTime()\r\nfor (let i = 0; i < 10; i++) {\r\n  data.push({\r\n    x: moment(new Date(beginDay + 1000 * 60 * 60 * 24 * i)).format('YYYY-MM-DD'),\r\n    y: Math.round(Math.random() * 10)\r\n  })\r\n}\r\nconst tooltip = [\r\n  'x*y',\r\n  (x, y) => ({\r\n    name: x,\r\n    value: y\r\n  })\r\n]\r\nconst scale = [{\r\n  dataKey: 'x',\r\n  min: 2\r\n}, {\r\n  dataKey: 'y',\r\n  title: '时间',\r\n  min: 1,\r\n  max: 30\r\n}]\r\nexport default {\r\n  name: 'MiniBar',\r\n  data() {\r\n    return {\r\n      data,\r\n      tooltip,\r\n      scale,\r\n      height: 100\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .antv-chart-mini {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  .chart-wrapper {\r\n    position: absolute;\r\n    bottom: -28px;\r\n    width: 100%;\r\n\r\n/*    margin: 0 -5px;\r\n    overflow: hidden;*/\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";AAYA,OAAOA,MAAK,MAAO,QAAO;AAC1B,IAAMC,KAAG,GAAI,EAAC;AACd,IAAMC,QAAO,GAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;AACpC,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;EAC3BJ,KAAI,CAACK,IAAI,CAAC;IACRC,CAAC,EAAEP,MAAM,CAAC,IAAIG,IAAI,CAACD,QAAO,GAAI,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAIG,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,YAAY,CAAC;IAC5EC,CAAC,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE;EAClC,CAAC;AACH;AACA,IAAMC,OAAM,GAAI,CACd,KAAK,EACL,UAACN,CAAC,EAAEE,CAAC;EAAA,OAAM;IACTK,IAAI,EAAEP,CAAC;IACPQ,KAAK,EAAEN;EACT,CAAC;AAAA,EACH;AACA,IAAMO,KAAI,GAAI,CAAC;EACbC,OAAO,EAAE,GAAG;EACZC,GAAG,EAAE;AACP,CAAC,EAAE;EACDD,OAAO,EAAE,GAAG;EACZE,KAAK,EAAE,IAAI;EACXD,GAAG,EAAE,CAAC;EACNE,GAAG,EAAE;AACP,CAAC;AACD,eAAe;EACbN,IAAI,EAAE,SAAS;EACfb,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLA,IAAI,EAAJA,KAAI;MACJY,OAAO,EAAPA,OAAO;MACPG,KAAK,EAALA,KAAK;MACLK,MAAM,EAAE;IACV;EACF;AACF", "ignoreList": []}]}