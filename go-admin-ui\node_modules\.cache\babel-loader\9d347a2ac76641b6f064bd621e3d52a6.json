{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1753924830213}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovRGVza3RvcC9Hb0FkbWluL2dvLWFkbWluLXVpL25vZGVfbW9kdWxlcy8uc3RvcmUvQGJhYmVsK3J1bnRpbWVANy4yOC4yL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwppbXBvcnQgTG9nbyBmcm9tICcuL0xvZ28nOwppbXBvcnQgU2lkZWJhckl0ZW0gZnJvbSAnLi9TaWRlYmFySXRlbSc7CmltcG9ydCBfdmFyaWFibGVzIGZyb20gJ0Avc3R5bGVzL3ZhcmlhYmxlcy5zY3NzJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFNpZGViYXJJdGVtOiBTaWRlYmFySXRlbSwKICAgIExvZ286IExvZ28KICB9LAogIGNvbXB1dGVkOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIG1hcEdldHRlcnMoWydzaWRlYmFyUm91dGVycycsICdzaWRlYmFyJ10pKSwge30sIHsKICAgIGFjdGl2ZU1lbnU6IGZ1bmN0aW9uIGFjdGl2ZU1lbnUoKSB7CiAgICAgIHZhciByb3V0ZSA9IHRoaXMuJHJvdXRlOwogICAgICB2YXIgbWV0YSA9IHJvdXRlLm1ldGEsCiAgICAgICAgcGF0aCA9IHJvdXRlLnBhdGg7CiAgICAgIC8vIGlmIHNldCBwYXRoLCB0aGUgc2lkZWJhciB3aWxsIGhpZ2hsaWdodCB0aGUgcGF0aCB5b3Ugc2V0CiAgICAgIGlmIChtZXRhLmFjdGl2ZU1lbnUpIHsKICAgICAgICByZXR1cm4gbWV0YS5hY3RpdmVNZW51OwogICAgICB9CiAgICAgIHJldHVybiBwYXRoOwogICAgfSwKICAgIHNob3dMb2dvOiBmdW5jdGlvbiBzaG93TG9nbygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNpZGViYXJMb2dvOwogICAgfSwKICAgIHZhcmlhYmxlczogZnVuY3Rpb24gdmFyaWFibGVzKCkgewogICAgICByZXR1cm4gX3ZhcmlhYmxlczsKICAgIH0sCiAgICBpc0NvbGxhcHNlOiBmdW5jdGlvbiBpc0NvbGxhcHNlKCkgewogICAgICByZXR1cm4gIXRoaXMuc2lkZWJhci5vcGVuZWQ7CiAgICB9CiAgfSksCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHt9Cn07"}, {"version": 3, "names": ["mapGetters", "Logo", "SidebarItem", "variables", "components", "computed", "_objectSpread", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "isCollapse", "sidebar", "opened", "mounted", "methods"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"{'has-logo':showLogo}\">\r\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\r\n    <el-scrollbar wrap-class=\"scrollbar-wrapper\">\r\n      <el-menu\r\n        :default-active=\"activeMenu\"\r\n        :collapse=\"isCollapse\"\r\n        :background-color=\" $store.state.settings.themeStyle === 'light' ? variables.menuLightBg : variables.menuBg\"\r\n        :text-color=\"$store.state.settings.themeStyle === 'light' ? 'rgba(0,0,0,.65)' : '#fff'\"\r\n        :active-text-color=\"$store.state.settings.theme\"\r\n        :unique-opened=\"true\"\r\n        :collapse-transition=\"true\"\r\n        mode=\"vertical\"\r\n      >\r\n        <sidebar-item\r\n          v-for=\"(route) in sidebarRouters\"\r\n          :key=\"route.path\"\r\n          :item=\"route\"\r\n          :base-path=\"route.path\"\r\n        />\r\n\r\n      </el-menu>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Logo from './Logo'\r\nimport SidebarItem from './SidebarItem'\r\nimport variables from '@/styles/variables.scss'\r\n\r\nexport default {\r\n  components: { SidebarItem, Logo },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebarRouters',\r\n      'sidebar'\r\n    ]),\r\n    activeMenu() {\r\n      const route = this.$route\r\n      const { meta, path } = route\r\n      // if set path, the sidebar will highlight the path you set\r\n      if (meta.activeMenu) {\r\n        return meta.activeMenu\r\n      }\r\n      return path\r\n    },\r\n    showLogo() {\r\n      return this.$store.state.settings.sidebarLogo\r\n    },\r\n    variables() {\r\n      return variables\r\n    },\r\n    isCollapse() {\r\n      return !this.sidebar.opened\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n  },\r\n  methods: {\r\n\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";AA2BA,SAASA,UAAS,QAAS,MAAK;AAChC,OAAOC,IAAG,MAAO,QAAO;AACxB,OAAOC,WAAU,MAAO,eAAc;AACtC,OAAOC,UAAQ,MAAO,yBAAwB;AAE9C,eAAe;EACbC,UAAU,EAAE;IAAEF,WAAW,EAAXA,WAAW;IAAED,IAAG,EAAHA;EAAK,CAAC;EACjCI,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHN,UAAU,CAAC,CACZ,gBAAgB,EAChB,SAAQ,CACT,CAAC;IACFO,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAMC,KAAI,GAAI,IAAI,CAACC,MAAK;MACxB,IAAQC,IAAI,GAAWF,KAAI,CAAnBE,IAAI;QAAEC,IAAG,GAAMH,KAAI,CAAbG,IAAG;MACjB;MACA,IAAID,IAAI,CAACH,UAAU,EAAE;QACnB,OAAOG,IAAI,CAACH,UAAS;MACvB;MACA,OAAOI,IAAG;IACZ,CAAC;IACDC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,CAACC,WAAU;IAC9C,CAAC;IACDb,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,OAAOA,UAAQ;IACjB,CAAC;IACDc,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAO,CAAC,IAAI,CAACC,OAAO,CAACC,MAAK;IAC5B;EAAA,EACD;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAEV,CAAC;EACDC,OAAO,EAAE,CAET;AACF", "ignoreList": []}]}