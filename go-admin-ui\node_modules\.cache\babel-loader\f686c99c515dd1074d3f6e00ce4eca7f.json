{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-api.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-api.js", "mtime": 1753924829927}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivolN5c0FwaeWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdFN5c0FwaShxdWVyeSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvc3lzLWFwaScsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6JTeXNBcGnor6bnu4YKZXhwb3J0IGZ1bmN0aW9uIGdldFN5c0FwaShpZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvc3lzLWFwaS8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinlN5c0FwaQpleHBvcnQgZnVuY3Rpb24gYWRkU3lzQXBpKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3N5cy1hcGknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuVN5c0FwaQpleHBvcnQgZnVuY3Rpb24gdXBkYXRlU3lzQXBpKGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3N5cy1hcGkvJyArIGRhdGEuaWQsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaRTeXNBcGkKZXhwb3J0IGZ1bmN0aW9uIGRlbFN5c0FwaShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9zeXMtYXBpJywKICAgIG1ldGhvZDogJ2RlbGV0ZScsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0="}, {"version": 3, "names": ["request", "listSysApi", "query", "url", "method", "params", "getSysApi", "id", "addSysApi", "data", "updateSysApi", "delSysApi"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-api.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询SysApi列表\r\nexport function listSysApi(query) {\r\n  return request({\r\n    url: '/api/v1/sys-api',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询SysApi详细\r\nexport function getSysApi(id) {\r\n  return request({\r\n    url: '/api/v1/sys-api/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增SysApi\r\nexport function addSysApi(data) {\r\n  return request({\r\n    url: '/api/v1/sys-api',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改SysApi\r\nexport function updateSysApi(data) {\r\n  return request({\r\n    url: '/api/v1/sys-api/' + data.id,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除SysApi\r\nexport function delSysApi(data) {\r\n  return request({\r\n    url: '/api/v1/sys-api',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB,GAAGI,EAAE;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB,GAAGM,IAAI,CAACF,EAAE;IACjCH,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,QAAQ;IAChBK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}