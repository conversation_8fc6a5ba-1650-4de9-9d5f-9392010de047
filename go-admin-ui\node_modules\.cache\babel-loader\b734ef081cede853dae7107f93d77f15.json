{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\get-page-title.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\get-page-title.js", "mtime": 1753924830257}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0IHN0b3JhZ2UgZnJvbSAnQC91dGlscy9zdG9yYWdlJzsKZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0UGFnZVRpdGxlKHBhZ2VUaXRsZSkgewogIHZhciBhcHBfaW5mbyA9IHN0b3JhZ2UuZ2V0KCdhcHBfaW5mbycpOwogIHZhciB0aXRsZSA9IGFwcF9pbmZvID8gYXBwX2luZm8uc3lzX2FwcF9uYW1lIDogJ2dvLWFkbWluIOWQjuWPsOeuoeeQhuezu+e7nyc7CiAgaWYgKHBhZ2VUaXRsZSkgewogICAgcmV0dXJuICIiLmNvbmNhdChwYWdlVGl0bGUsICIgLSAiKS5jb25jYXQodGl0bGUpOwogIH0KICByZXR1cm4gIiIuY29uY2F0KHRpdGxlKTsKfQ=="}, {"version": 3, "names": ["storage", "getPageTitle", "pageTitle", "app_info", "get", "title", "sys_app_name", "concat"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/get-page-title.js"], "sourcesContent": ["import storage from '@/utils/storage'\r\n\r\nexport default function getPageTitle(pageTitle) {\r\n  const app_info = storage.get('app_info')\r\n  const title = app_info ? app_info.sys_app_name : 'go-admin 后台管理系统'\r\n  if (pageTitle) {\r\n    return `${pageTitle} - ${title}`\r\n  }\r\n  return `${title}`\r\n}\r\n"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC9C,IAAMC,QAAQ,GAAGH,OAAO,CAACI,GAAG,CAAC,UAAU,CAAC;EACxC,IAAMC,KAAK,GAAGF,QAAQ,GAAGA,QAAQ,CAACG,YAAY,GAAG,iBAAiB;EAClE,IAAIJ,SAAS,EAAE;IACb,UAAAK,MAAA,CAAUL,SAAS,SAAAK,MAAA,CAAMF,KAAK;EAChC;EACA,UAAAE,MAAA,CAAUF,KAAK;AACjB", "ignoreList": []}]}