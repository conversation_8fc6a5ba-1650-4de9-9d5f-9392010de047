{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue", "mtime": 1753924830453}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgdXNlckF2YXRhciBmcm9tICcuL3VzZXJBdmF0YXInDQppbXBvcnQgdXNlckluZm8gZnJvbSAnLi91c2VySW5mbycNCmltcG9ydCByZXNldFB3ZCBmcm9tICcuL3Jlc2V0UHdkJw0KaW1wb3J0IHsgZ2V0VXNlclByb2ZpbGUgfSBmcm9tICdAL2FwaS9hZG1pbi9zeXMtdXNlcicNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUHJvZmlsZScsDQogIGNvbXBvbmVudHM6IHsgdXNlckF2YXRhciwgdXNlckluZm8sIHJlc2V0UHdkIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVzZXI6IHt9LA0KICAgICAgcm9sZUdyb3VwOiB7fSwNCiAgICAgIHBvc3RHcm91cDoge30sDQogICAgICBkZXB0R3JvdXA6IHt9LA0KICAgICAgYWN0aXZlVGFiOiAndXNlcmluZm8nLA0KICAgICAgcm9sZUlkczogdW5kZWZpbmVkLA0KICAgICAgcG9zdElkczogdW5kZWZpbmVkLA0KICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwNCiAgICAgIHBvc3ROYW1lOiB1bmRlZmluZWQsDQogICAgICBkZXB0OiB7fSwNCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRVc2VyKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldFVzZXIoKSB7DQogICAgICBnZXRVc2VyUHJvZmlsZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnVzZXIgPSByZXNwb25zZS5kYXRhLnVzZXINCiAgICAgICAgdGhpcy5yb2xlSWRzID0gcmVzcG9uc2UuZGF0YS51c2VyLnJvbGVJZHMNCiAgICAgICAgdGhpcy5yb2xlR3JvdXAgPSByZXNwb25zZS5kYXRhLnJvbGVzDQoNCiAgICAgICAgaWYgKHRoaXMucm9sZUlkc1swXSkgew0KICAgICAgICAgIGZvciAoY29uc3Qga2V5IGluIHRoaXMucm9sZUdyb3VwKSB7DQogICAgICAgICAgICBpZiAodGhpcy5yb2xlSWRzWzBdID09PSB0aGlzLnJvbGVHcm91cFtrZXldLnJvbGVJZCkgew0KICAgICAgICAgICAgICB0aGlzLnJvbGVOYW1lID0gdGhpcy5yb2xlR3JvdXBba2V5XS5yb2xlTmFtZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnJvbGVOYW1lID0gJ+aaguaXoCcNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmRlcHQgPSByZXNwb25zZS5kYXRhLnVzZXIuZGVwdA0KICAgICAgICB0aGlzLmRlcHROYW1lID0gdGhpcy5kZXB0LmRlcHROYW1lDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue"], "names": [], "mappings": ";AA+DA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C;UACF;QACF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACrB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC;IACH;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"6\" :xs=\"24\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>个人信息</span>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-center\">\r\n                <userAvatar :user=\"user\" />\r\n              </div>\r\n              <ul class=\"list-group list-group-striped\">\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />用户名称\r\n                  <div class=\"pull-right\">{{ user.username }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"phone\" />手机号码\r\n                  <div class=\"pull-right\">{{ user.phone }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"email\" />用户邮箱\r\n                  <div class=\"pull-right\">{{ user.email }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"tree\" />所属部门\r\n                  <div class=\"pull-right\">{{ deptName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"peoples\" />所属角色\r\n                  <div class=\"pull-right\">{{ roleName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"date\" />创建日期\r\n                  <div class=\"pull-right\">{{ user.createdAt }}</div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <el-card>\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>基本资料</span>\r\n            </div>\r\n            <el-tabs v-model=\"activeTab\">\r\n              <el-tab-pane label=\"基本资料\" name=\"userinfo\">\r\n                <userInfo :user=\"user\" />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\r\n                <resetPwd :user=\"user\" />\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport userAvatar from './userAvatar'\r\nimport userInfo from './userInfo'\r\nimport resetPwd from './resetPwd'\r\nimport { getUserProfile } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  name: 'Profile',\r\n  components: { userAvatar, userInfo, resetPwd },\r\n  data() {\r\n    return {\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      deptGroup: {},\r\n      activeTab: 'userinfo',\r\n      roleIds: undefined,\r\n      postIds: undefined,\r\n      roleName: undefined,\r\n      postName: undefined,\r\n      dept: {},\r\n      deptName: undefined\r\n    }\r\n  },\r\n  created() {\r\n    this.getUser()\r\n  },\r\n  methods: {\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data.user\r\n        this.roleIds = response.data.user.roleIds\r\n        this.roleGroup = response.data.roles\r\n\r\n        if (this.roleIds[0]) {\r\n          for (const key in this.roleGroup) {\r\n            if (this.roleIds[0] === this.roleGroup[key].roleId) {\r\n              this.roleName = this.roleGroup[key].roleName\r\n            }\r\n          }\r\n        } else {\r\n          this.roleName = '暂无'\r\n        }\r\n        this.dept = response.data.user.dept\r\n        this.deptName = this.dept.deptName\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .list-group-item{\r\n    padding: 18px 0;\r\n  }\r\n  .svg-icon{\r\n    margin-right: 5px;\r\n  }\r\n</style>\r\n"]}]}