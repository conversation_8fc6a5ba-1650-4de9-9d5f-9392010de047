{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\GithubCorner\\index.vue?vue&type=template&id=402c866a&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\GithubCorner\\index.vue", "mtime": 1753924830036}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxhIGhyZWY9Imh0dHBzOi8vZ2l0aHViLmNvbS93ZW5qaWFuemhhbmcvZ28tYWRtaW4iIHRhcmdldD0iX2JsYW5rIiBjbGFzcz0iZ2l0aHViLWNvcm5lciIgYXJpYS1sYWJlbD0iVmlldyBzb3VyY2Ugb24gR2l0aHViIj4NCiAgICA8c3ZnDQogICAgICB3aWR0aD0iODAiDQogICAgICBoZWlnaHQ9IjgwIg0KICAgICAgdmlld0JveD0iMCAwIDI1MCAyNTAiDQogICAgICBzdHlsZT0iZmlsbDojNDBjOWM2OyBjb2xvcjojZmZmOyINCiAgICAgIGFyaWEtaGlkZGVuPSJ0cnVlIg0KICAgID4NCiAgICAgIDxwYXRoIGQ9Ik0wLDAgTDExNSwxMTUgTDEzMCwxMTUgTDE0MiwxNDIgTDI1MCwyNTAgTDI1MCwwIFoiIC8+DQogICAgICA8cGF0aA0KICAgICAgICBkPSJNMTI4LjMsMTA5LjAgQzExMy44LDk5LjcgMTE5LjAsODkuNiAxMTkuMCw4OS42IEMxMjIuMCw4Mi43IDEyMC41LDc4LjYgMTIwLjUsNzguNiBDMTE5LjIsNzIuMCAxMjMuNCw3Ni4zIDEyMy40LDc2LjMgQzEyNy4zLDgwLjkgMTI1LjUsODcuMyAxMjUuNSw4Ny4zIEMxMjIuOSw5Ny42IDEzMC42LDEwMS45IDEzNC40LDEwMy4yIg0KICAgICAgICBmaWxsPSJjdXJyZW50Q29sb3IiDQogICAgICAgIHN0eWxlPSJ0cmFuc2Zvcm0tb3JpZ2luOiAxMzBweCAxMDZweDsiDQogICAgICAgIGNsYXNzPSJvY3RvLWFybSINCiAgICAgIC8+DQogICAgICA8cGF0aA0KICAgICAgICBkPSJNMTE1LjAsMTE1LjAgQzExNC45LDExNS4xIDExOC43LDExNi41IDExOS44LDExNS40IEwxMzMuNywxMDEuNiBDMTM2LjksOTkuMiAxMzkuOSw5OC40IDE0Mi4yLDk4LjYgQzEzMy44LDg4LjAgMTI3LjUsNzQuNCAxNDMuOCw1OC4wIEMxNDguNSw1My40IDE1NC4wLDUxLjIgMTU5LjcsNTEuMCBDMTYwLjMsNDkuNCAxNjMuMiw0My42IDE3MS40LDQwLjEgQzE3MS40LDQwLjEgMTc2LjEsNDIuNSAxNzguOCw1Ni4yIEMxODMuMSw1OC42IDE4Ny4yLDYxLjggMTkwLjksNjUuNCBDMTk0LjUsNjkuMCAxOTcuNyw3My4yIDIwMC4xLDc3LjYgQzIxMy44LDgwLjIgMjE2LjMsODQuOSAyMTYuMyw4NC45IEMyMTIuNyw5My4xIDIwNi45LDk2LjAgMjA1LjQsOTYuNiBDMjA1LjEsMTAyLjQgMjAzLjAsMTA3LjggMTk4LjMsMTEyLjUgQzE4MS45LDEyOC45IDE2OC4zLDEyMi41IDE1Ny43LDExNC4xIEMxNTcuOSwxMTYuOSAxNTYuNywxMjAuOSAxNTIuNywxMjQuOSBMMTQxLjAsMTM2LjUgQzEzOS44LDEzNy43IDE0MS42LDE0MS45IDE0MS44LDE0MS44IFoiDQogICAgICAgIGZpbGw9ImN1cnJlbnRDb2xvciINCiAgICAgICAgY2xhc3M9Im9jdG8tYm9keSINCiAgICAgIC8+DQogICAgPC9zdmc+DQogIDwvYT4NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\GithubCorner\\index.vue"], "names": [], "mappings": ";EACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzH,CAAC,CAAC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;MACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3L,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACthB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/GithubCorner/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <a href=\"https://github.com/wenjianz<PERSON>/go-admin\" target=\"_blank\" class=\"github-corner\" aria-label=\"View source on Github\">\r\n    <svg\r\n      width=\"80\"\r\n      height=\"80\"\r\n      viewBox=\"0 0 250 250\"\r\n      style=\"fill:#40c9c6; color:#fff;\"\r\n      aria-hidden=\"true\"\r\n    >\r\n      <path d=\"M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z\" />\r\n      <path\r\n        d=\"M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2\"\r\n        fill=\"currentColor\"\r\n        style=\"transform-origin: 130px 106px;\"\r\n        class=\"octo-arm\"\r\n      />\r\n      <path\r\n        d=\"M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z\"\r\n        fill=\"currentColor\"\r\n        class=\"octo-body\"\r\n      />\r\n    </svg>\r\n  </a>\r\n</template>\r\n\r\n<style scoped>\r\n.github-corner:hover .octo-arm {\r\n  animation: octocat-wave 560ms ease-in-out\r\n}\r\n\r\n@keyframes octocat-wave {\r\n  0%,\r\n  100% {\r\n    transform: rotate(0)\r\n  }\r\n  20%,\r\n  60% {\r\n    transform: rotate(-25deg)\r\n  }\r\n  40%,\r\n  80% {\r\n    transform: rotate(10deg)\r\n  }\r\n}\r\n\r\n@media (max-width:500px) {\r\n  .github-corner:hover .octo-arm {\r\n    animation: none\r\n  }\r\n  .github-corner .octo-arm {\r\n    animation: octocat-wave 560ms ease-in-out\r\n  }\r\n}\r\n</style>\r\n"]}]}