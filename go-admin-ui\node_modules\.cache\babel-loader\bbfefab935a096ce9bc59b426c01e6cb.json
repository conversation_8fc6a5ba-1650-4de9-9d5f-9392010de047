{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue", "mtime": 1753924830413}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFVwbG9hZEV4Y2VsQ29tcG9uZW50IGZyb20gJ0AvY29tcG9uZW50cy9VcGxvYWRFeGNlbC9pbmRleC52dWUnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1VwbG9hZEV4Y2VsJywKICBjb21wb25lbnRzOiB7CiAgICBVcGxvYWRFeGNlbENvbXBvbmVudDogVXBsb2FkRXhjZWxDb21wb25lbnQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB0YWJsZURhdGE6IFtdLAogICAgICB0YWJsZUhlYWRlcjogW10KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBiZWZvcmVVcGxvYWQ6IGZ1bmN0aW9uIGJlZm9yZVVwbG9hZChmaWxlKSB7CiAgICAgIHZhciBpc0x0MU0gPSBmaWxlLnNpemUgLyAxMDI0IC8gMTAyNCA8IDEwOwogICAgICBpZiAoaXNMdDFNKSB7CiAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgIH0KICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgbWVzc2FnZTogJ1BsZWFzZSBkbyBub3QgdXBsb2FkIGZpbGVzIGxhcmdlciB0aGFuIDFtIGluIHNpemUuJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSk7CiAgICAgIHJldHVybiBmYWxzZTsKICAgIH0sCiAgICBoYW5kbGVTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVTdWNjZXNzKF9yZWYpIHsKICAgICAgdmFyIHJlc3VsdHMgPSBfcmVmLnJlc3VsdHMsCiAgICAgICAgaGVhZGVyID0gX3JlZi5oZWFkZXI7CiAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzdWx0czsKICAgICAgdGhpcy50YWJsZUhlYWRlciA9IGhlYWRlcjsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["UploadExcelComponent", "name", "components", "data", "tableData", "tableHeader", "methods", "beforeUpload", "file", "isLt1M", "size", "$message", "message", "type", "handleSuccess", "_ref", "results", "header"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <upload-excel-component :on-success=\"handleSuccess\" :before-upload=\"beforeUpload\" />\r\n    <el-table :data=\"tableData\" border highlight-current-row style=\"width: 100%;margin-top:20px;\">\r\n      <el-table-column v-for=\"item of tableHeader\" :key=\"item\" :prop=\"item\" :label=\"item\" />\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UploadExcelComponent from '@/components/UploadExcel/index.vue'\r\n\r\nexport default {\r\n  name: 'UploadExcel',\r\n  components: { UploadExcelComponent },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      tableHeader: []\r\n    }\r\n  },\r\n  methods: {\r\n    beforeUpload(file) {\r\n      const isLt1M = file.size / 1024 / 1024 < 10\r\n\r\n      if (isLt1M) {\r\n        return true\r\n      }\r\n\r\n      this.$message({\r\n        message: 'Please do not upload files larger than 1m in size.',\r\n        type: 'warning'\r\n      })\r\n      return false\r\n    },\r\n    handleSuccess({ results, header }) {\r\n      this.tableData = results\r\n      this.tableHeader = header\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": "AAUA,OAAOA,oBAAmB,MAAO,oCAAmC;AAEpE,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IAAEF,oBAAmB,EAAnBA;EAAqB,CAAC;EACpCG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,YAAY,WAAZA,YAAYA,CAACC,IAAI,EAAE;MACjB,IAAMC,MAAK,GAAID,IAAI,CAACE,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI,EAAC;MAE1C,IAAID,MAAM,EAAE;QACV,OAAO,IAAG;MACZ;MAEA,IAAI,CAACE,QAAQ,CAAC;QACZC,OAAO,EAAE,oDAAoD;QAC7DC,IAAI,EAAE;MACR,CAAC;MACD,OAAO,KAAI;IACb,CAAC;IACDC,aAAa,WAAbA,aAAaA,CAAAC,IAAA,EAAsB;MAAA,IAAnBC,OAAO,GAAAD,IAAA,CAAPC,OAAO;QAAEC,MAAK,GAAAF,IAAA,CAALE,MAAK;MAC5B,IAAI,CAACb,SAAQ,GAAIY,OAAM;MACvB,IAAI,CAACX,WAAU,GAAIY,MAAK;IAC1B;EACF;AACF", "ignoreList": []}]}