{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-dept.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-dept.js", "mtime": 1753924829928}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKZXhwb3J0IGZ1bmN0aW9uIGdldERlcHRMaXN0KHF1ZXJ5KSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9kZXB0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoumDqOmXqOivpue7hgpleHBvcnQgZnVuY3Rpb24gZ2V0RGVwdChkZXB0SWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RlcHQvJyArIGRlcHRJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5p+l6K+i6YOo6Zeo5LiL5ouJ5qCR57uT5p6ECmV4cG9ydCBmdW5jdGlvbiB0cmVlc2VsZWN0KCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvZGVwdFRyZWUnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmoLnmja7op5LoibJJROafpeivoumDqOmXqOagkee7k+aehApleHBvcnQgZnVuY3Rpb24gcm9sZURlcHRUcmVlc2VsZWN0KHJvbGVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvcm9sZURlcHRUcmVlc2VsZWN0LycgKyByb2xlSWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinumDqOmXqApleHBvcnQgZnVuY3Rpb24gYWRkRGVwdChkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9kZXB0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnpg6jpl6gKZXhwb3J0IGZ1bmN0aW9uIHVwZGF0ZURlcHQoZGF0YSwgaWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RlcHQvJyArIGlkLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6YOo6ZeoCmV4cG9ydCBmdW5jdGlvbiBkZWxEZXB0KGRhdGEpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL2RlcHQnLAogICAgbWV0aG9kOiAnZGVsZXRlJywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQ=="}, {"version": 3, "names": ["request", "getDeptList", "query", "url", "method", "params", "getDept", "deptId", "treeselect", "roleDeptTreeselect", "roleId", "addDept", "data", "updateDept", "id", "delDept"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-dept.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\nexport function getDeptList(query) {\r\n  return request({\r\n    url: '/api/v1/dept',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询部门详细\r\nexport function getDept(deptId) {\r\n  return request({\r\n    url: '/api/v1/dept/' + deptId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询部门下拉树结构\r\nexport function treeselect() {\r\n  return request({\r\n    url: '/api/v1/deptTree',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据角色ID查询部门树结构\r\nexport function roleDeptTreeselect(roleId) {\r\n  return request({\r\n    url: '/api/v1/roleDeptTreeselect/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增部门\r\nexport function addDept(data) {\r\n  return request({\r\n    url: '/api/v1/dept',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改部门\r\nexport function updateDept(data, id) {\r\n  return request({\r\n    url: '/api/v1/dept/' + id,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除部门\r\nexport function delDept(data) {\r\n  return request({\r\n    url: '/api/v1/dept',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B,GAAGO,MAAM;IAC3CN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASO,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAEE,EAAE,EAAE;EACnC,OAAOd,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGW,EAAE;IACzBV,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACH,IAAI,EAAE;EAC5B,OAAOZ,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,QAAQ;IAChBQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}