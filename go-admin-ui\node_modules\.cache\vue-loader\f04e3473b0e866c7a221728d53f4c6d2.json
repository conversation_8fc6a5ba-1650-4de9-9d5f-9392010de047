{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue?vue&type=template&id=74f8da5d", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue", "mtime": 1753924830413}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPg0KICAgIDx1cGxvYWQtZXhjZWwtY29tcG9uZW50IDpvbi1zdWNjZXNzPSJoYW5kbGVTdWNjZXNzIiA6YmVmb3JlLXVwbG9hZD0iYmVmb3JlVXBsb2FkIiAvPg0KICAgIDxlbC10YWJsZSA6ZGF0YT0idGFibGVEYXRhIiBib3JkZXIgaGlnaGxpZ2h0LWN1cnJlbnQtcm93IHN0eWxlPSJ3aWR0aDogMTAwJTttYXJnaW4tdG9wOjIwcHg7Ij4NCiAgICAgIDxlbC10YWJsZS1jb2x1bW4gdi1mb3I9Iml0ZW0gb2YgdGFibGVIZWFkZXIiIDprZXk9Iml0ZW0iIDpwcm9wPSJpdGVtIiA6bGFiZWw9Iml0ZW0iIC8+DQogICAgPC9lbC10YWJsZT4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\upload-excel.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,<PERSON>AC,EAAE,CAAC;IACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/excel/upload-excel.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <upload-excel-component :on-success=\"handleSuccess\" :before-upload=\"beforeUpload\" />\r\n    <el-table :data=\"tableData\" border highlight-current-row style=\"width: 100%;margin-top:20px;\">\r\n      <el-table-column v-for=\"item of tableHeader\" :key=\"item\" :prop=\"item\" :label=\"item\" />\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UploadExcelComponent from '@/components/UploadExcel/index.vue'\r\n\r\nexport default {\r\n  name: 'UploadExcel',\r\n  components: { UploadExcelComponent },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      tableHeader: []\r\n    }\r\n  },\r\n  methods: {\r\n    beforeUpload(file) {\r\n      const isLt1M = file.size / 1024 / 1024 < 10\r\n\r\n      if (isLt1M) {\r\n        return true\r\n      }\r\n\r\n      this.$message({\r\n        message: 'Please do not upload files larger than 1m in size.',\r\n        type: 'warning'\r\n      })\r\n      return false\r\n    },\r\n    handleSuccess({ results, header }) {\r\n      this.tableData = results\r\n      this.tableHeader = header\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}