{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue?vue&type=template&id=87ad2a86&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue", "mtime": 1753924830300}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;gBAGhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACpE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE;;cAEE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/gen/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form ref=\"queryForm\" :model=\"queryParams\" :inline=\"true\" label-position=\"left\">\r\n          <el-form-item label=\"表名称\" prop=\"tableName\">\r\n            <el-input\r\n              v-model=\"queryParams.tableName\"\r\n              placeholder=\"请输入表名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"菜单名称\" prop=\"tableComment\">\r\n            <el-input\r\n              v-model=\"queryParams.tableComment\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n          <!-- <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleGenTable\"\r\n            >生成</el-button> -->\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"info\"\r\n              icon=\"el-icon-upload\"\r\n              size=\"mini\"\r\n              @click=\"openImportTable\"\r\n            >导入</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"success\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleEditTable\"\r\n            >修改</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n            >删除</el-button>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" />\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"tableId\" width=\"50px\" />\r\n          <el-table-column\r\n            label=\"表名称\"\r\n            align=\"center\"\r\n            prop=\"tableName\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column\r\n            label=\"菜单名称\"\r\n            align=\"center\"\r\n            prop=\"tableComment\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column\r\n            label=\"模型名称\"\r\n            align=\"center\"\r\n            prop=\"className\"\r\n            :show-overflow-tooltip=\"true\"\r\n            width=\"130\"\r\n          />\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"165\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleEditTable(scope.row)\"\r\n              >编辑</el-button>\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handlePreview(scope.row)\"\r\n              >预览</el-button>\r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"handleToProject(scope.row)\"\r\n                >代码生成</el-button>\r\n\r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                  @click=\"handleToDB(scope.row)\"\r\n                >生成配置</el-button>\r\n\r\n     \r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-view\"\r\n                   @click=\"handleToApiFile(scope.row)\"\r\n                >生成迁移脚本</el-button>\r\n                \r\n                <el-button\r\n                  slot=\"reference\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-delete\"\r\n                  @click=\"handleSingleDelete(scope.row)\"\r\n                >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageIndex\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 预览界面 -->\r\n\r\n      <el-dialog class=\"preview\" :title=\"preview.title\" :visible.sync=\"preview.open\" :close-on-click-modal=\"false\" fullscreen>\r\n        <div class=\"el-dialog-container\">\r\n          <div class=\"tag-group\">\r\n            <!-- eslint-disable-next-line vue/valid-v-for -->\r\n            <el-tag v-for=\"(value, key) in preview.data\" @click=\"codeChange(key)\">\r\n              <template>\r\n                {{ key.substring(key.lastIndexOf('/')+1,key.indexOf('.go.template')) }}\r\n              </template>\r\n            </el-tag>\r\n          </div>\r\n          <div id=\"codemirror\">\r\n            <codemirror ref=\"cmEditor\" :value=\"codestr\" :options=\"cmOptions\" />\r\n          </div>\r\n          <!-- <el-tabs v-model=\"preview.activeName\" tab-position=\"left\">\r\n            <el-tab-pane\r\n              v-for=\"(value, key) in preview.data\"\r\n              :key=\"key\"\r\n              :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.template'))\"\r\n              :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.template'))\"\r\n            >\r\n\r\n              <pre class=\"pre\"/>\r\n\r\n            </el-tab-pane>\r\n            </el-tabs> -->\r\n        </div>\r\n\r\n      </el-dialog>\r\n      <import-table ref=\"importTB\" @ok=\"handleQuery\" />\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listTable, previewTable, delTable, toDBTable, toProjectTableCheckRole, apiToFile } from '@/api/tools/gen'\r\nimport importTable from './importTable'\r\nimport { downLoadFile } from '@/utils/zipdownload'\r\nimport { codemirror } from 'vue-codemirror'\r\nimport 'codemirror/theme/material-palenight.css'\r\n\r\nrequire('codemirror/mode/javascript/javascript')\r\nimport 'codemirror/mode/javascript/javascript'\r\nimport 'codemirror/mode/go/go'\r\nimport 'codemirror/mode/vue/vue'\r\n\r\nexport default {\r\n  name: 'Gen',\r\n  components: { importTable, codemirror },\r\n  data() {\r\n    return {\r\n      cmOptions: {\r\n        tabSize: 4,\r\n        theme: 'material-palenight',\r\n        mode: 'text/javascript',\r\n        lineNumbers: true,\r\n        line: true\r\n        // more CodeMirror options...\r\n      },\r\n      codestr: '',\r\n      // 遮罩层\r\n      loading: true,\r\n      // 唯一标识符\r\n      uniqueId: '',\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中表数组\r\n      tableNames: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      tableList: [],\r\n      // 日期范围\r\n      dateRange: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageIndex: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      },\r\n      // 预览参数\r\n      preview: {\r\n        open: false,\r\n        title: '代码预览',\r\n        data: {},\r\n        activeName: 'api.go'\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  activated() {\r\n    const time = this.$route.query.t\r\n    if (time !== null && time !== this.uniqueId) {\r\n      this.uniqueId = time\r\n      this.resetQuery()\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询表集合 */\r\n    getList() {\r\n      this.loading = true\r\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n        this.tableList = response.data.list\r\n        this.total = response.data.count\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    codeChange(e) {\r\n      if (e.indexOf('js') > -1) {\r\n        this.cmOptions.mode = 'text/javascript'\r\n      }\r\n      if (e.indexOf('model') > -1 || e.indexOf('router') > -1 || e.indexOf('api') > -1 || e.indexOf('service') > -1 || e.indexOf('dto') > -1) {\r\n        this.cmOptions.mode = 'text/x-go'\r\n      }\r\n      if (e.indexOf('vue') > -1) {\r\n        this.cmOptions.mode = 'text/x-vue'\r\n      }\r\n      this.codestr = this.preview.data[e]\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    /** 生成代码操作 */\r\n    handleGenTable(row) {\r\n      const ids = row.tableId || this.ids\r\n      if (ids === '') {\r\n        this.msgError('请选择要生成的数据')\r\n        return\r\n      }\r\n      downLoadFile('/api/v1/gen/gencode/' + ids)\r\n    },\r\n    /** 打开导入表弹窗 */\r\n    openImportTable() {\r\n      this.$refs.importTB.show()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    /** 预览按钮 */\r\n    handlePreview(row) {\r\n      previewTable(row.tableId).then(response => {\r\n        this.preview.data = response.data\r\n        this.preview.open = true\r\n        this.codeChange('template/api.go.template')\r\n      })\r\n    },\r\n    handleToProject(row) {\r\n      toProjectTableCheckRole(row.tableId, false).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    handleToApiFile(row) {\r\n      apiToFile(row.tableId, true).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    handleToDB(row) {\r\n      toDBTable(row.tableId).then((response) => {\r\n        this.msgSuccess(response.msg)\r\n      }).catch(function() {})\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.tableId)\r\n      this.tableNames = selection.map(item => item.tableName)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleEditTable(row) {\r\n      const tableId = row.tableId || this.ids[0]\r\n      this.$router.push({ path: '/dev-tools/editTable', query: { tableId: tableId }})\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const tableIds = row.tableId || this.ids\r\n      this.$confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        return delTable(tableIds)\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    },\r\n    handleSingleDelete(row) {\r\n      const tableIds = row.tableId || this.ids\r\n      delTable(tableIds).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n .el-dialog-container ::v-deep{\r\n   overflow: hidden;\r\n   .el-scrollbar__view{\r\n     height: 100%;\r\n   }\r\n   .pre{\r\n     height: 546px;\r\n      overflow: hidden;\r\n      .el-scrollbar{\r\n        height: 100%;\r\n      }\r\n   }\r\n   .el-scrollbar__wrap::-webkit-scrollbar{\r\n     display: none;\r\n   }\r\n }\r\n ::v-deep .el-dialog__body{\r\n    padding: 0 20px;\r\n    margin:0;\r\n  }\r\n  .tag-group {\r\n    margin: 0 0 10px -10px;\r\n  }\r\n  .tag-group .el-tag{\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .el-tag {\r\n    cursor: pointer;\r\n  }\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n  #codemirror {\r\n      height: auto;\r\n      margin: 0;\r\n      overflow: auto;\r\n    }\r\n  .CodeMirror {\r\n      border: 1px solid #eee;\r\n      height: 600px;\r\n    }\r\n</style>\r\n"]}]}