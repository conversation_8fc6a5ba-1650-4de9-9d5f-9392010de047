{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\permission.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\permission.js", "mtime": 1753924830220}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "store", "Message", "NProgress", "getToken", "getPageTitle", "configure", "showSpinner", "whiteList", "beforeEach", "_ref", "_asyncToGenerator", "_regenerator", "m", "_callee", "to", "from", "next", "hasToken", "hasRoles", "_yield$store$dispatch", "roles", "accessRoutes", "_t", "w", "_context", "p", "n", "start", "document", "title", "meta", "path", "done", "getters", "length", "dispatch", "v", "addRoutes", "_objectSpread", "replace", "error", "concat", "indexOf", "a", "_x", "_x2", "_x3", "apply", "arguments", "after<PERSON>ach"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/permission.js"], "sourcesContent": ["import router from './router'\r\nimport store from './store'\r\nimport { Message } from 'element-ui'\r\nimport NProgress from 'nprogress' // progress bar\r\nimport 'nprogress/nprogress.css' // progress bar style\r\nimport { getToken } from '@/utils/auth' // get token from cookie\r\nimport getPageTitle from '@/utils/get-page-title'\r\n\r\nNProgress.configure({ showSpinner: false }) // NProgress Configuration\r\n\r\nconst whiteList = ['/login', '/auth-redirect'] // no redirect whitelist\r\n\r\nrouter.beforeEach(async(to, from, next) => {\r\n  // start progress bar\r\n  NProgress.start()\r\n\r\n  // set page title\r\n  document.title = getPageTitle(to.meta.title)\r\n\r\n  // determine whether the user has logged in\r\n  const hasToken = getToken()\r\n\r\n  if (hasToken) {\r\n    if (to.path === '/login') {\r\n      // if is logged in, redirect to the home page\r\n      next({ path: '/' })\r\n      NProgress.done()\r\n    } else {\r\n      // determine whether the user has obtained his permission roles through getInfo\r\n      const hasRoles = store.getters.roles && store.getters.roles.length > 0\r\n      if (hasRoles) {\r\n        next()\r\n      } else {\r\n        try {\r\n          // get user info\r\n          // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']\r\n          const { roles } = await store.dispatch('user/getInfo')\r\n\r\n          // generate accessible routes map based on roles\r\n          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)\r\n\r\n          // dynamically add accessible routes\r\n          router.addRoutes(accessRoutes)\r\n\r\n          // hack method to ensure that addRoutes is complete\r\n          // set the replace: true, so the navigation will not leave a history record\r\n          next({ ...to, replace: true })\r\n        } catch (error) {\r\n          // remove token and go to login page to re-login\r\n          // await store.dispatch('user/resetToken')\r\n          Message.error(error || 'Has Error')\r\n          next(`/login?redirect=${to.path}`)\r\n          NProgress.done()\r\n        }\r\n      }\r\n    }\r\n  } else {\r\n    /* has no token*/\r\n\r\n    if (whiteList.indexOf(to.path) !== -1) {\r\n      // in the free login whitelist, go directly\r\n      next()\r\n    } else {\r\n      // other pages that do not have permission to access are redirected to the login page.\r\n      next(`/login?redirect=${to.path}`)\r\n      NProgress.done()\r\n    }\r\n  }\r\n})\r\n\r\nrouter.afterEach(() => {\r\n  // finish progress bar\r\n  NProgress.done()\r\n})\r\n"], "mappings": ";;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,OAAO,QAAQ,YAAY;AACpC,OAAOC,SAAS,MAAM,WAAW,EAAC;AAClC,OAAO,yBAAyB,EAAC;AACjC,SAASC,QAAQ,QAAQ,cAAc,EAAC;AACxC,OAAOC,YAAY,MAAM,wBAAwB;AAEjDF,SAAS,CAACG,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC,EAAC;;AAE5C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAAC;;AAE/CR,MAAM,CAACS,UAAU;EAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,CAAC,SAAAC,QAAMC,EAAE,EAAEC,IAAI,EAAEC,IAAI;IAAA,IAAAC,QAAA,EAAAC,QAAA,EAAAC,qBAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,EAAA;IAAA,OAAAX,YAAA,GAAAY,CAAA,WAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;QAAA;UACpC;UACAxB,SAAS,CAACyB,KAAK,CAAC,CAAC;;UAEjB;UACAC,QAAQ,CAACC,KAAK,GAAGzB,YAAY,CAACU,EAAE,CAACgB,IAAI,CAACD,KAAK,CAAC;;UAE5C;UACMZ,QAAQ,GAAGd,QAAQ,CAAC,CAAC;UAAA,KAEvBc,QAAQ;YAAAO,QAAA,CAAAE,CAAA;YAAA;UAAA;UAAA,MACNZ,EAAE,CAACiB,IAAI,KAAK,QAAQ;YAAAP,QAAA,CAAAE,CAAA;YAAA;UAAA;UACtB;UACAV,IAAI,CAAC;YAAEe,IAAI,EAAE;UAAI,CAAC,CAAC;UACnB7B,SAAS,CAAC8B,IAAI,CAAC,CAAC;UAAAR,QAAA,CAAAE,CAAA;UAAA;QAAA;UAEhB;UACMR,QAAQ,GAAGlB,KAAK,CAACiC,OAAO,CAACb,KAAK,IAAIpB,KAAK,CAACiC,OAAO,CAACb,KAAK,CAACc,MAAM,GAAG,CAAC;UAAA,KAClEhB,QAAQ;YAAAM,QAAA,CAAAE,CAAA;YAAA;UAAA;UACVV,IAAI,CAAC,CAAC;UAAAQ,QAAA,CAAAE,CAAA;UAAA;QAAA;UAAAF,QAAA,CAAAC,CAAA;UAAAD,QAAA,CAAAE,CAAA;UAAA,OAKoB1B,KAAK,CAACmC,QAAQ,CAAC,cAAc,CAAC;QAAA;UAAAhB,qBAAA,GAAAK,QAAA,CAAAY,CAAA;UAA9ChB,KAAK,GAAAD,qBAAA,CAALC,KAAK;UAAAI,QAAA,CAAAE,CAAA;UAAA,OAGc1B,KAAK,CAACmC,QAAQ,CAAC,2BAA2B,EAAEf,KAAK,CAAC;QAAA;UAAvEC,YAAY,GAAAG,QAAA,CAAAY,CAAA;UAElB;UACArC,MAAM,CAACsC,SAAS,CAAChB,YAAY,CAAC;;UAE9B;UACA;UACAL,IAAI,CAAAsB,aAAA,CAAAA,aAAA,KAAMxB,EAAE;YAAEyB,OAAO,EAAE;UAAI,EAAE,CAAC;UAAAf,QAAA,CAAAE,CAAA;UAAA;QAAA;UAAAF,QAAA,CAAAC,CAAA;UAAAH,EAAA,GAAAE,QAAA,CAAAY,CAAA;UAE9B;UACA;UACAnC,OAAO,CAACuC,KAAK,CAAClB,EAAA,IAAS,WAAW,CAAC;UACnCN,IAAI,oBAAAyB,MAAA,CAAoB3B,EAAE,CAACiB,IAAI,CAAE,CAAC;UAClC7B,SAAS,CAAC8B,IAAI,CAAC,CAAC;QAAA;UAAAR,QAAA,CAAAE,CAAA;UAAA;QAAA;UAKtB;;UAEA,IAAInB,SAAS,CAACmC,OAAO,CAAC5B,EAAE,CAACiB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrC;YACAf,IAAI,CAAC,CAAC;UACR,CAAC,MAAM;YACL;YACAA,IAAI,oBAAAyB,MAAA,CAAoB3B,EAAE,CAACiB,IAAI,CAAE,CAAC;YAClC7B,SAAS,CAAC8B,IAAI,CAAC,CAAC;UAClB;QAAC;UAAA,OAAAR,QAAA,CAAAmB,CAAA;MAAA;IAAA,GAAA9B,OAAA;EAAA,CAEJ;EAAA,iBAAA+B,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAArC,IAAA,CAAAsC,KAAA,OAAAC,SAAA;EAAA;AAAA,IAAC;AAEFjD,MAAM,CAACkD,SAAS,CAAC,YAAM;EACrB;EACA/C,SAAS,CAAC8B,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}