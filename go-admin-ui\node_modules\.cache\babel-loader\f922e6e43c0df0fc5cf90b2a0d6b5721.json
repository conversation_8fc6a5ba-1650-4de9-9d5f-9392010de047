{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Activity.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Activity.vue", "mtime": 1753924830443}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGF2YXRhclByZWZpeCA9ICc/aW1hZ2VWaWV3Mi8xL3cvODAvaC84MCc7CnZhciBjYXJvdXNlbFByZWZpeCA9ICc/aW1hZ2VWaWV3Mi8yL2gvNDQwJzsKZXhwb3J0IGRlZmF1bHQgewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjYXJvdXNlbEltYWdlczogWydodHRwczovL3dwaW1nLndhbGxzdGNuLmNvbS85Njc5ZmZiMC05ZTBiLTQ0NTEtOTkxNi1lMjE5OTIyMTgwNTQuanBnJywgJ2h0dHBzOi8vd3BpbWcud2FsbHN0Y24uY29tL2JjY2UzNzM0LTA4MzctNGI5Zi05MjYxLTM1MWVmMzg0Zjc1YS5qcGcnLCAnaHR0cHM6Ly93cGltZy53YWxsc3Rjbi5jb20vZDFkN2IwMzMtZDc1ZS00Y2Q2LWFlMzktZmNkNWYxYzBhN2M1LmpwZycsICdodHRwczovL3dwaW1nLndhbGxzdGNuLmNvbS81MDUzMDA2MS04NTFiLTRjYTUtOWRjNS0yZmVhZDkyOGE5MzkuanBnJ10sCiAgICAgIGF2YXRhclByZWZpeDogYXZhdGFyUHJlZml4LAogICAgICBjYXJvdXNlbFByZWZpeDogY2Fyb3VzZWxQcmVmaXgKICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["avatarPrefix", "carouselPrefix", "data", "carouselImages"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Activity.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-activity\">\r\n    <div class=\"post\">\r\n      <div class=\"user-block\">\r\n        <img class=\"img-circle\" :src=\"'https://wpimg.wallstcn.com/57ed425a-c71e-4201-9428-68760c0537c4.jpg'+avatarPrefix\">\r\n        <span class=\"username text-muted\">Iron Man</span>\r\n        <span class=\"description\">Shared publicly - 7:30 PM today</span>\r\n      </div>\r\n      <p>\r\n        Lorem ipsum represents a long-held tradition for designers,\r\n        typographers and the like. Some people hate it and argue for\r\n        its demise, but others ignore the hate as they create awesome\r\n        tools to help create filler text for everyone from bacon lovers\r\n        to Charlie Sheen fans.\r\n      </p>\r\n      <ul class=\"list-inline\">\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <i class=\"el-icon-share\" />\r\n            Share\r\n          </span>\r\n        </li>\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <svg-icon icon-class=\"like\" />\r\n            Like\r\n          </span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"post\">\r\n      <div class=\"user-block\">\r\n        <img class=\"img-circle\" :src=\"'https://wpimg.wallstcn.com/9e2a5d0a-bd5b-457f-ac8e-86554616c87b.jpg'+avatarPrefix\">\r\n        <span class=\"username text-muted\">Captain American</span>\r\n        <span class=\"description\">Sent you a message - yesterday</span>\r\n      </div>\r\n      <p>\r\n        Lorem ipsum represents a long-held tradition for designers,\r\n        typographers and the like. Some people hate it and argue for\r\n        its demise, but others ignore the hate as they create awesome\r\n        tools to help create filler text for everyone from bacon lovers\r\n        to Charlie Sheen fans.\r\n      </p>\r\n      <ul class=\"list-inline\">\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <i class=\"el-icon-share\" />\r\n            Share\r\n          </span>\r\n        </li>\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <svg-icon icon-class=\"like\" />\r\n            Like\r\n          </span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n    <div class=\"post\">\r\n      <div class=\"user-block\">\r\n        <img class=\"img-circle\" :src=\"'https://wpimg.wallstcn.com/fb57f689-e1ab-443c-af12-8d4066e202e2.jpg'+avatarPrefix\">\r\n        <span class=\"username\">Spider Man</span>\r\n        <span class=\"description\">Posted 4 photos - 2 days ago</span>\r\n      </div>\r\n      <div class=\"user-images\">\r\n        <el-carousel :interval=\"6000\" type=\"card\" height=\"220px\">\r\n          <el-carousel-item v-for=\"item in carouselImages\" :key=\"item\">\r\n            <img :src=\"item+carouselPrefix\" class=\"image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n      <ul class=\"list-inline\">\r\n        <li><span class=\"link-black text-sm\"><i class=\"el-icon-share\" /> Share</span></li>\r\n        <li>\r\n          <span class=\"link-black text-sm\">\r\n            <svg-icon icon-class=\"like\" /> Like</span>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nconst avatarPrefix = '?imageView2/1/w/80/h/80'\r\nconst carouselPrefix = '?imageView2/2/h/440'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      carouselImages: [\r\n        'https://wpimg.wallstcn.com/9679ffb0-9e0b-4451-9916-e21992218054.jpg',\r\n        'https://wpimg.wallstcn.com/bcce3734-0837-4b9f-9261-351ef384f75a.jpg',\r\n        'https://wpimg.wallstcn.com/d1d7b033-d75e-4cd6-ae39-fcd5f1c0a7c5.jpg',\r\n        'https://wpimg.wallstcn.com/50530061-851b-4ca5-9dc5-2fead928a939.jpg'\r\n      ],\r\n      avatarPrefix,\r\n      carouselPrefix\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user-activity {\r\n  .user-block {\r\n\r\n    .username,\r\n    .description {\r\n      display: block;\r\n      margin-left: 50px;\r\n      padding: 2px 0;\r\n    }\r\n\r\n    .username{\r\n      font-size: 16px;\r\n      color: #000;\r\n    }\r\n\r\n    :after {\r\n      clear: both;\r\n    }\r\n\r\n    .img-circle {\r\n      border-radius: 50%;\r\n      width: 40px;\r\n      height: 40px;\r\n      float: left;\r\n    }\r\n\r\n    span {\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n\r\n  .post {\r\n    font-size: 14px;\r\n    border-bottom: 1px solid #d2d6de;\r\n    margin-bottom: 15px;\r\n    padding-bottom: 15px;\r\n    color: #666;\r\n\r\n    .image {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n    }\r\n\r\n    .user-images {\r\n      padding-top: 20px;\r\n    }\r\n  }\r\n\r\n  .list-inline {\r\n    padding-left: 0;\r\n    margin-left: -5px;\r\n    list-style: none;\r\n\r\n    li {\r\n      display: inline-block;\r\n      padding-right: 5px;\r\n      padding-left: 5px;\r\n      font-size: 13px;\r\n    }\r\n\r\n    .link-black {\r\n\r\n      &:hover,\r\n      &:focus {\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n\r\n.box-center {\r\n  margin: 0 auto;\r\n  display: table;\r\n}\r\n\r\n.text-muted {\r\n  color: #777;\r\n}\r\n</style>\r\n"], "mappings": "AAmFA,IAAMA,YAAW,GAAI,yBAAwB;AAC7C,IAAMC,cAAa,GAAI,qBAAoB;AAE3C,eAAe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,cAAc,EAAE,CACd,qEAAqE,EACrE,qEAAqE,EACrE,qEAAqE,EACrE,qEAAoE,CACrE;MACDH,YAAY,EAAZA,YAAY;MACZC,cAAa,EAAbA;IACF;EACF;AACF", "ignoreList": []}]}