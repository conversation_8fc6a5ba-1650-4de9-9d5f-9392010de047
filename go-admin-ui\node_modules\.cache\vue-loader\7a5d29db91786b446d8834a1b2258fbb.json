{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue", "mtime": 1753924830053}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNaW5pUHJvZ3Jlc3MnLA0KICBwcm9wczogew0KICAgIHRhcmdldDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgaGVpZ2h0OiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnMTBweCcNCiAgICB9LA0KICAgIGNvbG9yOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnIzEzQzJDMicNCiAgICB9LA0KICAgIHBlcmNlbnRhZ2U6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue"], "names": [], "mappings": ";AAaA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/MiniProgress/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"chart-mini-progress\">\r\n    <div class=\"target\" :style=\"{ left: target + '%'}\">\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n    </div>\r\n    <div class=\"progress-wrapper\">\r\n      <div class=\"progress\" :style=\"{ backgroundColor: color, width: percentage + '%', height: height }\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MiniProgress',\r\n  props: {\r\n    target: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '10px'\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: '#13C2C2'\r\n    },\r\n    percentage: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-mini-progress {\r\n    padding: 5px 0;\r\n    position: relative;\r\n    width: 100%;\r\n    .target {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      span {\r\n        border-radius: 100px;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        height: 4px;\r\n        width: 2px;\r\n        &:last-child {\r\n          top: auto;\r\n          bottom: 0;\r\n        }\r\n      }\r\n    }\r\n    .progress-wrapper {\r\n      background-color: #f5f5f5;\r\n      position: relative;\r\n      .progress {\r\n        transition: all .4s cubic-bezier(.08,.82,.17,1) 0s;\r\n        border-radius: 1px 0 0 1px;\r\n        background-color: #1890ff;\r\n        width: 0;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}