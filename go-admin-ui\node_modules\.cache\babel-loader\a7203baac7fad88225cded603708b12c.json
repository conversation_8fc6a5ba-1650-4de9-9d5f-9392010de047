{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1753924830021}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3RhcnRzLXdpdGguanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyI7CmltcG9ydCB7IGNvbXBpbGUgfSBmcm9tICdwYXRoLXRvLXJlZ2V4cCc7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbGV2ZWxMaXN0OiBudWxsCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgICRyb3V0ZTogZnVuY3Rpb24gJHJvdXRlKHJvdXRlKSB7CiAgICAgIC8vIGlmIHlvdSBnbyB0byB0aGUgcmVkaXJlY3QgcGFnZSwgZG8gbm90IHVwZGF0ZSB0aGUgYnJlYWRjcnVtYnMKICAgICAgaWYgKHJvdXRlLnBhdGguc3RhcnRzV2l0aCgnL3JlZGlyZWN0LycpKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QnJlYWRjcnVtYjogZnVuY3Rpb24gZ2V0QnJlYWRjcnVtYigpIHsKICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUKICAgICAgdmFyIG1hdGNoZWQgPSB0aGlzLiRyb3V0ZS5tYXRjaGVkLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlOwogICAgICB9KTsKICAgICAgdmFyIGZpcnN0ID0gbWF0Y2hlZFswXTsKICAgICAgaWYgKCF0aGlzLmlzRGFzaGJvYXJkKGZpcnN0KSkgewogICAgICAgIG1hdGNoZWQgPSBbewogICAgICAgICAgcGF0aDogJy9pbmRleCcsCiAgICAgICAgICBtZXRhOiB7CiAgICAgICAgICAgIHRpdGxlOiAn6aaW6aG1JwogICAgICAgICAgfQogICAgICAgIH1dLmNvbmNhdChtYXRjaGVkKTsKICAgICAgfQogICAgICB0aGlzLmxldmVsTGlzdCA9IG1hdGNoZWQuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ubWV0YSAmJiBpdGVtLm1ldGEudGl0bGUgJiYgaXRlbS5tZXRhLmJyZWFkY3J1bWIgIT09IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBpc0Rhc2hib2FyZDogZnVuY3Rpb24gaXNEYXNoYm9hcmQocm91dGUpIHsKICAgICAgdmFyIG5hbWUgPSByb3V0ZSAmJiByb3V0ZS5uYW1lOwogICAgICBpZiAoIW5hbWUpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpID09PSAn6aaW6aG1JzsKICAgIH0sCiAgICBwYXRoQ29tcGlsZTogZnVuY3Rpb24gcGF0aENvbXBpbGUocGF0aCkgewogICAgICAvLyBUbyBzb2x2ZSB0aGlzIHByb2JsZW0gaHR0cHM6Ly9naXRodWIuY29tL1BhbkppYUNoZW4vdnVlLWVsZW1lbnQtYWRtaW4vaXNzdWVzLzU2MQogICAgICB2YXIgcGFyYW1zID0gdGhpcy4kcm91dGUucGFyYW1zOwogICAgICB2YXIgdG9QYXRoID0gY29tcGlsZShwYXRoKTsKICAgICAgcmV0dXJuIHRvUGF0aChwYXJhbXMpOwogICAgfSwKICAgIGhhbmRsZUxpbms6IGZ1bmN0aW9uIGhhbmRsZUxpbmsoaXRlbSkgewogICAgICB2YXIgcmVkaXJlY3QgPSBpdGVtLnJlZGlyZWN0LAogICAgICAgIHBhdGggPSBpdGVtLnBhdGg7CiAgICAgIGlmIChyZWRpcmVjdCkgewogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHJlZGlyZWN0KTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy4kcm91dGVyLnB1c2godGhpcy5wYXRoQ29tcGlsZShwYXRoKSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["compile", "data", "levelList", "watch", "$route", "route", "path", "startsWith", "getBreadcrumb", "created", "methods", "matched", "filter", "item", "meta", "title", "first", "isDashboard", "concat", "breadcrumb", "name", "trim", "pathCompile", "params", "to<PERSON><PERSON>", "handleLink", "redirect", "$router", "push"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Breadcrumb\\index.vue"], "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.meta.title }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nimport { compile } from 'path-to-regexp'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith('/redirect/')) {\r\n        return\r\n      }\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = this.$route.matched.filter(item => item.meta && item.meta.title)\r\n      const first = matched[0]\r\n\r\n      if (!this.isDashboard(first)) {\r\n        matched = [{ path: '/index', meta: { title: '首页' }}].concat(matched)\r\n      }\r\n\r\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim() === '首页'\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(this.pathCompile(path))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAYA,SAASA,OAAM,QAAS,gBAAe;AAEvC,eAAe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE;IACb;EACF,CAAC;EACDC,KAAK,EAAE;IACLC,MAAM,WAANA,MAAMA,CAACC,KAAK,EAAE;MACZ;MACA,IAAIA,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QACvC;MACF;MACA,IAAI,CAACC,aAAa,CAAC;IACrB;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACD,aAAa,CAAC;EACrB,CAAC;EACDE,OAAO,EAAE;IACPF,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd;MACA,IAAIG,OAAM,GAAI,IAAI,CAACP,MAAM,CAACO,OAAO,CAACC,MAAM,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACC,IAAG,IAAKD,IAAI,CAACC,IAAI,CAACC,KAAK;MAAA;MAC7E,IAAMC,KAAI,GAAIL,OAAO,CAAC,CAAC;MAEvB,IAAI,CAAC,IAAI,CAACM,WAAW,CAACD,KAAK,CAAC,EAAE;QAC5BL,OAAM,GAAI,CAAC;UAAEL,IAAI,EAAE,QAAQ;UAAEQ,IAAI,EAAE;YAAEC,KAAK,EAAE;UAAK;QAAC,CAAC,CAAC,CAACG,MAAM,CAACP,OAAO;MACrE;MAEA,IAAI,CAACT,SAAQ,GAAIS,OAAO,CAACC,MAAM,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACC,IAAG,IAAKD,IAAI,CAACC,IAAI,CAACC,KAAI,IAAKF,IAAI,CAACC,IAAI,CAACK,UAAS,KAAM,KAAK;MAAA;IACxG,CAAC;IACDF,WAAW,WAAXA,WAAWA,CAACZ,KAAK,EAAE;MACjB,IAAMe,IAAG,GAAIf,KAAI,IAAKA,KAAK,CAACe,IAAG;MAC/B,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,KAAI;MACb;MACA,OAAOA,IAAI,CAACC,IAAI,CAAC,MAAM,IAAG;IAC5B,CAAC;IACDC,WAAW,WAAXA,WAAWA,CAAChB,IAAI,EAAE;MAChB;MACA,IAAQiB,MAAK,GAAM,IAAI,CAACnB,MAAK,CAArBmB,MAAK;MACb,IAAIC,MAAK,GAAIxB,OAAO,CAACM,IAAI;MACzB,OAAOkB,MAAM,CAACD,MAAM;IACtB,CAAC;IACDE,UAAU,WAAVA,UAAUA,CAACZ,IAAI,EAAE;MACf,IAAQa,QAAQ,GAAWb,IAAG,CAAtBa,QAAQ;QAAEpB,IAAG,GAAMO,IAAG,CAAZP,IAAG;MACrB,IAAIoB,QAAQ,EAAE;QACZ,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,QAAQ;QAC1B;MACF;MACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAACN,WAAW,CAAChB,IAAI,CAAC;IAC1C;EACF;AACF", "ignoreList": []}]}