{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-config\\set.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-config\\set.vue", "mtime": 1753924830273}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getSetConfig", "updateSetConfig", "getToken", "name", "components", "data", "loading", "configList", "formConf", "headers", "form", "sys_app_name", "undefined", "sys_app_logo", "sys_user_initPassword", "sys_index_skinName", "sys_index_sideTheme", "rules", "required", "message", "trigger", "sys_app_logoAction", "process", "env", "VUE_APP_BASE_API", "sys_app_logofileList", "sys_index_skinNameOptions", "sys_index_sideThemeOptions", "created", "getList", "methods", "submitForm", "_this", "console", "log", "$refs", "validate", "valid", "list", "i", "key", "then", "response", "code", "msgSuccess", "msg", "open", "_this$form", "$store", "commit", "msgError", "resetForm", "resetFields", "sys_app_logoBeforeUpload", "file", "isRightSize", "size", "$message", "error", "uploadSuccess", "fileList", "full_path", "_this2", "setUrl", "url", "fillFormData", "key2", "Date", "typeFormat", "row", "column", "selectDictLabel", "typeOptions", "configType", "handleQuery", "queryParams", "pageIndex", "fields", "for<PERSON>ach", "item", "val", "__vModel__", "__config__", "defaultValue", "bind", "sumbitForm2", "_this3"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-config\\set.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-tabs tab-position=\"left\" style=\"height: 100%;\">\r\n          <el-tab-pane label=\"系统内置\">\r\n            <el-form label-width=\"80px\">\r\n              <div class=\"test-form\">\r\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"small\" label-width=\"100px\">\r\n                  <el-form-item label=\"系统名称\" prop=\"sys_app_name\">\r\n                    <el-input v-model=\"form.sys_app_name\" placeholder=\"请输入系统名称\" clearable :style=\"{width: '100%'}\" />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"系统logo\" prop=\"sys_app_logo\" required>\r\n                    <img v-if=\"form.sys_app_logo\" :src=\"form.sys_app_logo\" class=\"el-upload el-upload--picture-card\" style=\"float:left\">\r\n                    <el-upload ref=\"sys_app_logo\" :headers=\"headers\" :file-list=\"sys_app_logofileList\" :action=\"sys_app_logoAction\" style=\"float:left\" :before-upload=\"sys_app_logoBeforeUpload\" list-type=\"picture-card\" :show-file-list=\"false\" :on-success=\"uploadSuccess\">\r\n                      <i class=\"el-icon-plus\" />\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"初始密码\" prop=\"sys_user_initPassword\">\r\n                    <el-input v-model=\"form.sys_user_initPassword\" placeholder=\"请输入初始密码\" clearable :style=\"{width: '100%'}\" />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"皮肤样式\" prop=\"sys_index_skinName\">\r\n                    <el-select v-model=\"form.sys_index_skinName\" placeholder=\"请选择皮肤样式\" clearable :style=\"{width: '100%'}\">\r\n                      <el-option v-for=\"(item, index) in sys_index_skinNameOptions\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"侧栏主题\" prop=\"sys_index_sideTheme\">\r\n                    <el-select v-model=\"form.sys_index_sideTheme\" placeholder=\"请选择侧栏主题\" clearable :style=\"{width: '100%'}\">\r\n                      <el-option v-for=\"(item, index) in sys_index_sideThemeOptions\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item size=\"large\">\r\n                    <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n                    <el-button @click=\"resetForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n            </el-form>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"其他\">其他</el-tab-pane>\r\n        </el-tabs>\r\n\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getSetConfig,\r\n  updateSetConfig\r\n} from '@/api/admin/sys-config'\r\n\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SysConfigSet',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 参数表格数据\r\n      configList: [],\r\n      formConf: {},\r\n      headers: { 'Authorization': 'Bearer ' + getToken() },\r\n      form: {\r\n        sys_app_name: undefined,\r\n        sys_app_logo: null,\r\n        sys_user_initPassword: undefined,\r\n        sys_index_skinName: undefined,\r\n        sys_index_sideTheme: undefined\r\n      },\r\n      rules: {\r\n        sys_app_name: [{\r\n          required: true,\r\n          message: '请输入系统名称',\r\n          trigger: 'blur'\r\n        }],\r\n        sys_user_initPassword: [{\r\n          required: true,\r\n          message: '请输入初始密码',\r\n          trigger: 'blur'\r\n        }],\r\n        sys_index_skinName: [{\r\n          required: true,\r\n          message: '请选择皮肤样式',\r\n          trigger: 'change'\r\n        }],\r\n        sys_index_sideTheme: [{\r\n          required: true,\r\n          message: '请选择侧栏主题',\r\n          trigger: 'change'\r\n        }]\r\n      },\r\n      sys_app_logoAction: process.env.VUE_APP_BASE_API + '/api/v1/public/uploadFile',\r\n      sys_app_logofileList: [],\r\n      sys_index_skinNameOptions: [{\r\n        'label': '蓝色',\r\n        'value': 'skin-blue'\r\n      }],\r\n      sys_index_sideThemeOptions: [{\r\n        'label': '深色主题',\r\n        'value': 'theme-dark'\r\n      }]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      console.log(this.form)\r\n      this.$refs['form'].validate(valid => {\r\n        if (!valid) return\r\n        console.log(this.form)\r\n        var list = []\r\n        var i = 0\r\n        for (var key in this.form) {\r\n          list[i] = {\r\n            'configKey': key,\r\n            'configValue': this.form[key]\r\n          }\r\n          i++\r\n        }\r\n        updateSetConfig(list).then(response => {\r\n          if (response.code === 200) {\r\n            this.msgSuccess(response.msg)\r\n            this.open = false\r\n            this.getList()\r\n            const { sys_app_name, sys_app_logo } = this.form\r\n            this.$store.commit('system/SET_INFO', {\r\n              sys_app_logo,\r\n              sys_app_name\r\n            })\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n        })\r\n      })\r\n    },\r\n    resetForm() {\r\n      this.$refs['form'].resetFields()\r\n    },\r\n    sys_app_logoBeforeUpload(file) {\r\n      const isRightSize = file.size / 1024 / 1024 < 2\r\n      if (!isRightSize) {\r\n        this.$message.error('文件大小超过 2MB')\r\n      }\r\n      return isRightSize\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('sss')\r\n      this.form.sys_app_logo = process.env.VUE_APP_BASE_API + response.data.full_path\r\n      console.log(response.data.full_path)\r\n    },\r\n    /** 查询参数列表 */\r\n    getList() {\r\n      this.loading = true\r\n      getSetConfig().then(response => {\r\n        this.configList = response.data\r\n        this.loading = false\r\n        this.form = this.configList\r\n        // this.sys_app_logofileList = [this.configList.sys_app_logo]\r\n        // this.fillFormData(this.elForm, this.configList)\r\n        // 更新表单\r\n        // this.key2 = +new Date()\r\n      })\r\n    },\r\n    setUrl(url) {\r\n      const data = {\r\n        sys_app_logo: ''\r\n      }\r\n      data.sys_app_logo = url\r\n      // 回填数据\r\n      this.fillFormData(this.formConf, data)\r\n      // 更新表单\r\n      this.key2 = +new Date()\r\n    },\r\n    // 参数系统内置字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(this.typeOptions, row.configType)\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    fillFormData(form, data) {\r\n      form.fields.forEach(item => {\r\n        const val = data[item.__vModel__]\r\n        if (val) {\r\n          item.__config__.defaultValue = val\r\n        }\r\n      })\r\n    },\r\n    bind(key, data) {\r\n      this.setUrl(data)\r\n    },\r\n    sumbitForm2(data) {\r\n      var list = []\r\n      var i = 0\r\n      for (var key in data) {\r\n        list[i] = {\r\n          'configKey': key,\r\n          'configValue': data[key]\r\n        }\r\n        i++\r\n      }\r\n      updateSetConfig(list).then(response => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;AAgDA,SACEA,YAAY,EACZC,eAAc,QACT,wBAAuB;AAE9B,SAASC,QAAO,QAAS,cAAa;AAEtC,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,CAAC,CAAC;MACZC,OAAO,EAAE;QAAE,eAAe,EAAE,SAAQ,GAAIP,QAAQ,CAAC;MAAE,CAAC;MACpDQ,IAAI,EAAE;QACJC,YAAY,EAAEC,SAAS;QACvBC,YAAY,EAAE,IAAI;QAClBC,qBAAqB,EAAEF,SAAS;QAChCG,kBAAkB,EAAEH,SAAS;QAC7BI,mBAAmB,EAAEJ;MACvB,CAAC;MACDK,KAAK,EAAE;QACLN,YAAY,EAAE,CAAC;UACbO,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,SAAS;UAClBC,OAAO,EAAE;QACX,CAAC,CAAC;QACFN,qBAAqB,EAAE,CAAC;UACtBI,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,SAAS;UAClBC,OAAO,EAAE;QACX,CAAC,CAAC;QACFL,kBAAkB,EAAE,CAAC;UACnBG,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,SAAS;UAClBC,OAAO,EAAE;QACX,CAAC,CAAC;QACFJ,mBAAmB,EAAE,CAAC;UACpBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE,SAAS;UAClBC,OAAO,EAAE;QACX,CAAC;MACH,CAAC;MACDC,kBAAkB,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAI,2BAA2B;MAC9EC,oBAAoB,EAAE,EAAE;MACxBC,yBAAyB,EAAE,CAAC;QAC1B,OAAO,EAAE,IAAI;QACb,OAAO,EAAE;MACX,CAAC,CAAC;MACFC,0BAA0B,EAAE,CAAC;QAC3B,OAAO,EAAE,MAAM;QACf,OAAO,EAAE;MACX,CAAC;IACH;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC;EACf,CAAC;EACDC,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACXC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxB,IAAI;MACrB,IAAI,CAACyB,KAAK,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAI,CAACA,KAAK,EAAE;QACZJ,OAAO,CAACC,GAAG,CAACF,KAAI,CAACtB,IAAI;QACrB,IAAI4B,IAAG,GAAI,EAAC;QACZ,IAAIC,CAAA,GAAI;QACR,KAAK,IAAIC,GAAE,IAAKR,KAAI,CAACtB,IAAI,EAAE;UACzB4B,IAAI,CAACC,CAAC,IAAI;YACR,WAAW,EAAEC,GAAG;YAChB,aAAa,EAAER,KAAI,CAACtB,IAAI,CAAC8B,GAAG;UAC9B;UACAD,CAAC,EAAC;QACJ;QACAtC,eAAe,CAACqC,IAAI,CAAC,CAACG,IAAI,CAAC,UAAAC,QAAO,EAAK;UACrC,IAAIA,QAAQ,CAACC,IAAG,KAAM,GAAG,EAAE;YACzBX,KAAI,CAACY,UAAU,CAACF,QAAQ,CAACG,GAAG;YAC5Bb,KAAI,CAACc,IAAG,GAAI,KAAI;YAChBd,KAAI,CAACH,OAAO,CAAC;YACb,IAAAkB,UAAA,GAAuCf,KAAI,CAACtB,IAAG;cAAvCC,YAAY,GAAAoC,UAAA,CAAZpC,YAAY;cAAEE,YAAW,GAAAkC,UAAA,CAAXlC,YAAW;YACjCmB,KAAI,CAACgB,MAAM,CAACC,MAAM,CAAC,iBAAiB,EAAE;cACpCpC,YAAY,EAAZA,YAAY;cACZF,YAAW,EAAXA;YACF,CAAC;UACH,OAAO;YACLqB,KAAI,CAACkB,QAAQ,CAACR,QAAQ,CAACG,GAAG;UAC5B;QACF,CAAC;MACH,CAAC;IACH,CAAC;IACDM,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAAChB,KAAK,CAAC,MAAM,CAAC,CAACiB,WAAW,CAAC;IACjC,CAAC;IACDC,wBAAwB,WAAxBA,wBAAwBA,CAACC,IAAI,EAAE;MAC7B,IAAMC,WAAU,GAAID,IAAI,CAACE,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChB,IAAI,CAACE,QAAQ,CAACC,KAAK,CAAC,YAAY;MAClC;MACA,OAAOH,WAAU;IACnB,CAAC;IACDI,aAAa,WAAbA,aAAaA,CAACjB,QAAQ,EAAEY,IAAI,EAAEM,QAAQ,EAAE;MACtC3B,OAAO,CAACC,GAAG,CAAC,KAAK;MACjB,IAAI,CAACxB,IAAI,CAACG,YAAW,GAAIS,OAAO,CAACC,GAAG,CAACC,gBAAe,GAAIkB,QAAQ,CAACrC,IAAI,CAACwD,SAAQ;MAC9E5B,OAAO,CAACC,GAAG,CAACQ,QAAQ,CAACrC,IAAI,CAACwD,SAAS;IACrC,CAAC;IACD,aACAhC,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAiC,MAAA;MACR,IAAI,CAACxD,OAAM,GAAI,IAAG;MAClBN,YAAY,CAAC,CAAC,CAACyC,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC9BoB,MAAI,CAACvD,UAAS,GAAImC,QAAQ,CAACrC,IAAG;QAC9ByD,MAAI,CAACxD,OAAM,GAAI,KAAI;QACnBwD,MAAI,CAACpD,IAAG,GAAIoD,MAAI,CAACvD,UAAS;QAC1B;QACA;QACA;QACA;MACF,CAAC;IACH,CAAC;IACDwD,MAAM,WAANA,MAAMA,CAACC,GAAG,EAAE;MACV,IAAM3D,IAAG,GAAI;QACXQ,YAAY,EAAE;MAChB;MACAR,IAAI,CAACQ,YAAW,GAAImD,GAAE;MACtB;MACA,IAAI,CAACC,YAAY,CAAC,IAAI,CAACzD,QAAQ,EAAEH,IAAI;MACrC;MACA,IAAI,CAAC6D,IAAG,GAAI,CAAC,IAAIC,IAAI,CAAC;IACxB,CAAC;IACD;IACAC,UAAU,WAAVA,UAAUA,CAACC,GAAG,EAAEC,MAAM,EAAE;MACtB,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACC,WAAW,EAAEH,GAAG,CAACI,UAAU;IAC9D,CAAC;IACD,aACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACC,WAAW,CAACC,SAAQ,GAAI;MAC7B,IAAI,CAAC/C,OAAO,CAAC;IACf,CAAC;IACDoC,YAAY,WAAZA,YAAYA,CAACvD,IAAI,EAAEL,IAAI,EAAE;MACvBK,IAAI,CAACmE,MAAM,CAACC,OAAO,CAAC,UAAAC,IAAG,EAAK;QAC1B,IAAMC,GAAE,GAAI3E,IAAI,CAAC0E,IAAI,CAACE,UAAU;QAChC,IAAID,GAAG,EAAE;UACPD,IAAI,CAACG,UAAU,CAACC,YAAW,GAAIH,GAAE;QACnC;MACF,CAAC;IACH,CAAC;IACDI,IAAI,WAAJA,IAAIA,CAAC5C,GAAG,EAAEnC,IAAI,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,IAAI;IAClB,CAAC;IACDgF,WAAW,WAAXA,WAAWA,CAAChF,IAAI,EAAE;MAAA,IAAAiF,MAAA;MAChB,IAAIhD,IAAG,GAAI,EAAC;MACZ,IAAIC,CAAA,GAAI;MACR,KAAK,IAAIC,GAAE,IAAKnC,IAAI,EAAE;QACpBiC,IAAI,CAACC,CAAC,IAAI;UACR,WAAW,EAAEC,GAAG;UAChB,aAAa,EAAEnC,IAAI,CAACmC,GAAG;QACzB;QACAD,CAAC,EAAC;MACJ;MACAtC,eAAe,CAACqC,IAAI,CAAC,CAACG,IAAI,CAAC,UAAAC,QAAO,EAAK;QACrC,IAAIA,QAAQ,CAACC,IAAG,KAAM,GAAG,EAAE;UACzB2C,MAAI,CAAC1C,UAAU,CAACF,QAAQ,CAACG,GAAG;UAC5ByC,MAAI,CAACxC,IAAG,GAAI,KAAI;UAChBwC,MAAI,CAACzD,OAAO,CAAC;QACf,OAAO;UACLyD,MAAI,CAACpC,QAAQ,CAACR,QAAQ,CAACG,GAAG;QAC5B;MACF,CAAC;IACH;EACF;AACF", "ignoreList": []}]}