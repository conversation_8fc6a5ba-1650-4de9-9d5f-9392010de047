{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\directive\\permission\\permission.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\directive\\permission\\permission.js", "mtime": 1753924830076}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3Iuc29tZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBpbnNlcnRlZDogZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgICB2YXIgdmFsdWUgPSBiaW5kaW5nLnZhbHVlOwogICAgdmFyIHJvbGVzID0gc3RvcmUuZ2V0dGVycyAmJiBzdG9yZS5nZXR0ZXJzLnJvbGVzOwogICAgaWYgKHZhbHVlICYmIHZhbHVlIGluc3RhbmNlb2YgQXJyYXkgJiYgdmFsdWUubGVuZ3RoID4gMCkgewogICAgICB2YXIgcGVybWlzc2lvblJvbGVzID0gdmFsdWU7CiAgICAgIHZhciBoYXNQZXJtaXNzaW9uID0gcm9sZXMuc29tZShmdW5jdGlvbiAocm9sZSkgewogICAgICAgIHJldHVybiBwZXJtaXNzaW9uUm9sZXMuaW5jbHVkZXMocm9sZSk7CiAgICAgIH0pOwogICAgICBpZiAoIWhhc1Blcm1pc3Npb24pIHsKICAgICAgICBlbC5wYXJlbnROb2RlICYmIGVsLnBhcmVudE5vZGUucmVtb3ZlQ2hpbGQoZWwpOwogICAgICB9CiAgICB9IGVsc2UgewogICAgICB0aHJvdyBuZXcgRXJyb3IoIm5lZWQgcm9sZXMhIExpa2Ugdi1wZXJtaXNzaW9uPVwiWydhZG1pbicsJ2VkaXRvciddXCIiKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["store", "inserted", "el", "binding", "vnode", "value", "roles", "getters", "Array", "length", "permissionRoles", "hasPermission", "some", "role", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/directive/permission/permission.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const roles = store.getters && store.getters.roles\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const permissionRoles = value\r\n\r\n      const hasPermission = roles.some(role => {\r\n        return permissionRoles.includes(role)\r\n      })\r\n\r\n      if (!hasPermission) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error(`need roles! Like v-permission=\"['admin','editor']\"`)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,SAAS;AAE3B,eAAe;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACO,OAAO,CAACD,KAAK;IAElD,IAAID,KAAK,IAAIA,KAAK,YAAYG,KAAK,IAAIH,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,eAAe,GAAGL,KAAK;MAE7B,IAAMM,aAAa,GAAGL,KAAK,CAACM,IAAI,CAAC,UAAAC,IAAI,EAAI;QACvC,OAAOH,eAAe,CAACI,QAAQ,CAACD,IAAI,CAAC;MACvC,CAAC,CAAC;MAEF,IAAI,CAACF,aAAa,EAAE;QAClBT,EAAE,CAACa,UAAU,IAAIb,EAAE,CAACa,UAAU,CAACC,WAAW,CAACd,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIe,KAAK,uDAAqD,CAAC;IACvE;EACF;AACF,CAAC", "ignoreList": []}]}