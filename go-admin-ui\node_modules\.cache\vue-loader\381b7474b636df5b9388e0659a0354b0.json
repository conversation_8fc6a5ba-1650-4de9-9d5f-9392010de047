{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-config\\set.vue?vue&type=template&id=61f2366f", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-config\\set.vue", "mtime": 1753924830273}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-config\\set.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAChJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/admin/sys-config/set.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-tabs tab-position=\"left\" style=\"height: 100%;\">\r\n          <el-tab-pane label=\"系统内置\">\r\n            <el-form label-width=\"80px\">\r\n              <div class=\"test-form\">\r\n                <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" size=\"small\" label-width=\"100px\">\r\n                  <el-form-item label=\"系统名称\" prop=\"sys_app_name\">\r\n                    <el-input v-model=\"form.sys_app_name\" placeholder=\"请输入系统名称\" clearable :style=\"{width: '100%'}\" />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"系统logo\" prop=\"sys_app_logo\" required>\r\n                    <img v-if=\"form.sys_app_logo\" :src=\"form.sys_app_logo\" class=\"el-upload el-upload--picture-card\" style=\"float:left\">\r\n                    <el-upload ref=\"sys_app_logo\" :headers=\"headers\" :file-list=\"sys_app_logofileList\" :action=\"sys_app_logoAction\" style=\"float:left\" :before-upload=\"sys_app_logoBeforeUpload\" list-type=\"picture-card\" :show-file-list=\"false\" :on-success=\"uploadSuccess\">\r\n                      <i class=\"el-icon-plus\" />\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"初始密码\" prop=\"sys_user_initPassword\">\r\n                    <el-input v-model=\"form.sys_user_initPassword\" placeholder=\"请输入初始密码\" clearable :style=\"{width: '100%'}\" />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"皮肤样式\" prop=\"sys_index_skinName\">\r\n                    <el-select v-model=\"form.sys_index_skinName\" placeholder=\"请选择皮肤样式\" clearable :style=\"{width: '100%'}\">\r\n                      <el-option v-for=\"(item, index) in sys_index_skinNameOptions\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"侧栏主题\" prop=\"sys_index_sideTheme\">\r\n                    <el-select v-model=\"form.sys_index_sideTheme\" placeholder=\"请选择侧栏主题\" clearable :style=\"{width: '100%'}\">\r\n                      <el-option v-for=\"(item, index) in sys_index_sideThemeOptions\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\" />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item size=\"large\">\r\n                    <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n                    <el-button @click=\"resetForm\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n            </el-form>\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"其他\">其他</el-tab-pane>\r\n        </el-tabs>\r\n\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getSetConfig,\r\n  updateSetConfig\r\n} from '@/api/admin/sys-config'\r\n\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SysConfigSet',\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 参数表格数据\r\n      configList: [],\r\n      formConf: {},\r\n      headers: { 'Authorization': 'Bearer ' + getToken() },\r\n      form: {\r\n        sys_app_name: undefined,\r\n        sys_app_logo: null,\r\n        sys_user_initPassword: undefined,\r\n        sys_index_skinName: undefined,\r\n        sys_index_sideTheme: undefined\r\n      },\r\n      rules: {\r\n        sys_app_name: [{\r\n          required: true,\r\n          message: '请输入系统名称',\r\n          trigger: 'blur'\r\n        }],\r\n        sys_user_initPassword: [{\r\n          required: true,\r\n          message: '请输入初始密码',\r\n          trigger: 'blur'\r\n        }],\r\n        sys_index_skinName: [{\r\n          required: true,\r\n          message: '请选择皮肤样式',\r\n          trigger: 'change'\r\n        }],\r\n        sys_index_sideTheme: [{\r\n          required: true,\r\n          message: '请选择侧栏主题',\r\n          trigger: 'change'\r\n        }]\r\n      },\r\n      sys_app_logoAction: process.env.VUE_APP_BASE_API + '/api/v1/public/uploadFile',\r\n      sys_app_logofileList: [],\r\n      sys_index_skinNameOptions: [{\r\n        'label': '蓝色',\r\n        'value': 'skin-blue'\r\n      }],\r\n      sys_index_sideThemeOptions: [{\r\n        'label': '深色主题',\r\n        'value': 'theme-dark'\r\n      }]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      console.log(this.form)\r\n      this.$refs['form'].validate(valid => {\r\n        if (!valid) return\r\n        console.log(this.form)\r\n        var list = []\r\n        var i = 0\r\n        for (var key in this.form) {\r\n          list[i] = {\r\n            'configKey': key,\r\n            'configValue': this.form[key]\r\n          }\r\n          i++\r\n        }\r\n        updateSetConfig(list).then(response => {\r\n          if (response.code === 200) {\r\n            this.msgSuccess(response.msg)\r\n            this.open = false\r\n            this.getList()\r\n            const { sys_app_name, sys_app_logo } = this.form\r\n            this.$store.commit('system/SET_INFO', {\r\n              sys_app_logo,\r\n              sys_app_name\r\n            })\r\n          } else {\r\n            this.msgError(response.msg)\r\n          }\r\n        })\r\n      })\r\n    },\r\n    resetForm() {\r\n      this.$refs['form'].resetFields()\r\n    },\r\n    sys_app_logoBeforeUpload(file) {\r\n      const isRightSize = file.size / 1024 / 1024 < 2\r\n      if (!isRightSize) {\r\n        this.$message.error('文件大小超过 2MB')\r\n      }\r\n      return isRightSize\r\n    },\r\n    uploadSuccess(response, file, fileList) {\r\n      console.log('sss')\r\n      this.form.sys_app_logo = process.env.VUE_APP_BASE_API + response.data.full_path\r\n      console.log(response.data.full_path)\r\n    },\r\n    /** 查询参数列表 */\r\n    getList() {\r\n      this.loading = true\r\n      getSetConfig().then(response => {\r\n        this.configList = response.data\r\n        this.loading = false\r\n        this.form = this.configList\r\n        // this.sys_app_logofileList = [this.configList.sys_app_logo]\r\n        // this.fillFormData(this.elForm, this.configList)\r\n        // 更新表单\r\n        // this.key2 = +new Date()\r\n      })\r\n    },\r\n    setUrl(url) {\r\n      const data = {\r\n        sys_app_logo: ''\r\n      }\r\n      data.sys_app_logo = url\r\n      // 回填数据\r\n      this.fillFormData(this.formConf, data)\r\n      // 更新表单\r\n      this.key2 = +new Date()\r\n    },\r\n    // 参数系统内置字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(this.typeOptions, row.configType)\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageIndex = 1\r\n      this.getList()\r\n    },\r\n    fillFormData(form, data) {\r\n      form.fields.forEach(item => {\r\n        const val = data[item.__vModel__]\r\n        if (val) {\r\n          item.__config__.defaultValue = val\r\n        }\r\n      })\r\n    },\r\n    bind(key, data) {\r\n      this.setUrl(data)\r\n    },\r\n    sumbitForm2(data) {\r\n      var list = []\r\n      var i = 0\r\n      for (var key in data) {\r\n        list[i] = {\r\n          'configKey': key,\r\n          'configValue': data[key]\r\n        }\r\n        i++\r\n      }\r\n      updateSetConfig(list).then(response => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}