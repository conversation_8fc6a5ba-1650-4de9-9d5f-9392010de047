{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Bar.vue?vue&type=template&id=2aa73eda", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Bar.vue", "mtime": 1753924830020}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgOnN0eWxlPSJ7IHBhZGRpbmc6ICcwIDAgMzJweCAzMnB4JyB9Ij4NCiAgICA8aDQgOnN0eWxlPSJ7IG1hcmdpbkJvdHRvbTogJzIwcHgnIH0iPnt7IHRpdGxlIH19PC9oND4NCiAgICA8di1jaGFydA0KICAgICAgaGVpZ2h0PSIyNTQiDQogICAgICA6ZGF0YT0iYXJyIg0KICAgICAgOmZvcmNlLWZpdD0idHJ1ZSINCiAgICAgIDpwYWRkaW5nPSJbJ2F1dG8nLCAnYXV0bycsICc0MCcsICc1MCddIg0KICAgID4NCiAgICAgIDx2LXRvb2x0aXAgLz4NCiAgICAgIDx2LWF4aXMgLz4NCiAgICAgIDx2LWJhciBwb3NpdGlvbj0ieCp5IiAvPg0KICAgIDwvdi1jaGFydD4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Bar.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/Bar.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :style=\"{ padding: '0 0 32px 32px' }\">\r\n    <h4 :style=\"{ marginBottom: '20px' }\">{{ title }}</h4>\r\n    <v-chart\r\n      height=\"254\"\r\n      :data=\"arr\"\r\n      :force-fit=\"true\"\r\n      :padding=\"['auto', 'auto', '40', '50']\"\r\n    >\r\n      <v-tooltip />\r\n      <v-axis />\r\n      <v-bar position=\"x*y\" />\r\n    </v-chart>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Bar',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    list: {\r\n      type: Array,\r\n      default: () => {}\r\n    },\r\n    scale: {\r\n      type: Array,\r\n      default: () => {\r\n        return [{\r\n          dataKey: 'x',\r\n          min: 2\r\n        }, {\r\n          dataKey: 'y',\r\n          title: '时间',\r\n          min: 1,\r\n          max: 22\r\n        }]\r\n      }\r\n    },\r\n    tooltip: {\r\n      type: Array,\r\n      default: () => {\r\n        return [\r\n          'x*y',\r\n          (x, y) => ({\r\n            name: x,\r\n            value: y\r\n          })\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      arr: []\r\n    }\r\n  },\r\n  mounted() {\r\n    setTimeout(_ => {\r\n      this.arr = this.list\r\n    }, 300)\r\n  }\r\n}\r\n</script>\r\n"]}]}