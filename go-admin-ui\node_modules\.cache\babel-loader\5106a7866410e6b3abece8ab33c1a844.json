{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-menu.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-menu.js", "mtime": 1753924829930}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsKCi8vIOafpeivouiPnOWNleWIl+ihqApleHBvcnQgZnVuY3Rpb24gbGlzdE1lbnUocXVlcnkpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL21lbnUnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i6I+c5Y2V6K+m57uGCmV4cG9ydCBmdW5jdGlvbiBnZXRNZW51KG1lbnVJZCkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvbWVudS8nICsgbWVudUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmn6Xor6Loj5zljZXkuIvmi4nmoJHnu5PmnoQKLy8gZXhwb3J0IGZ1bmN0aW9uIHRyZWVzZWxlY3QoKSB7Ci8vICAgcmV0dXJuIHJlcXVlc3QoewovLyAgICAgdXJsOiAnL2FwaS92MS9tZW51VHJlZXNlbGVjdCcsCi8vICAgICBtZXRob2Q6ICdnZXQnCi8vICAgfSkKLy8gfQoKLy8g5qC55o2u6KeS6ImySUTmn6Xor6Loj5zljZXkuIvmi4nmoJHnu5PmnoQKZXhwb3J0IGZ1bmN0aW9uIHJvbGVNZW51VHJlZXNlbGVjdChyb2xlSWQpIHsKICByZXR1cm4gcmVxdWVzdCh7CiAgICB1cmw6ICcvYXBpL3YxL3JvbGVNZW51VHJlZXNlbGVjdC8nICsgcm9sZUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7oj5zljZUKZXhwb3J0IGZ1bmN0aW9uIGFkZE1lbnUoZGF0YSkgewogIHJldHVybiByZXF1ZXN0KHsKICAgIHVybDogJy9hcGkvdjEvbWVudScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS56I+c5Y2VCmV4cG9ydCBmdW5jdGlvbiB1cGRhdGVNZW51KGRhdGEsIGlkKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9tZW51LycgKyBpZCwKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOiPnOWNlQpleHBvcnQgZnVuY3Rpb24gZGVsTWVudShkYXRhKSB7CiAgcmV0dXJuIHJlcXVlc3QoewogICAgdXJsOiAnL2FwaS92MS9tZW51JywKICAgIG1ldGhvZDogJ2RlbGV0ZScsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0="}, {"version": 3, "names": ["request", "listMenu", "query", "url", "method", "params", "getMenu", "menuId", "roleMenuTreeselect", "roleId", "addMenu", "data", "updateMenu", "id", "delMenu"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-menu.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询菜单列表\r\nexport function listMenu(query) {\r\n  return request({\r\n    url: '/api/v1/menu',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询菜单详细\r\nexport function getMenu(menuId) {\r\n  return request({\r\n    url: '/api/v1/menu/' + menuId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询菜单下拉树结构\r\n// export function treeselect() {\r\n//   return request({\r\n//     url: '/api/v1/menuTreeselect',\r\n//     method: 'get'\r\n//   })\r\n// }\r\n\r\n// 根据角色ID查询菜单下拉树结构\r\nexport function roleMenuTreeselect(roleId) {\r\n  return request({\r\n    url: '/api/v1/roleMenuTreeselect/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增菜单\r\nexport function addMenu(data) {\r\n  return request({\r\n    url: '/api/v1/menu',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改菜单\r\nexport function updateMenu(data, id) {\r\n  return request({\r\n    url: '/api/v1/menu/' + id,\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除菜单\r\nexport function delMenu(data) {\r\n  return request({\r\n    url: '/api/v1/menu',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASI,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,6BAA6B,GAAGM,MAAM;IAC3CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAEE,EAAE,EAAE;EACnC,OAAOb,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe,GAAGU,EAAE;IACzBT,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,OAAOA,CAACH,IAAI,EAAE;EAC5B,OAAOX,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,QAAQ;IAChBO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}