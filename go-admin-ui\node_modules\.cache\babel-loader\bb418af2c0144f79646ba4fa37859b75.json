{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\directive\\permission\\index.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\directive\\permission\\index.js", "mtime": 1753924830075}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHBlcm1pc3Npb24gZnJvbSAnLi9wZXJtaXNzaW9uJzsKaW1wb3J0IHBlcm1pc2FjdGlvbiBmcm9tICcuL3Blcm1pc2FjdGlvbic7CnZhciBpbnN0YWxsID0gZnVuY3Rpb24gaW5zdGFsbChWdWUpIHsKICBWdWUuZGlyZWN0aXZlKCdwZXJtaXNzaW9uJywgcGVybWlzc2lvbik7CiAgVnVlLmRpcmVjdGl2ZSgncGVybWlzYWN0aW9uJywgcGVybWlzYWN0aW9uKTsKfTsKaWYgKHdpbmRvdy5WdWUpIHsKICB3aW5kb3dbJ3Blcm1pc3Npb24nXSA9IHBlcm1pc3Npb247CiAgd2luZG93WydwZXJtaXNhY3Rpb24nXSA9IHBlcm1pc2FjdGlvbjsKICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW5kZWYKICBWdWUudXNlKGluc3RhbGwpOwp9CnBlcm1pc3Npb24uaW5zdGFsbCA9IGluc3RhbGw7CmV4cG9ydCBkZWZhdWx0IHBlcm1pc3Npb247"}, {"version": 3, "names": ["permission", "permisaction", "install", "<PERSON><PERSON>", "directive", "window", "use"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/directive/permission/index.js"], "sourcesContent": ["import permission from './permission'\r\nimport permisaction from './permisaction'\r\n\r\nconst install = function(Vue) {\r\n  Vue.directive('permission', permission)\r\n  Vue.directive('permisaction', permisaction)\r\n}\r\n\r\nif (window.Vue) {\r\n  window['permission'] = permission\r\n  window['permisaction'] = permisaction\r\n  // eslint-disable-next-line no-undef\r\n  Vue.use(install)\r\n}\r\n\r\npermission.install = install\r\nexport default permission\r\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,cAAc;AACrC,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAYC,GAAG,EAAE;EAC5BA,GAAG,CAACC,SAAS,CAAC,YAAY,EAAEJ,UAAU,CAAC;EACvCG,GAAG,CAACC,SAAS,CAAC,cAAc,EAAEH,YAAY,CAAC;AAC7C,CAAC;AAED,IAAII,MAAM,CAACF,GAAG,EAAE;EACdE,MAAM,CAAC,YAAY,CAAC,GAAGL,UAAU;EACjCK,MAAM,CAAC,cAAc,CAAC,GAAGJ,YAAY;EACrC;EACAE,GAAG,CAACG,GAAG,CAACJ,OAAO,CAAC;AAClB;AAEAF,UAAU,CAACE,OAAO,GAAGA,OAAO;AAC5B,eAAeF,UAAU", "ignoreList": []}]}