{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue", "mtime": 1753924830307}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBlcnJHaWYgZnJvbSAnQC9hc3NldHMvNDAxX2ltYWdlcy80MDEuZ2lmJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdQYWdlNDAxJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZXJyR2lmOiBlcnJHaWYgKyAnPycgKyArbmV3IERhdGUoKSwKICAgICAgZXdpemFyZENsYXA6ICdodHRwczovL3dwaW1nLndhbGxzdGNuLmNvbS8wMDdlZjUxNy1iYWZkLTQwNjYtYWFlNC02ODgzNjMyZDk2NDYnLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZQogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGJhY2s6IGZ1bmN0aW9uIGJhY2soKSB7CiAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5ub0dvQmFjaykgewogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICAgIHBhdGg6ICcvZGFzaGJvYXJkJwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["err<PERSON><PERSON>", "name", "data", "Date", "ewizard<PERSON>lap", "dialogVisible", "methods", "back", "$route", "query", "noGoBack", "$router", "push", "path", "go"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue"], "sourcesContent": ["<template>\r\n  <div class=\"errPage-container\">\r\n    <el-button icon=\"el-icon-arrow-left\" class=\"pan-back-btn\" @click=\"back\">\r\n      返回\r\n    </el-button>\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <h1 class=\"text-jumbo text-ginormous\">\r\n          Oops!\r\n        </h1>\r\n        gif来源<a href=\"https://zh.airbnb.com/\" target=\"_blank\">airbnb</a> 页面\r\n        <h2>你没有权限去该页面</h2>\r\n        <h6>如有不满请联系你领导</h6>\r\n        <ul class=\"list-unstyled\">\r\n          <li>或者你可以去:</li>\r\n          <li class=\"link-type\">\r\n            <router-link to=\"/dashboard\">\r\n              回首页\r\n            </router-link>\r\n          </li>\r\n          <li class=\"link-type\">\r\n            <a href=\"https://www.taobao.com/\">随便看看</a>\r\n          </li>\r\n          <li><a href=\"#\" @click.prevent=\"dialogVisible=true\">点我看图</a></li>\r\n        </ul>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"随便看\" :close-on-click-modal=\"false\">\r\n      <img :src=\"ewizardClap\" class=\"pan-img\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport errGif from '@/assets/401_images/401.gif'\r\n\r\nexport default {\r\n  name: 'Page401',\r\n  data() {\r\n    return {\r\n      errGif: errGif + '?' + +new Date(),\r\n      ewizardClap: 'https://wpimg.wallstcn.com/007ef517-bafd-4066-aae4-6883632d9646',\r\n      dialogVisible: false\r\n    }\r\n  },\r\n  methods: {\r\n    back() {\r\n      if (this.$route.query.noGoBack) {\r\n        this.$router.push({ path: '/dashboard' })\r\n      } else {\r\n        this.$router.go(-1)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .errPage-container {\r\n    width: 800px;\r\n    max-width: 100%;\r\n    margin: 100px auto;\r\n    .pan-back-btn {\r\n      background: #008489;\r\n      color: #fff;\r\n      border: none!important;\r\n    }\r\n    .pan-gif {\r\n      margin: 0 auto;\r\n      display: block;\r\n    }\r\n    .pan-img {\r\n      display: block;\r\n      margin: 0 auto;\r\n      width: 100%;\r\n    }\r\n    .text-jumbo {\r\n      font-size: 60px;\r\n      font-weight: 700;\r\n      color: #484848;\r\n    }\r\n    .list-unstyled {\r\n      font-size: 14px;\r\n      li {\r\n        padding-bottom: 5px;\r\n      }\r\n      a {\r\n        color: #008489;\r\n        text-decoration: none;\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";AAqCA,OAAOA,MAAK,MAAO,6BAA4B;AAE/C,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLF,MAAM,EAAEA,MAAK,GAAI,GAAE,GAAI,CAAC,IAAIG,IAAI,CAAC,CAAC;MAClCC,WAAW,EAAE,iEAAiE;MAC9EC,aAAa,EAAE;IACjB;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,IAAI,WAAJA,IAAIA,CAAA,EAAG;MACL,IAAI,IAAI,CAACC,MAAM,CAACC,KAAK,CAACC,QAAQ,EAAE;QAC9B,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;UAAEC,IAAI,EAAE;QAAa,CAAC;MAC1C,OAAO;QACL,IAAI,CAACF,OAAO,CAACG,EAAE,CAAC,CAAC,CAAC;MACpB;IACF;EACF;AACF", "ignoreList": []}]}