{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1753924830207}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Settings\\index.vue"], "names": [], "mappings": ";AAqEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC;MACH;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC;QACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjG;MACF;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC;MACH;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC;MACH;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC;IACH;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/Settings/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"drawer-container\">\r\n    <div>\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          页面设置\r\n        </div>\r\n        <div class=\"setting-drawer-block-checbox\">\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('light')\">\r\n            <img src=\"@/assets/light.svg\" alt=\"light\">\r\n            <div v-if=\"themeStyle === 'light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" />\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('dark')\">\r\n            <img src=\"@/assets/dark.svg\" alt=\"dark\">\r\n            <div v-if=\"themeStyle === 'dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n              <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\"><path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\" />\r\n                </svg>\r\n              </i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <el-divider />\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          主题设置\r\n        </div>\r\n        <div class=\"drawer-item\">\r\n          <span>主题颜色</span>\r\n          <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\r\n        </div>\r\n      </div>\r\n      <el-divider />\r\n      <div class=\"setting-drawer-content\">\r\n        <div class=\"setting-drawer-title\">\r\n          布局设置\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启任务栏</span>\r\n          <el-switch v-model=\"tagsView\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>Header 固定</span>\r\n          <el-switch v-model=\"fixedHeader\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>侧边栏Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" :active-color=\"activeColor\" class=\"drawer-switch\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {\r\n      activeColor: this.$store.state.settings.theme\r\n    }\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    },\r\n    themeStyle() {\r\n      return this.$store.state.settings.themeStyle\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.commit('permission/SET_SIDEBAR_ROUTERS', this.$store.state.permission.defaultRoutes)\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.activeColor = val\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'themeStyle',\r\n        value: val\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer-container {\r\n  padding: 24px;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  word-wrap: break-word;\r\n\r\n  .drawer-title {\r\n    margin-bottom: 12px;\r\n    color: rgba(0, 0, 0, .85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n  }\r\n\r\n  .drawer-item {\r\n    color: rgba(0, 0, 0, .65);\r\n    font-size: 14px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .drawer-switch {\r\n    float: right\r\n  }\r\n}\r\n.setting-drawer-content{\r\n  .setting-drawer-title{\r\n    margin-bottom: 12px;\r\n    color: rgba(0,0,0,.85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n    font-weight: bold;\r\n  }\r\n  .setting-drawer-block-checbox{\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n        img{\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n        .setting-drawer-block-checbox-selectIcon{\r\n            position: absolute;\r\n            top: 0;\r\n            right: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            padding-top: 15px;\r\n            padding-left: 24px;\r\n            color: #1890ff;\r\n            font-weight: 700;\r\n            font-size: 14px;\r\n        }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}