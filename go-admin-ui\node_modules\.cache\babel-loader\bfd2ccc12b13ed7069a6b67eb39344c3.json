{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userInfo.vue", "mtime": 1753924830462}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["updateUser", "props", "user", "type", "Object", "data", "rules", "nick<PERSON><PERSON>", "required", "message", "trigger", "email", "phone", "pattern", "methods", "submit", "_this", "$refs", "validate", "valid", "then", "response", "code", "msgSuccess", "msg", "msgError", "close", "$store", "dispatch", "$route", "$router", "push", "path"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\userInfo.vue"], "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"user\" :rules=\"rules\" label-width=\"80px\">\r\n    <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n      <el-input v-model=\"user.nickName\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"手机号码\" prop=\"phone\">\r\n      <el-input v-model=\"user.phone\" maxlength=\"11\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"邮箱\" prop=\"email\">\r\n      <el-input v-model=\"user.email\" maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"性别\">\r\n      <el-radio-group v-model=\"user.sex\">\r\n        <el-radio label=\"0\">男</el-radio>\r\n        <el-radio label=\"1\">女</el-radio>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item>\r\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\r\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { updateUser } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  props: {\r\n    // eslint-disable-next-line vue/require-default-prop\r\n    user: { type: Object }\r\n  },\r\n  data() {\r\n    return {\r\n      // 表单校验\r\n      rules: {\r\n        nickName: [\r\n          { required: true, message: '用户昵称不能为空', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          { required: true, message: '邮箱地址不能为空', trigger: 'blur' },\r\n          {\r\n            type: 'email',\r\n            message: \"'请输入正确的邮箱地址\",\r\n            trigger: ['blur', 'change']\r\n          }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '手机号码不能为空', trigger: 'blur' },\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: '请输入正确的手机号码',\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          updateUser(this.user).then(response => {\r\n            if (response.code === 200) {\r\n              this.msgSuccess(response.msg)\r\n            } else {\r\n              this.msgError(response.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    close() {\r\n      this.$store.dispatch('tagsView/delView', this.$route)\r\n      this.$router.push({ path: '/index' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";AAyBA,SAASA,UAAS,QAAS,sBAAqB;AAEhD,eAAe;EACbC,KAAK,EAAE;IACL;IACAC,IAAI,EAAE;MAAEC,IAAI,EAAEC;IAAO;EACvB,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,KAAK,EAAE;QACLC,QAAQ,EAAE,CACR;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,EACxD;QACDC,KAAK,EAAE,CACL;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC,EACxD;UACEP,IAAI,EAAE,OAAO;UACbM,OAAO,EAAE,aAAa;UACtBC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;QAC5B,EACD;QACDE,KAAK,EAAE,CACL;UAAEJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC,EACxD;UACEG,OAAO,EAAE,8BAA8B;UACvCJ,OAAO,EAAE,YAAY;UACrBC,OAAO,EAAE;QACX;MAEJ;IACF;EACF,CAAC;EACDI,OAAO,EAAE;IACPC,MAAM,WAANA,MAAMA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACP,IAAI,CAACC,KAAK,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAIA,KAAK,EAAE;UACTnB,UAAU,CAACgB,KAAI,CAACd,IAAI,CAAC,CAACkB,IAAI,CAAC,UAAAC,QAAO,EAAK;YACrC,IAAIA,QAAQ,CAACC,IAAG,KAAM,GAAG,EAAE;cACzBN,KAAI,CAACO,UAAU,CAACF,QAAQ,CAACG,GAAG;YAC9B,OAAO;cACLR,KAAI,CAACS,QAAQ,CAACJ,QAAQ,CAACG,GAAG;YAC5B;UACF,CAAC;QACH;MACF,CAAC;IACH,CAAC;IACDE,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAACC,MAAM;MACpD,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAS,CAAC;IACtC;EACF;AACF", "ignoreList": []}]}