{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\request.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\request.js", "mtime": 1753924830260}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7CmltcG9ydCB7IE1lc3NhZ2VCb3gsIE1lc3NhZ2UgfSBmcm9tICdlbGVtZW50LXVpJzsKaW1wb3J0IHN0b3JlIGZyb20gJ0Avc3RvcmUnOwppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gJ0AvdXRpbHMvYXV0aCc7CgovLyBjcmVhdGUgYW4gYXhpb3MgaW5zdGFuY2UKdmFyIHNlcnZpY2UgPSBheGlvcy5jcmVhdGUoewogIGJhc2VVUkw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEksCiAgLy8gdXJsID0gYmFzZSB1cmwgKyByZXF1ZXN0IHVybAogIC8vIHdpdGhDcmVkZW50aWFsczogdHJ1ZSwgLy8gc2VuZCBjb29raWVzIHdoZW4gY3Jvc3MtZG9tYWluIHJlcXVlc3RzCiAgdGltZW91dDogMTAwMDAgLy8gcmVxdWVzdCB0aW1lb3V0Cn0pOwoKLy8gcmVxdWVzdCBpbnRlcmNlcHRvcgpzZXJ2aWNlLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShmdW5jdGlvbiAoY29uZmlnKSB7CiAgLy8gZG8gc29tZXRoaW5nIGJlZm9yZSByZXF1ZXN0IGlzIHNlbnQKCiAgaWYgKHN0b3JlLmdldHRlcnMudG9rZW4pIHsKICAgIC8vIGxldCBlYWNoIHJlcXVlc3QgY2FycnkgdG9rZW4KICAgIC8vIFsnWC1Ub2tlbiddIGlzIGEgY3VzdG9tIGhlYWRlcnMga2V5CiAgICAvLyBwbGVhc2UgbW9kaWZ5IGl0IGFjY29yZGluZyB0byB0aGUgYWN0dWFsIHNpdHVhdGlvbgogICAgY29uZmlnLmhlYWRlcnNbJ0F1dGhvcml6YXRpb24nXSA9ICdCZWFyZXIgJyArIGdldFRva2VuKCk7CiAgICBjb25maWcuaGVhZGVyc1snQ29udGVudC1UeXBlJ10gPSAnYXBwbGljYXRpb24vanNvbic7CiAgfQogIHJldHVybiBjb25maWc7Cn0sIGZ1bmN0aW9uIChlcnJvcikgewogIC8vIGRvIHNvbWV0aGluZyB3aXRoIHJlcXVlc3QgZXJyb3IKICBjb25zb2xlLmxvZyhlcnJvcik7IC8vIGZvciBkZWJ1ZwogIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7Cn0pOwoKLy8gcmVzcG9uc2UgaW50ZXJjZXB0b3IKc2VydmljZS5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKAovKioNCiAqIElmIHlvdSB3YW50IHRvIGdldCBodHRwIGluZm9ybWF0aW9uIHN1Y2ggYXMgaGVhZGVycyBvciBzdGF0dXMNCiAqIFBsZWFzZSByZXR1cm4gIHJlc3BvbnNlID0+IHJlc3BvbnNlDQoqLwoKLyoqDQogKiBEZXRlcm1pbmUgdGhlIHJlcXVlc3Qgc3RhdHVzIGJ5IGN1c3RvbSBjb2RlDQogKiBIZXJlIGlzIGp1c3QgYW4gZXhhbXBsZQ0KICogWW91IGNhbiBhbHNvIGp1ZGdlIHRoZSBzdGF0dXMgYnkgSFRUUCBTdGF0dXMgQ29kZQ0KICovCmZ1bmN0aW9uIChyZXNwb25zZSkgewogIHZhciBjb2RlID0gcmVzcG9uc2UuZGF0YS5jb2RlOwogIGlmIChjb2RlID09PSA0MDEpIHsKICAgIHN0b3JlLmRpc3BhdGNoKCd1c2VyL3Jlc2V0VG9rZW4nKTsKICAgIGlmIChsb2NhdGlvbi5ocmVmLmluZGV4T2YoJ2xvZ2luJykgIT09IC0xKSB7CiAgICAgIGxvY2F0aW9uLnJlbG9hZCgpOyAvLyDkuLrkuobph43mlrDlrp7kvovljJZ2dWUtcm91dGVy5a+56LGhIOmBv+WFjWJ1ZwogICAgfSBlbHNlIHsKICAgICAgTWVzc2FnZUJveC5jb25maXJtKCfnmbvlvZXnirbmgIHlt7Lov4fmnJ/vvIzmgqjlj6/ku6Xnu6fnu63nlZnlnKjor6XpobXpnaLvvIzmiJbogIXph43mlrDnmbvlvZUnLCAn57O757uf5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn6YeN5paw55m75b2VJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgbG9jYXRpb24ucmVsb2FkKCk7IC8vIOS4uuS6humHjeaWsOWunuS+i+WMlnZ1ZS1yb3V0ZXLlr7nosaEg6YG/5YWNYnVnCiAgICAgIH0pOwogICAgfQogIH0gZWxzZSBpZiAoY29kZSA9PT0gNjQwMSkgewogICAgc3RvcmUuZGlzcGF0Y2goJ3VzZXIvcmVzZXRUb2tlbicpOwogICAgTWVzc2FnZUJveC5jb25maXJtKCfnmbvlvZXnirbmgIHlt7Lov4fmnJ/vvIzmgqjlj6/ku6Xnu6fnu63nlZnlnKjor6XpobXpnaLvvIzmiJbogIXph43mlrDnmbvlvZUnLCAn57O757uf5o+Q56S6JywgewogICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+mHjeaWsOeZu+W9lScsCiAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICB0eXBlOiAnd2FybmluZycKICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICBsb2NhdGlvbi5yZWxvYWQoKTsgLy8g5Li65LqG6YeN5paw5a6e5L6L5YyWdnVlLXJvdXRlcuWvueixoSDpgb/lhY1idWcKICAgIH0pOwogICAgcmV0dXJuIGZhbHNlOwogIH0gZWxzZSBpZiAoY29kZSA9PT0gNDAwIHx8IGNvZGUgPT09IDQwMykgewogICAgTWVzc2FnZSh7CiAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLmRhdGEubXNnLAogICAgICB0eXBlOiAnZXJyb3InLAogICAgICBkdXJhdGlvbjogNSAqIDEwMDAKICAgIH0pOwogIH0gZWxzZSBpZiAoY29kZSAhPT0gMjAwKSB7CiAgICAvLyBOb3RpZmljYXRpb24uZXJyb3IoewogICAgLy8gICB0aXRsZTogcmVzcG9uc2UuZGF0YS5tc2cKICAgIC8vIH0pCiAgICBNZXNzYWdlKHsKICAgICAgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tc2csCiAgICAgIHR5cGU6ICdlcnJvcicKICAgIH0pOwogICAgcmV0dXJuIFByb21pc2UucmVqZWN0KCdlcnJvcicpOwogIH0gZWxzZSB7CiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTsKICB9Cn0sIGZ1bmN0aW9uIChlcnJvcikgewogIGlmIChlcnJvci5tZXNzYWdlID09PSAnTmV0d29yayBFcnJvcicpIHsKICAgIE1lc3NhZ2UoewogICAgICBtZXNzYWdlOiAn5pyN5Yqh5Zmo6L+e5o6l5byC5bi477yM6K+35qOA5p+l5pyN5Yqh5Zmo77yBJywKICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgZHVyYXRpb246IDUgKiAxMDAwCiAgICB9KTsKICAgIHJldHVybjsKICB9CiAgY29uc29sZS5sb2coJ2VycicgKyBlcnJvcik7IC8vIGZvciBkZWJ1ZwoKICBNZXNzYWdlKHsKICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsCiAgICB0eXBlOiAnZXJyb3InLAogICAgZHVyYXRpb246IDUgKiAxMDAwCiAgfSk7CiAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTsKfSk7CmV4cG9ydCBkZWZhdWx0IHNlcnZpY2U7"}, {"version": 3, "names": ["axios", "MessageBox", "Message", "store", "getToken", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "getters", "token", "headers", "error", "console", "log", "Promise", "reject", "response", "code", "data", "dispatch", "location", "href", "indexOf", "reload", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "message", "msg", "duration"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { MessageBox, Message } from 'element-ui'\r\nimport store from '@/store'\r\nimport { getToken } from '@/utils/auth'\r\n\r\n// create an axios instance\r\nconst service = axios.create({\r\n  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url\r\n  // withCredentials: true, // send cookies when cross-domain requests\r\n  timeout: 10000 // request timeout\r\n})\r\n\r\n// request interceptor\r\nservice.interceptors.request.use(\r\n  config => {\r\n    // do something before request is sent\r\n\r\n    if (store.getters.token) {\r\n      // let each request carry token\r\n      // ['X-Token'] is a custom headers key\r\n      // please modify it according to the actual situation\r\n      config.headers['Authorization'] = 'Bearer ' + getToken()\r\n      config.headers['Content-Type'] = 'application/json'\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    // do something with request error\r\n    console.log(error) // for debug\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// response interceptor\r\nservice.interceptors.response.use(\r\n  /**\r\n   * If you want to get http information such as headers or status\r\n   * Please return  response => response\r\n  */\r\n\r\n  /**\r\n   * Determine the request status by custom code\r\n   * Here is just an example\r\n   * You can also judge the status by HTTP Status Code\r\n   */\r\n  response => {\r\n    const code = response.data.code\r\n    if (code === 401) {\r\n      store.dispatch('user/resetToken')\r\n      if (location.href.indexOf('login') !== -1) {\r\n        location.reload() // 为了重新实例化vue-router对象 避免bug\r\n      } else {\r\n        MessageBox.confirm(\r\n          '登录状态已过期，您可以继续留在该页面，或者重新登录',\r\n          '系统提示',\r\n          {\r\n            confirmButtonText: '重新登录',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }\r\n        ).then(() => {\r\n          location.reload() // 为了重新实例化vue-router对象 避免bug\r\n        })\r\n      }\r\n    } else if (code === 6401) {\r\n      store.dispatch('user/resetToken')\r\n      MessageBox.confirm(\r\n        '登录状态已过期，您可以继续留在该页面，或者重新登录',\r\n        '系统提示',\r\n        {\r\n          confirmButtonText: '重新登录',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }\r\n      ).then(() => {\r\n        location.reload() // 为了重新实例化vue-router对象 避免bug\r\n      })\r\n      return false\r\n    } else if (code === 400 || code === 403) {\r\n      Message({\r\n        message: response.data.msg,\r\n        type: 'error',\r\n        duration: 5 * 1000\r\n      })\r\n    } else if (code !== 200) {\r\n      // Notification.error({\r\n      //   title: response.data.msg\r\n      // })\r\n      Message({\r\n        message: response.data.msg,\r\n        type: 'error'\r\n      })\r\n      return Promise.reject('error')\r\n    } else {\r\n      return response.data\r\n    }\r\n  },\r\n  error => {\r\n    if (error.message === 'Network Error') {\r\n      Message({\r\n        message: '服务器连接异常，请检查服务器！',\r\n        type: 'error',\r\n        duration: 5 * 1000\r\n      })\r\n      return\r\n    }\r\n    console.log('err' + error) // for debug\r\n\r\n    Message({\r\n      message: error.message,\r\n      type: 'error',\r\n      duration: 5 * 1000\r\n    })\r\n\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nexport default service\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,QAAQ,YAAY;AAChD,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,QAAQ,cAAc;;AAEvC;AACA,IAAMC,OAAO,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EAAE;EACvC;EACAC,OAAO,EAAE,KAAK,CAAC;AACjB,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9B,UAAAC,MAAM,EAAI;EACR;;EAEA,IAAIZ,KAAK,CAACa,OAAO,CAACC,KAAK,EAAE;IACvB;IACA;IACA;IACAF,MAAM,CAACG,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAGd,QAAQ,CAAC,CAAC;IACxDW,MAAM,CAACG,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB;EACrD;EACA,OAAOH,MAAM;AACf,CAAC,EACD,UAAAI,KAAK,EAAI;EACP;EACAC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAC;EACnB,OAAOG,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,OAAO,CAACO,YAAY,CAACY,QAAQ,CAACV,GAAG;AAC/B;AACF;AACA;AACA;;AAEE;AACF;AACA;AACA;AACA;AACE,UAAAU,QAAQ,EAAI;EACV,IAAMC,IAAI,GAAGD,QAAQ,CAACE,IAAI,CAACD,IAAI;EAC/B,IAAIA,IAAI,KAAK,GAAG,EAAE;IAChBtB,KAAK,CAACwB,QAAQ,CAAC,iBAAiB,CAAC;IACjC,IAAIC,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MACzCF,QAAQ,CAACG,MAAM,CAAC,CAAC,EAAC;IACpB,CAAC,MAAM;MACL9B,UAAU,CAAC+B,OAAO,CAChB,2BAA2B,EAC3B,MAAM,EACN;QACEC,iBAAiB,EAAE,MAAM;QACzBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CACF,CAAC,CAACC,IAAI,CAAC,YAAM;QACXR,QAAQ,CAACG,MAAM,CAAC,CAAC,EAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,MAAM,IAAIN,IAAI,KAAK,IAAI,EAAE;IACxBtB,KAAK,CAACwB,QAAQ,CAAC,iBAAiB,CAAC;IACjC1B,UAAU,CAAC+B,OAAO,CAChB,2BAA2B,EAC3B,MAAM,EACN;MACEC,iBAAiB,EAAE,MAAM;MACzBC,gBAAgB,EAAE,IAAI;MACtBC,IAAI,EAAE;IACR,CACF,CAAC,CAACC,IAAI,CAAC,YAAM;MACXR,QAAQ,CAACG,MAAM,CAAC,CAAC,EAAC;IACpB,CAAC,CAAC;IACF,OAAO,KAAK;EACd,CAAC,MAAM,IAAIN,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;IACvCvB,OAAO,CAAC;MACNmC,OAAO,EAAEb,QAAQ,CAACE,IAAI,CAACY,GAAG;MAC1BH,IAAI,EAAE,OAAO;MACbI,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAId,IAAI,KAAK,GAAG,EAAE;IACvB;IACA;IACA;IACAvB,OAAO,CAAC;MACNmC,OAAO,EAAEb,QAAQ,CAACE,IAAI,CAACY,GAAG;MAC1BH,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAOb,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOC,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC,EACD,UAAAP,KAAK,EAAI;EACP,IAAIA,KAAK,CAACkB,OAAO,KAAK,eAAe,EAAE;IACrCnC,OAAO,CAAC;MACNmC,OAAO,EAAE,iBAAiB;MAC1BF,IAAI,EAAE,OAAO;MACbI,QAAQ,EAAE,CAAC,GAAG;IAChB,CAAC,CAAC;IACF;EACF;EACAnB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGF,KAAK,CAAC,EAAC;;EAE3BjB,OAAO,CAAC;IACNmC,OAAO,EAAElB,KAAK,CAACkB,OAAO;IACtBF,IAAI,EAAE,OAAO;IACbI,QAAQ,EAAE,CAAC,GAAG;EAChB,CAAC,CAAC;EAEF,OAAOjB,OAAO,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAed,OAAO", "ignoreList": []}]}