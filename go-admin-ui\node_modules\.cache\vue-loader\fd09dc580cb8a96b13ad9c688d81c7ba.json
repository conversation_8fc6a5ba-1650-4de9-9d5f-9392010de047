{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue?vue&type=style&index=0&id=35f3a2c1&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue", "mtime": 1753924830064}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZWwtbWVudS0taG9yaXpvbnRhbCA+IC5lbC1tZW51LWl0ZW0gew0KICBmbG9hdDogbGVmdDsNCiAgaGVpZ2h0OiA1MHB4Ow0KICBsaW5lLWhlaWdodDogNTBweDsNCiAgbWFyZ2luOiAwOw0KICBib3JkZXItYm90dG9tOiAzcHggc29saWQgdHJhbnNwYXJlbnQ7DQogIGNvbG9yOiAjOTk5MDkzOw0KICBwYWRkaW5nOiAwIDVweDsNCiAgbWFyZ2luOiAwIDEwcHg7DQp9DQoNCi5lbC1tZW51LS1ob3Jpem9udGFsID4gLmVsLW1lbnUtaXRlbS5pcy1hY3RpdmUgew0KICBib3JkZXItYm90dG9tOiAzcHggc29saWQgIzQwOWVmZjsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\TopNav\\index.vue"], "names": [], "mappings": ";AAgIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/TopNav/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item\r\n        v-if=\"index < visibleNumber\"\r\n        :key=\"index\"\r\n        :index=\"item.path\"\r\n      ><svg-icon :icon-class=\"item.meta.icon\" />\r\n        {{ item.meta.title }}</el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu v-if=\"topMenus.length > visibleNumber\" index=\"more\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          v-if=\"index >= visibleNumber\"\r\n          :key=\"index\"\r\n          :index=\"item.path\"\r\n        ><svg-icon :icon-class=\"item.meta.icon\" />\r\n          {{ item.meta.title }}</el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from '@/router'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 是否为首次加载\r\n      isFrist: false\r\n    }\r\n  },\r\n  computed: {\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      return this.routers.map((menu) => ({\r\n        ...menu,\r\n        children: undefined\r\n      }))\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = []\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            router.children[item].parentPath = router.path\r\n          }\r\n          childrenMenus.push(router.children[item])\r\n        }\r\n      })\r\n      return constantRoutes.concat(childrenMenus)\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path\r\n      let activePath = this.routers[0].path\r\n      if (path.lastIndexOf('/') > 0) {\r\n        const tmpPath = path.substring(1, path.length)\r\n        activePath = '/' + tmpPath.substring(0, tmpPath.indexOf('/'))\r\n      } else if (path === '/index' || path === '') {\r\n        if (!this.isFrist) {\r\n          // eslint-disable-next-line vue/no-side-effects-in-computed-properties\r\n          this.isFrist = true\r\n        } else {\r\n          activePath = 'index'\r\n        }\r\n      }\r\n      this.activeRoutes(activePath)\r\n      return activePath\r\n    }\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber()\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width - 200\r\n      const elWidth = this.$el.getBoundingClientRect().width\r\n      const menuItemNodes = this.$el.children\r\n      const menuWidth = Array.from(menuItemNodes).map(\r\n        (i) => i.getBoundingClientRect().width\r\n      )\r\n      this.visibleNumber = (\r\n        parseInt(width - elWidth) / parseInt(menuWidth)\r\n      ).toFixed(0)\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      if (key.indexOf('http://') !== -1 || key.indexOf('https://') !== -1) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, '_blank')\r\n      } else {\r\n        this.activeRoutes(key)\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = []\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key === item.parentPath || (key === 'index' && item.path === '')) {\r\n            routes.push(item)\r\n          }\r\n        })\r\n      }\r\n      this.$store.commit('permission/SET_SIDEBAR_ROUTERS', routes)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  margin: 0;\r\n  border-bottom: 3px solid transparent;\r\n  color: #999093;\r\n  padding: 0 5px;\r\n  margin: 0 10px;\r\n}\r\n\r\n.el-menu--horizontal > .el-menu-item.is-active {\r\n  border-bottom: 3px solid #409eff;\r\n  color: #303133;\r\n}\r\n</style>\r\n"]}]}