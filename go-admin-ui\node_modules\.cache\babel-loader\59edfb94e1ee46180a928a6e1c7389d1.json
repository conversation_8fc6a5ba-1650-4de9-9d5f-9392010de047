{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue", "mtime": 1753924830453}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHVzZXJBdmF0YXIgZnJvbSAnLi91c2VyQXZhdGFyJzsKaW1wb3J0IHVzZXJJbmZvIGZyb20gJy4vdXNlckluZm8nOwppbXBvcnQgcmVzZXRQd2QgZnJvbSAnLi9yZXNldFB3ZCc7CmltcG9ydCB7IGdldFVzZXJQcm9maWxlIH0gZnJvbSAnQC9hcGkvYWRtaW4vc3lzLXVzZXInOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1Byb2ZpbGUnLAogIGNvbXBvbmVudHM6IHsKICAgIHVzZXJBdmF0YXI6IHVzZXJBdmF0YXIsCiAgICB1c2VySW5mbzogdXNlckluZm8sCiAgICByZXNldFB3ZDogcmVzZXRQd2QKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VyOiB7fSwKICAgICAgcm9sZUdyb3VwOiB7fSwKICAgICAgcG9zdEdyb3VwOiB7fSwKICAgICAgZGVwdEdyb3VwOiB7fSwKICAgICAgYWN0aXZlVGFiOiAndXNlcmluZm8nLAogICAgICByb2xlSWRzOiB1bmRlZmluZWQsCiAgICAgIHBvc3RJZHM6IHVuZGVmaW5lZCwKICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgcG9zdE5hbWU6IHVuZGVmaW5lZCwKICAgICAgZGVwdDoge30sCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQKICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRVc2VyKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRVc2VyOiBmdW5jdGlvbiBnZXRVc2VyKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICBnZXRVc2VyUHJvZmlsZSgpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMudXNlciA9IHJlc3BvbnNlLmRhdGEudXNlcjsKICAgICAgICBfdGhpcy5yb2xlSWRzID0gcmVzcG9uc2UuZGF0YS51c2VyLnJvbGVJZHM7CiAgICAgICAgX3RoaXMucm9sZUdyb3VwID0gcmVzcG9uc2UuZGF0YS5yb2xlczsKICAgICAgICBpZiAoX3RoaXMucm9sZUlkc1swXSkgewogICAgICAgICAgZm9yICh2YXIga2V5IGluIF90aGlzLnJvbGVHcm91cCkgewogICAgICAgICAgICBpZiAoX3RoaXMucm9sZUlkc1swXSA9PT0gX3RoaXMucm9sZUdyb3VwW2tleV0ucm9sZUlkKSB7CiAgICAgICAgICAgICAgX3RoaXMucm9sZU5hbWUgPSBfdGhpcy5yb2xlR3JvdXBba2V5XS5yb2xlTmFtZTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpcy5yb2xlTmFtZSA9ICfmmoLml6AnOwogICAgICAgIH0KICAgICAgICBfdGhpcy5kZXB0ID0gcmVzcG9uc2UuZGF0YS51c2VyLmRlcHQ7CiAgICAgICAgX3RoaXMuZGVwdE5hbWUgPSBfdGhpcy5kZXB0LmRlcHROYW1lOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["userAvatar", "userInfo", "resetPwd", "getUserProfile", "name", "components", "data", "user", "roleGroup", "postGroup", "deptGroup", "activeTab", "roleIds", "undefined", "postIds", "<PERSON><PERSON><PERSON>", "postName", "dept", "deptName", "created", "getUser", "methods", "_this", "then", "response", "roles", "key", "roleId"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"6\" :xs=\"24\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>个人信息</span>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-center\">\r\n                <userAvatar :user=\"user\" />\r\n              </div>\r\n              <ul class=\"list-group list-group-striped\">\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />用户名称\r\n                  <div class=\"pull-right\">{{ user.username }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"phone\" />手机号码\r\n                  <div class=\"pull-right\">{{ user.phone }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"email\" />用户邮箱\r\n                  <div class=\"pull-right\">{{ user.email }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"tree\" />所属部门\r\n                  <div class=\"pull-right\">{{ deptName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"peoples\" />所属角色\r\n                  <div class=\"pull-right\">{{ roleName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"date\" />创建日期\r\n                  <div class=\"pull-right\">{{ user.createdAt }}</div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <el-card>\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>基本资料</span>\r\n            </div>\r\n            <el-tabs v-model=\"activeTab\">\r\n              <el-tab-pane label=\"基本资料\" name=\"userinfo\">\r\n                <userInfo :user=\"user\" />\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"修改密码\" name=\"resetPwd\">\r\n                <resetPwd :user=\"user\" />\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </el-card>\r\n        </el-col>\r\n      </el-row>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport userAvatar from './userAvatar'\r\nimport userInfo from './userInfo'\r\nimport resetPwd from './resetPwd'\r\nimport { getUserProfile } from '@/api/admin/sys-user'\r\n\r\nexport default {\r\n  name: 'Profile',\r\n  components: { userAvatar, userInfo, resetPwd },\r\n  data() {\r\n    return {\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      deptGroup: {},\r\n      activeTab: 'userinfo',\r\n      roleIds: undefined,\r\n      postIds: undefined,\r\n      roleName: undefined,\r\n      postName: undefined,\r\n      dept: {},\r\n      deptName: undefined\r\n    }\r\n  },\r\n  created() {\r\n    this.getUser()\r\n  },\r\n  methods: {\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data.user\r\n        this.roleIds = response.data.user.roleIds\r\n        this.roleGroup = response.data.roles\r\n\r\n        if (this.roleIds[0]) {\r\n          for (const key in this.roleGroup) {\r\n            if (this.roleIds[0] === this.roleGroup[key].roleId) {\r\n              this.roleName = this.roleGroup[key].roleName\r\n            }\r\n          }\r\n        } else {\r\n          this.roleName = '暂无'\r\n        }\r\n        this.dept = response.data.user.dept\r\n        this.deptName = this.dept.deptName\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .list-group-item{\r\n    padding: 18px 0;\r\n  }\r\n  .svg-icon{\r\n    margin-right: 5px;\r\n  }\r\n</style>\r\n"], "mappings": "AA+DA,OAAOA,UAAS,MAAO,cAAa;AACpC,OAAOC,QAAO,MAAO,YAAW;AAChC,OAAOC,QAAO,MAAO,YAAW;AAChC,SAASC,cAAa,QAAS,sBAAqB;AAEpD,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE;IAAEL,UAAU,EAAVA,UAAU;IAAEC,QAAQ,EAARA,QAAQ;IAAEC,QAAO,EAAPA;EAAS,CAAC;EAC9CI,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE,CAAC,CAAC;MACRC,SAAS,EAAE,CAAC,CAAC;MACbC,SAAS,EAAE,CAAC,CAAC;MACbC,SAAS,EAAE,CAAC,CAAC;MACbC,SAAS,EAAE,UAAU;MACrBC,OAAO,EAAEC,SAAS;MAClBC,OAAO,EAAED,SAAS;MAClBE,QAAQ,EAAEF,SAAS;MACnBG,QAAQ,EAAEH,SAAS;MACnBI,IAAI,EAAE,CAAC,CAAC;MACRC,QAAQ,EAAEL;IACZ;EACF,CAAC;EACDM,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,OAAO,CAAC;EACf,CAAC;EACDC,OAAO,EAAE;IACPD,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAAE,KAAA;MACRnB,cAAc,CAAC,CAAC,CAACoB,IAAI,CAAC,UAAAC,QAAO,EAAK;QAChCF,KAAI,CAACf,IAAG,GAAIiB,QAAQ,CAAClB,IAAI,CAACC,IAAG;QAC7Be,KAAI,CAACV,OAAM,GAAIY,QAAQ,CAAClB,IAAI,CAACC,IAAI,CAACK,OAAM;QACxCU,KAAI,CAACd,SAAQ,GAAIgB,QAAQ,CAAClB,IAAI,CAACmB,KAAI;QAEnC,IAAIH,KAAI,CAACV,OAAO,CAAC,CAAC,CAAC,EAAE;UACnB,KAAK,IAAMc,GAAE,IAAKJ,KAAI,CAACd,SAAS,EAAE;YAChC,IAAIc,KAAI,CAACV,OAAO,CAAC,CAAC,MAAMU,KAAI,CAACd,SAAS,CAACkB,GAAG,CAAC,CAACC,MAAM,EAAE;cAClDL,KAAI,CAACP,QAAO,GAAIO,KAAI,CAACd,SAAS,CAACkB,GAAG,CAAC,CAACX,QAAO;YAC7C;UACF;QACF,OAAO;UACLO,KAAI,CAACP,QAAO,GAAI,IAAG;QACrB;QACAO,KAAI,CAACL,IAAG,GAAIO,QAAQ,CAAClB,IAAI,CAACC,IAAI,CAACU,IAAG;QAClCK,KAAI,CAACJ,QAAO,GAAII,KAAI,CAACL,IAAI,CAACC,QAAO;MACnC,CAAC;IACH;EACF;AACF", "ignoreList": []}]}