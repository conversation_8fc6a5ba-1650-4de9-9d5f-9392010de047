{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\404.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\404.vue", "mtime": 1753924830308}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdQYWdlNDA0JywKICBjb21wdXRlZDogewogICAgbWVzc2FnZTogZnVuY3Rpb24gbWVzc2FnZSgpIHsKICAgICAgcmV0dXJuICdUaGUgd2VibWFzdGVyIHNhaWQgdGhhdCB5b3UgY2FuIG5vdCBlbnRlciB0aGlzIHBhZ2UuLi4nOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "computed", "message"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\404.vue"], "sourcesContent": ["<template>\r\n  <div class=\"wscn-http404-container\">\r\n    <div class=\"wscn-http404\">\r\n      <div class=\"pic-404\">\r\n        <img class=\"pic-404__parent\" src=\"@/assets/404_images/404.png\" alt=\"404\">\r\n        <img class=\"pic-404__child left\" src=\"@/assets/404_images/404_cloud.png\" alt=\"404\">\r\n        <img class=\"pic-404__child mid\" src=\"@/assets/404_images/404_cloud.png\" alt=\"404\">\r\n        <img class=\"pic-404__child right\" src=\"@/assets/404_images/404_cloud.png\" alt=\"404\">\r\n      </div>\r\n      <div class=\"bullshit\">\r\n        <div class=\"bullshit__oops\">OOPS!</div>\r\n        <div class=\"bullshit__info\">All rights reserved\r\n          <a style=\"color:#20a0ff\" href=\"https://wallstreetcn.com\" target=\"_blank\">wallstreetcn</a>\r\n        </div>\r\n        <div class=\"bullshit__headline\">{{ message }}</div>\r\n        <div class=\"bullshit__info\">Please check that the URL you entered is correct, or click the button below to return to the homepage.</div>\r\n        <a href=\"\" class=\"bullshit__return-home\">Back to home</a>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n  name: 'Page404',\r\n  computed: {\r\n    message() {\r\n      return 'The webmaster said that you can not enter this page...'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.wscn-http404-container{\r\n  transform: translate(-50%,-50%);\r\n  position: absolute;\r\n  top: 40%;\r\n  left: 50%;\r\n}\r\n.wscn-http404 {\r\n  position: relative;\r\n  width: 1200px;\r\n  padding: 0 50px;\r\n  overflow: hidden;\r\n  .pic-404 {\r\n    position: relative;\r\n    float: left;\r\n    width: 600px;\r\n    overflow: hidden;\r\n    &__parent {\r\n      width: 100%;\r\n    }\r\n    &__child {\r\n      position: absolute;\r\n      &.left {\r\n        width: 80px;\r\n        top: 17px;\r\n        left: 220px;\r\n        opacity: 0;\r\n        animation-name: cloudLeft;\r\n        animation-duration: 2s;\r\n        animation-timing-function: linear;\r\n        animation-fill-mode: forwards;\r\n        animation-delay: 1s;\r\n      }\r\n      &.mid {\r\n        width: 46px;\r\n        top: 10px;\r\n        left: 420px;\r\n        opacity: 0;\r\n        animation-name: cloudMid;\r\n        animation-duration: 2s;\r\n        animation-timing-function: linear;\r\n        animation-fill-mode: forwards;\r\n        animation-delay: 1.2s;\r\n      }\r\n      &.right {\r\n        width: 62px;\r\n        top: 100px;\r\n        left: 500px;\r\n        opacity: 0;\r\n        animation-name: cloudRight;\r\n        animation-duration: 2s;\r\n        animation-timing-function: linear;\r\n        animation-fill-mode: forwards;\r\n        animation-delay: 1s;\r\n      }\r\n      @keyframes cloudLeft {\r\n        0% {\r\n          top: 17px;\r\n          left: 220px;\r\n          opacity: 0;\r\n        }\r\n        20% {\r\n          top: 33px;\r\n          left: 188px;\r\n          opacity: 1;\r\n        }\r\n        80% {\r\n          top: 81px;\r\n          left: 92px;\r\n          opacity: 1;\r\n        }\r\n        100% {\r\n          top: 97px;\r\n          left: 60px;\r\n          opacity: 0;\r\n        }\r\n      }\r\n      @keyframes cloudMid {\r\n        0% {\r\n          top: 10px;\r\n          left: 420px;\r\n          opacity: 0;\r\n        }\r\n        20% {\r\n          top: 40px;\r\n          left: 360px;\r\n          opacity: 1;\r\n        }\r\n        70% {\r\n          top: 130px;\r\n          left: 180px;\r\n          opacity: 1;\r\n        }\r\n        100% {\r\n          top: 160px;\r\n          left: 120px;\r\n          opacity: 0;\r\n        }\r\n      }\r\n      @keyframes cloudRight {\r\n        0% {\r\n          top: 100px;\r\n          left: 500px;\r\n          opacity: 0;\r\n        }\r\n        20% {\r\n          top: 120px;\r\n          left: 460px;\r\n          opacity: 1;\r\n        }\r\n        80% {\r\n          top: 180px;\r\n          left: 340px;\r\n          opacity: 1;\r\n        }\r\n        100% {\r\n          top: 200px;\r\n          left: 300px;\r\n          opacity: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .bullshit {\r\n    position: relative;\r\n    float: left;\r\n    width: 300px;\r\n    padding: 30px 0;\r\n    overflow: hidden;\r\n    &__oops {\r\n      font-size: 32px;\r\n      font-weight: bold;\r\n      line-height: 40px;\r\n      color: #1482f0;\r\n      opacity: 0;\r\n      margin-bottom: 20px;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n    &__headline {\r\n      font-size: 20px;\r\n      line-height: 24px;\r\n      color: #222;\r\n      font-weight: bold;\r\n      opacity: 0;\r\n      margin-bottom: 10px;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-delay: 0.1s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n    &__info {\r\n      font-size: 13px;\r\n      line-height: 21px;\r\n      color: grey;\r\n      opacity: 0;\r\n      margin-bottom: 30px;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-delay: 0.2s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n    &__return-home {\r\n      display: block;\r\n      float: left;\r\n      width: 110px;\r\n      height: 36px;\r\n      background: #1482f0;\r\n      border-radius: 100px;\r\n      text-align: center;\r\n      color: #ffffff;\r\n      opacity: 0;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n      animation-name: slideUp;\r\n      animation-duration: 0.5s;\r\n      animation-delay: 0.3s;\r\n      animation-fill-mode: forwards;\r\n    }\r\n    @keyframes slideUp {\r\n      0% {\r\n        transform: translateY(60px);\r\n        opacity: 0;\r\n      }\r\n      100% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAwBA,eAAe;EACbA,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,OAAO,wDAAuD;IAChE;EACF;AACF", "ignoreList": []}]}