{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue?vue&type=template&id=4dfc1336&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1753924830293}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImRhc2hib2FyZC1lZGl0b3ItY29udGFpbmVyIj4NCiAgICA8ZGl2IGNsYXNzPSIgY2xlYXJmaXgiPg0KICAgICAgPHBhbi10aHVtYiA6aW1hZ2U9ImF2YXRhciIgc3R5bGU9ImZsb2F0OiBsZWZ0Ij4NCiAgICAgICAgWW91ciByb2xlczoNCiAgICAgICAgPHNwYW4gdi1mb3I9Iml0ZW0gaW4gcm9sZXMiIDprZXk9Iml0ZW0iIGNsYXNzPSJwYW4taW5mby1yb2xlcyI+e3sgaXRlbSB9fTwvc3Bhbj4NCiAgICAgIDwvcGFuLXRodW1iPg0KICAgICAgPGdpdGh1Yi1jb3JuZXIgc3R5bGU9InBvc2l0aW9uOiBhYnNvbHV0ZTsgdG9wOiAwcHg7IGJvcmRlcjogMDsgcmlnaHQ6IDA7IiAvPg0KICAgICAgPGRpdiBjbGFzcz0iaW5mby1jb250YWluZXIiPg0KICAgICAgICA8c3BhbiBjbGFzcz0iZGlzcGxheV9uYW1lIj57eyBuYW1lIH19PC9zcGFuPg0KICAgICAgICA8c3BhbiBzdHlsZT0iZm9udC1zaXplOjIwcHg7cGFkZGluZy10b3A6MjBweDtkaXNwbGF5OmlubGluZS1ibG9jazsiPkVkaXRvcidzIERhc2hib2FyZDwvc3Bhbj4NCiAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KICAgIDxkaXY+DQogICAgICA8aW1nIDpzcmM9ImVtcHR5R2lmIiBjbGFzcz0iZW1wdHlHaWYiPg0KICAgIDwvZGl2Pg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;QAC5C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MAC3E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9F,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/editor/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <div class=\" clearfix\">\r\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\r\n        Your roles:\r\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\r\n      </pan-thumb>\r\n      <github-corner style=\"position: absolute; top: 0px; border: 0; right: 0;\" />\r\n      <div class=\"info-container\">\r\n        <span class=\"display_name\">{{ name }}</span>\r\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <img :src=\"emptyGif\" class=\"emptyGif\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport PanThumb from '@/components/PanThumb'\r\nimport GithubCorner from '@/components/GithubCorner'\r\n\r\nexport default {\r\n  name: 'DashboardEditor',\r\n  components: { PanThumb, GithubCorner },\r\n  data() {\r\n    return {\r\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'name',\r\n      'avatar',\r\n      'roles'\r\n    ])\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .emptyGif {\r\n    display: block;\r\n    width: 45%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .dashboard-editor-container {\r\n    background-color: #e3e3e3;\r\n    min-height: 100vh;\r\n    padding: 50px 60px 0px;\r\n    .pan-info-roles {\r\n      font-size: 12px;\r\n      font-weight: 700;\r\n      color: #333;\r\n      display: block;\r\n    }\r\n    .info-container {\r\n      position: relative;\r\n      margin-left: 190px;\r\n      height: 150px;\r\n      line-height: 200px;\r\n      .display_name {\r\n        font-size: 48px;\r\n        line-height: 48px;\r\n        color: #212121;\r\n        position: absolute;\r\n        top: 25px;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}