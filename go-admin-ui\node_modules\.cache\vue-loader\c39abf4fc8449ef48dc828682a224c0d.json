{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\sys-tools\\monitor.vue?vue&type=template&id=5f5cf47c&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\sys-tools\\monitor.vue", "mtime": 1753924830482}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\sys-tools\\monitor.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd;sBACE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC3E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACvD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtB,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtB,CAAC,CAAC;oBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtB,CAAC;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC3G,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAE9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAClF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAER,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/sys-tools/monitor.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div>\r\n    <BasicLayout>\r\n      <template #wrapper>\r\n        <el-row :gutter=\"10\" class=\"mb10\">\r\n          <el-col :sm=\"24\" :md=\"8\">\r\n            <el-card v-if=\"info.cpu\" class=\"box-card\" shadow=\"always\" :body-style=\"{paddingTop:'0 !important'}\">\r\n              <div slot=\"header\" class=\"clearfix\">\r\n                <el-row :gutter=\"10\">\r\n                  <el-col :sm=\"24\" :md=\"8\">\r\n                    <el-tag\r\n                      type=\"success\"\r\n                      effect=\"dark\"\r\n                    >\r\n                      Runing\r\n                    </el-tag>\r\n                  </el-col>\r\n                  <el-col :sm=\"24\" :md=\"8\" class=\"\" style=\"line-height:28px;text-align:center;\">\r\n                    {{ info.location }}\r\n                  </el-col>\r\n                </el-row>\r\n              </div>\r\n              <div class=\"monitor\" style=\"padding-top:0px;\">\r\n                <div class=\"monitor-content\">\r\n                  <el-row :gutter=\"10\">\r\n                    <el-col :sm=\"24\" :md=\"12\">\r\n                      <Cell label=\"系统\" :value=\"info.os.goOs\" border />\r\n                      <Cell label=\"内存\" :value=\"`${info.mem.used}MB/${info.mem.total}MB`\" border />\r\n                      <Cell label=\"交换\" :value=\"`${info.swap.used}/${info.swap.total}`\" border />\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"12\">\r\n                      <Cell label=\"时间\" :value=\"info.os.time\" border />\r\n                      <Cell label=\"在线\" :value=\"`${info.bootTime}小时`\" border />\r\n                      <Cell label=\"硬盘\" :value=\"`${info.disk.used}GB/${info.disk.total}GB`\" border />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\">\r\n                    <el-col :sm=\"12\" :md=\"12\" class=\"line\">\r\n                      <el-row>\r\n                        <el-col span=\"12\" :sm=\"8\" :md=\"8\" xs=\"12\">\r\n                          下载<i class=\"el-icon-caret-bottom\" />\r\n                        </el-col>\r\n                        <el-col span=\"12\" :sm=\"16\" :md=\"16\" xs=\"12\" class=\"line-value\">\r\n                          {{ info.net.in }}KB\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                    <el-col :sm=\"12\" :md=\"12\" class=\"line\">\r\n                      <el-row border>\r\n                        <el-col span=\"12\" :sm=\"6\" :md=\"8\">\r\n                          上传<i class=\"el-icon-caret-top\" />\r\n                        </el-col>\r\n                        <el-col span=\"12\" :sm=\"6\" :md=\"16\" class=\"line-value\">\r\n                          {{ info.net.out }}KB\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\" class=\"monitor-progress\">\r\n                    <el-col :sm=\"24\" :md=\"4\">\r\n                      CPU\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"20\">\r\n                      <el-progress :color=\"customColors\" :text-inside=\"true\" :stroke-width=\"24\" :percentage=\"info.cpu.percent\" />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\" class=\"monitor-progress\">\r\n                    <el-col :sm=\"24\" :md=\"4\">\r\n                      RAM\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"20\">\r\n                      <el-progress :color=\"customColors\" :text-inside=\"true\" :stroke-width=\"24\" :percentage=\"info.mem.percent\" />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row :gutter=\"10\" class=\"monitor-progress\">\r\n                    <el-col :sm=\"24\" :md=\"4\">\r\n                      硬盘\r\n                    </el-col>\r\n                    <el-col :sm=\"24\" :md=\"20\">\r\n                      <el-progress :color=\"customColors\" :text-inside=\"true\" :stroke-width=\"24\" :percentage=\"info.disk.percent\" />\r\n                    </el-col>\r\n                  </el-row>\r\n                  <!-- <el-progress :color=\"$store.state.settings.theme\" type=\"circle\" :percentage=\"info.cpu.Percent\" /> -->\r\n                </div>\r\n                <!-- <div class=\"monitor-footer\">\r\n\r\n                  <Cell label=\"CPU主频\" :value=\"info.cpu.cpuInfo[0].modelName.split('@ ')[1]\" border />\r\n                  <Cell label=\"核心数\" :value=\"`${info.cpu.cpuInfo[0].cores}`\" />\r\n                </div> -->\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n\r\n          <!-- <el-card v-if=\"info.os\" class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>go运行环境</span>\r\n            </div>\r\n            <div class=\"monitor\">\r\n              <Cell label=\"GO 版本\" :value=\"info.os.version\" border />\r\n              <Cell label=\"Goroutine\" :value=\"`${info.os.numGoroutine}`\" border />\r\n              <Cell label=\"项目地址\" :value=\"info.os.projectDir\" />\r\n            </div>\r\n          </el-card> -->\r\n\r\n          <el-card v-if=\"info.os\" class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>服务器信息</span>\r\n            </div>\r\n            <div class=\"monitor\">\r\n              <Cell label=\"主机名称\" :value=\"info.os.hostName\" border />\r\n              <Cell label=\"操作系统\" :value=\"info.os.goOs\" border />\r\n              <Cell label=\"服务器IP\" :value=\"info.os.ip\" border />\r\n              <Cell label=\"系统架构\" :value=\"info.os.arch\" border />\r\n              <Cell label=\"CPU\" :value=\"info.cpu.cpuInfo[0].modelName\" border />\r\n              <Cell label=\"当前时间\" :value=\"info.os.time\" />\r\n            </div>\r\n          </el-card>\r\n\r\n        </el-row></template>\r\n    </BasicLayout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Cell from '@/components/Cell/index'\r\nimport {\r\n  getServer\r\n} from '@/api/monitor/server'\r\nexport default {\r\n  name: 'Monitor',\r\n  components: {\r\n    Cell\r\n  },\r\n  data() {\r\n    return {\r\n      info: {},\r\n      customColors: [\r\n        { color: '#13ce66', percentage: 20 },\r\n        { color: '#1890ff', percentage: 40 },\r\n        { color: '#e6a23c', percentage: 60 },\r\n        { color: '#1989fa', percentage: 80 },\r\n        { color: '#F56C6C', percentage: 100 }\r\n      ],\r\n      timer: null\r\n    }\r\n  },\r\n  created() {\r\n    this.getServerInfo()\r\n    this.timer = setInterval(() => {\r\n      this.getServerInfo()\r\n    }, 1000)\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer)\r\n    this.timer = null\r\n  },\r\n  methods: {\r\n    getServerInfo() {\r\n      getServer().then(ret => {\r\n        if (ret.code === 200) {\r\n          this.info = ret\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.line{\r\n  line-height: 49px;\r\n  font-size: 14px ;\r\n  padding-left: 5px !important;\r\n  padding-right: 5px !important;\r\n  border-bottom: 1px solid #e6ebf5;\r\n  .line-value{\r\n    text-align: right;\r\n    color: #969799;\r\n  }\r\n}\r\n\r\n.monitor {\r\n  .monitor-header {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n  .monitor-progress{\r\n    padding-top: 15px;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}