{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\Todo.vue?vue&type=template&id=6ca6b06d", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\Todo.vue", "mtime": 1753924830288}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxsaSA6Y2xhc3M9InsgY29tcGxldGVkOiB0b2RvLmRvbmUsIGVkaXRpbmc6IGVkaXRpbmcgfSIgY2xhc3M9InRvZG8iPg0KICAgIDxkaXYgY2xhc3M9InZpZXciPg0KICAgICAgPGlucHV0DQogICAgICAgIDpjaGVja2VkPSJ0b2RvLmRvbmUiDQogICAgICAgIGNsYXNzPSJ0b2dnbGUiDQogICAgICAgIHR5cGU9ImNoZWNrYm94Ig0KICAgICAgICBAY2hhbmdlPSJ0b2dnbGVUb2RvKCB0b2RvKSINCiAgICAgID4NCiAgICAgIDxsYWJlbCBAZGJsY2xpY2s9ImVkaXRpbmcgPSB0cnVlIiB2LXRleHQ9InRvZG8udGV4dCIgLz4NCiAgICAgIDxidXR0b24gY2xhc3M9ImRlc3Ryb3kiIEBjbGljaz0iZGVsZXRlVG9kbyggdG9kbyApIiAvPg0KICAgIDwvZGl2Pg0KICAgIDxpbnB1dA0KICAgICAgdi1zaG93PSJlZGl0aW5nIg0KICAgICAgdi1mb2N1cz0iZWRpdGluZyINCiAgICAgIDp2YWx1ZT0idG9kby50ZXh0Ig0KICAgICAgY2xhc3M9ImVkaXQiDQogICAgICBAa2V5dXAuZW50ZXI9ImRvbmVFZGl0Ig0KICAgICAgQGtleXVwLmVzYz0iY2FuY2VsRWRpdCINCiAgICAgIEBibHVyPSJkb25lRWRpdCINCiAgICA+DQogIDwvbGk+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\Todo.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACvD,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/components/TodoList/Todo.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <li :class=\"{ completed: todo.done, editing: editing }\" class=\"todo\">\r\n    <div class=\"view\">\r\n      <input\r\n        :checked=\"todo.done\"\r\n        class=\"toggle\"\r\n        type=\"checkbox\"\r\n        @change=\"toggleTodo( todo)\"\r\n      >\r\n      <label @dblclick=\"editing = true\" v-text=\"todo.text\" />\r\n      <button class=\"destroy\" @click=\"deleteTodo( todo )\" />\r\n    </div>\r\n    <input\r\n      v-show=\"editing\"\r\n      v-focus=\"editing\"\r\n      :value=\"todo.text\"\r\n      class=\"edit\"\r\n      @keyup.enter=\"doneEdit\"\r\n      @keyup.esc=\"cancelEdit\"\r\n      @blur=\"doneEdit\"\r\n    >\r\n  </li>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Todo',\r\n  directives: {\r\n    focus(el, { value }, { context }) {\r\n      if (value) {\r\n        context.$nextTick(() => {\r\n          el.focus()\r\n        })\r\n      }\r\n    }\r\n  },\r\n  props: {\r\n    todo: {\r\n      type: Object,\r\n      default: function() {\r\n        return {}\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      editing: false\r\n    }\r\n  },\r\n  methods: {\r\n    deleteTodo(todo) {\r\n      this.$emit('deleteTodo', todo)\r\n    },\r\n    editTodo({ todo, value }) {\r\n      this.$emit('editTodo', { todo, value })\r\n    },\r\n    toggleTodo(todo) {\r\n      this.$emit('toggleTodo', todo)\r\n    },\r\n    doneEdit(e) {\r\n      const value = e.target.value.trim()\r\n      const { todo } = this\r\n      if (!value) {\r\n        this.deleteTodo({\r\n          todo\r\n        })\r\n      } else if (this.editing) {\r\n        this.editTodo({\r\n          todo,\r\n          value\r\n        })\r\n        this.editing = false\r\n      }\r\n    },\r\n    cancelEdit(e) {\r\n      e.target.value = this.todo.text\r\n      this.editing = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}