{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1753924830216}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue"], "names": [], "mappings": ";AAoCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;IACF;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACpE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MACtC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC;QACH;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACxB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B;QACF;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD;MACF;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE;YACA,CAAC,CAAC,CAAC,CAAC;UACN;QACF;MACF,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9D,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC;QACH,CAAC;MACH,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;UACxE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpC;QACF,CAAC;MACH;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACzD,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7B,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5D,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB;MACF;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;AACF;;AAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACrC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;MACnE;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3D;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,EAAE,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACd;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/TagsView/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n    <el-tabs\r\n      v-model=\"editableTabsValue\"\r\n      type=\"card\"\r\n      @tab-remove=\"closeSelectedTag\"\r\n    >\r\n      <el-tab-pane\r\n        v-for=\"item in visitedViews\"\r\n        :key=\"item.path\"\r\n        :closable=\"item.fullPath === '/dashboard' ? false : true\"\r\n        :name=\"item.fullPath\"\r\n      >\r\n        <router-link\r\n          ref=\"tag\"\r\n          slot=\"label\"\r\n          tag=\"span\"\r\n          class=\"tags-view-item\"\r\n          :style=\"{ color: item.fullPath === $route.fullPath ? theme : '' }\"\r\n          :to=\"{ path: item.path, query: item.query, fullPath: item.fullPath }\"\r\n          @contextmenu.prevent.native=\"openMenu(item,$event)\"\r\n        >\r\n          {{ item.title }}\r\n        </router-link>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n      <li class=\"tags-item\" @click=\"refreshSelectedTag(selectedTag)\" @mouseover=\"handleTagsOver(1)\" @mouseleave=\"handleTagsLeave(1)\">刷新当前标签页</li>\r\n      <li v-if=\"!isAffix(selectedTag)\" class=\"tags-item\" @click=\"closeSelectedTag(selectedTag)\" @mouseover=\"handleTagsOver(2)\" @mouseleave=\"handleTagsLeave(2)\">关闭当前标签页</li>\r\n      <li class=\"tags-item\" @click=\"closeOthersTags\" @mouseover=\"handleTagsOver(3)\" @mouseleave=\"handleTagsLeave(3)\">关闭其他标签页</li>\r\n      <li class=\"tags-item\" @click=\"closeAllTags(selectedTag)\" @mouseover=\"handleTagsOver(4)\" @mouseleave=\"handleTagsLeave(4)\">关闭全部标签页</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      editableTabsValue: '/',\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: [],\r\n      visible: false\r\n    }\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags()\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.closeMenu)\r\n      } else {\r\n        document.body.removeEventListener('click', this.closeMenu)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTags()\r\n    this.addTags()\r\n    this.isActive()\r\n    this.beforeUnload()\r\n  },\r\n  methods: {\r\n    // 刷新前缓存tab\r\n    beforeUnload() {\r\n      // 监听页面刷新\r\n      window.addEventListener('beforeunload', () => {\r\n        const tabViews = this.visitedViews.map(item => {\r\n          return {\r\n            fullPath: item.fullPath,\r\n            hash: item.hash,\r\n            meta: { ...item.meta },\r\n            name: item.name,\r\n            params: { ...item.params },\r\n            path: item.path,\r\n            query: { ...item.query },\r\n            title: item.title\r\n          }\r\n        })\r\n        sessionStorage.setItem('tabViews', JSON.stringify(tabViews))\r\n      })\r\n      // 页面初始化加载判断缓存中是否有数据\r\n      const oldViews = JSON.parse(sessionStorage.getItem('tabViews')) || []\r\n      if (oldViews.length > 0) {\r\n        this.$store.state.tagsView.visitedViews = oldViews\r\n      }\r\n    },\r\n    handleTagsOver(index) {\r\n      const tags = document.querySelectorAll('.tags-item')\r\n      const item = tags[index - 1]\r\n      item.style.cssText = `color:${this.$store.state.settings.theme};background:${\r\n        this.$store.state.settings.theme.colorRgb()\r\n      }`\r\n    },\r\n    handleTagsLeave(index) {\r\n      const tags = document.querySelectorAll('.tags-item')\r\n      const item = tags[index - 1]\r\n      item.style.cssText = `color:#606266`\r\n    },\r\n    isActive() {\r\n      const index = this.visitedViews.findIndex(item => item.fullPath === this.$route.fullPath)\r\n      const pathIndex = index > -1 ? index : 0\r\n      this.editableTabsValue = this.visitedViews[pathIndex].fullPath\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix\r\n    },\r\n    filterAffixTags(routes, basePath = '/') {\r\n      let tags = []\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path)\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: { ...route.meta }\r\n          })\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path)\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags]\r\n          }\r\n        }\r\n      })\r\n      return tags\r\n    },\r\n    initTags() {\r\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch('tagsView/addVisitedView', tag)\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const { name } = this.$route\r\n      if (name) {\r\n        this.$store.dispatch('tagsView/addView', this.$route)\r\n        this.isActive()\r\n      }\r\n      return false\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path === this.$route.path) {\r\n            // this.$refs.scrollPane.moveToTarget(tag)\r\n            // when query is different then update\r\n            if (tag.to.fullPath !== this.$route.fullPath) {\r\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n            }\r\n            break\r\n          }\r\n        }\r\n      })\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n        const { fullPath } = view\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: '/redirect' + fullPath\r\n          })\r\n        })\r\n      })\r\n    },\r\n    closeSelectedTag(view) {\r\n      const routerPath = view.fullPath ? view.fullPath : view\r\n      const index = this.visitedViews.findIndex(item => item.fullPath === routerPath)\r\n      if (index > -1) {\r\n        const path = this.visitedViews[index]\r\n        this.$store.dispatch('tagsView/delView', path).then(({ visitedViews }) => {\r\n          if (this.editableTabsValue === path.fullPath) {\r\n            this.toLastView(visitedViews, path)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag.path).catch(e => e)\r\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\r\n        this.moveToCurrentTag()\r\n      })\r\n    },\r\n    closeAllTags(view) {\r\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\r\n        if (this.affixTags.some(tag => tag.path === view.path)) {\r\n          return\r\n        }\r\n        this.toLastView(visitedViews, view)\r\n      })\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0]\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath).catch(err => err)\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name === 'Dashboard') {\r\n          // to reload home page\r\n          this.$router.replace({ path: '/redirect' + view.fullPath })\r\n        } else {\r\n          this.$router.push('/')\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105\r\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n      const offsetWidth = this.$el.offsetWidth // container width\r\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\r\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft\r\n      } else {\r\n        this.left = left\r\n      }\r\n\r\n      this.top = e.clientY\r\n      this.visible = true\r\n      this.selectedTag = tag\r\n    },\r\n    closeMenu() {\r\n      this.visible = false\r\n    }\r\n  }\r\n}\r\n\r\n// eslint-disable-next-line no-extend-native\r\nString.prototype.colorRgb = function() {\r\n  let sColor = this.toLowerCase()\r\n  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n  if (sColor && reg.test(sColor)) {\r\n    if (sColor.length === 4) {\r\n      let sColorNew = '#'\r\n      for (let i = 1; i < 4; i += 1) {\r\n        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n      }\r\n      sColor = sColorNew\r\n    }\r\n    const sColorChange = []\r\n    for (let i = 1; i < 7; i += 2) {\r\n      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))\r\n    }\r\n    return 'rgba(' + sColorChange.join(',') + ',0.2)'\r\n  } else {\r\n    return sColor\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tags-view-container ::v-deep{\r\n  height: 43px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-bottom: 1px solid #d8dce5;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n  padding: 0 15px;\r\n  box-sizing: border-box;\r\n  .el-tabs__item{\r\n    &:hover{\r\n      color: #000;\r\n    }\r\n  }\r\n  .tags-view-item{\r\n    height: 40px;\r\n    display: inline-block;\r\n  }\r\n  .tags-view-wrapper {\r\n    .tags-view-item {\r\n      display: inline-block;\r\n      position: relative;\r\n      cursor: pointer;\r\n      height: 26px;\r\n      line-height: 26px;\r\n      border: 1px solid #d8dce5;\r\n      color: #495060;\r\n      background: #fff;\r\n      padding: 0 8px;\r\n      font-size: 12px;\r\n      margin-left: 5px;\r\n      margin-top: 4px;\r\n      &:first-of-type {\r\n        margin-left: 15px;\r\n      }\r\n      &:last-of-type {\r\n        margin-right: 15px;\r\n      }\r\n      &.active {\r\n        background-color: #42b983;\r\n        color: #fff;\r\n        border-color: #42b983;\r\n        &::before {\r\n          content: '';\r\n          background: #fff;\r\n          display: inline-block;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          position: relative;\r\n          margin-right: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .contextmenu {\r\n    margin: 0;\r\n    background: #fff;\r\n    z-index: 3000;\r\n    position: absolute;\r\n    list-style-type: none;\r\n    padding: 5px 0;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333;\r\n    box-shadow: 1px 2px 10px #ccc;\r\n    -moz-user-select:none;\r\n    -webkit-user-select:none;\r\n    user-select:none;\r\n    li {\r\n      list-style: none;\r\n      line-height: 36px;\r\n      padding: 2px 20px;\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      cursor: pointer;\r\n      outline: 0;\r\n      cursor: pointer;\r\n      &:hover {\r\n        background: #eee;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n//reset element css of el-icon-close\r\n.tags-view-wrapper {\r\n  .tags-view-item {\r\n    .el-icon-close {\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: 2px;\r\n      border-radius: 50%;\r\n      text-align: center;\r\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\r\n      transform-origin: 100% 50%;\r\n      &:before {\r\n        transform: scale(.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n      }\r\n      &:hover {\r\n        background-color: #b4bccc;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}