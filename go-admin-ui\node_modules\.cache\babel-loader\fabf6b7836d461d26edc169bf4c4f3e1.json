{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\index.vue", "mtime": 1753924830294}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovRGVza3RvcC9Hb0FkbWluL2dvLWFkbWluLXVpL25vZGVfbW9kdWxlcy8uc3RvcmUvQGJhYmVsK3J1bnRpbWVANy4yOC4yL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwppbXBvcnQgYWRtaW5EYXNoYm9hcmQgZnJvbSAnLi9hZG1pbic7CmltcG9ydCBlZGl0b3JEYXNoYm9hcmQgZnJvbSAnLi9lZGl0b3InOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0Rhc2hib2FyZCcsCiAgY29tcG9uZW50czogewogICAgYWRtaW5EYXNoYm9hcmQ6IGFkbWluRGFzaGJvYXJkLAogICAgZWRpdG9yRGFzaGJvYXJkOiBlZGl0b3JEYXNoYm9hcmQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjdXJyZW50Um9sZTogJ2FkbWluRGFzaGJvYXJkJwogICAgfTsKICB9LAogIGNvbXB1dGVkOiBfb2JqZWN0U3ByZWFkKHt9LCBtYXBHZXR0ZXJzKFsncm9sZXMnXSkpLAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICAvLyBpZiAoIXRoaXMucm9sZXMuaW5jbHVkZXMoJ2FkbWluJykpIHsKICAgIC8vICAgdGhpcy5jdXJyZW50Um9sZSA9ICdlZGl0b3JEYXNoYm9hcmQnCiAgICAvLyB9CiAgfQp9Ow=="}, {"version": 3, "names": ["mapGetters", "adminDashboard", "editorDashboard", "name", "components", "data", "currentRole", "computed", "_objectSpread", "created"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <component :is=\"currentRole\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport adminDashboard from './admin'\r\nimport editorDashboard from './editor'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  components: { adminDashboard, editorDashboard },\r\n  data() {\r\n    return {\r\n      currentRole: 'adminDashboard'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'roles'\r\n    ])\r\n  },\r\n  created() {\r\n    // if (!this.roles.includes('admin')) {\r\n    //   this.currentRole = 'editorDashboard'\r\n    // }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";AAOA,SAASA,UAAS,QAAS,MAAK;AAChC,OAAOC,cAAa,MAAO,SAAQ;AACnC,OAAOC,eAAc,MAAO,UAAS;AAErC,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IAAEH,cAAc,EAAdA,cAAc;IAAEC,eAAc,EAAdA;EAAgB,CAAC;EAC/CG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,QAAQ,EAAAC,aAAA,KACHR,UAAU,CAAC,CACZ,OAAM,CACP,EACF;EACDS,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACA;IACA;EAAA;AAEJ", "ignoreList": []}]}