{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue", "mtime": 1753924830290}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\TodoList\\index.vue"], "names": [], "mappings": ";AAwCA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD;AACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EAClB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACzC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAClC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AAChC;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC;IACH;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/components/TodoList/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <section class=\"todoapp\">\r\n    <!-- header -->\r\n    <header class=\"header\">\r\n      <input class=\"new-todo\" autocomplete=\"off\" placeholder=\"Todo List\" @keyup.enter=\"addTodo\">\r\n    </header>\r\n    <!-- main section -->\r\n    <section v-show=\"todos.length\" class=\"main\">\r\n      <input id=\"toggle-all\" :checked=\"allChecked\" class=\"toggle-all\" type=\"checkbox\" @change=\"toggleAll({ done: !allChecked })\">\r\n      <label for=\"toggle-all\" />\r\n      <ul class=\"todo-list\">\r\n        <todo\r\n          v-for=\"(todo, index) in filteredTodos\"\r\n          :key=\"index\"\r\n          :todo=\"todo\"\r\n          @toggleTodo=\"toggleTodo\"\r\n          @editTodo=\"editTodo\"\r\n          @deleteTodo=\"deleteTodo\"\r\n        />\r\n      </ul>\r\n    </section>\r\n    <!-- footer -->\r\n    <footer v-show=\"todos.length\" class=\"footer\">\r\n      <span class=\"todo-count\">\r\n        <strong>{{ remaining }}</strong>\r\n        {{ remaining | pluralize('item') }} left\r\n      </span>\r\n      <ul class=\"filters\">\r\n        <li v-for=\"(val, key) in filters\" :key=\"key\">\r\n          <a :class=\"{ selected: visibility === key }\" @click.prevent=\"visibility = key\">{{ key | capitalize }}</a>\r\n        </li>\r\n      </ul>\r\n      <!-- <button class=\"clear-completed\" v-show=\"todos.length > remaining\" @click=\"clearCompleted\">\r\n        Clear completed\r\n      </button> -->\r\n    </footer>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport Todo from './Todo.vue'\r\n\r\nconst STORAGE_KEY = 'todos'\r\nconst filters = {\r\n  all: todos => todos,\r\n  active: todos => todos.filter(todo => !todo.done),\r\n  completed: todos => todos.filter(todo => todo.done)\r\n}\r\nconst defalutList = [\r\n  { text: 'star this repository', done: false },\r\n  { text: 'fork this repository', done: false },\r\n  { text: 'follow author', done: false },\r\n  { text: 'vue-element-admin', done: true },\r\n  { text: 'vue', done: true },\r\n  { text: 'element-ui', done: true },\r\n  { text: 'axios', done: true },\r\n  { text: 'webpack', done: true }\r\n]\r\nexport default {\r\n  components: { Todo },\r\n  filters: {\r\n    pluralize: (n, w) => n === 1 ? w : w + 's',\r\n    capitalize: s => s.charAt(0).toUpperCase() + s.slice(1)\r\n  },\r\n  data() {\r\n    return {\r\n      visibility: 'all',\r\n      filters,\r\n      // todos: JSON.parse(window.localStorage.getItem(STORAGE_KEY)) || defalutList\r\n      todos: defalutList\r\n    }\r\n  },\r\n  computed: {\r\n    allChecked() {\r\n      return this.todos.every(todo => todo.done)\r\n    },\r\n    filteredTodos() {\r\n      return filters[this.visibility](this.todos)\r\n    },\r\n    remaining() {\r\n      return this.todos.filter(todo => !todo.done).length\r\n    }\r\n  },\r\n  methods: {\r\n    setLocalStorage() {\r\n      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(this.todos))\r\n    },\r\n    addTodo(e) {\r\n      const text = e.target.value\r\n      if (text.trim()) {\r\n        this.todos.push({\r\n          text,\r\n          done: false\r\n        })\r\n        this.setLocalStorage()\r\n      }\r\n      e.target.value = ''\r\n    },\r\n    toggleTodo(val) {\r\n      val.done = !val.done\r\n      this.setLocalStorage()\r\n    },\r\n    deleteTodo(todo) {\r\n      this.todos.splice(this.todos.indexOf(todo), 1)\r\n      this.setLocalStorage()\r\n    },\r\n    editTodo({ todo, value }) {\r\n      todo.text = value\r\n      this.setLocalStorage()\r\n    },\r\n    clearCompleted() {\r\n      this.todos = this.todos.filter(todo => !todo.done)\r\n      this.setLocalStorage()\r\n    },\r\n    toggleAll({ done }) {\r\n      this.todos.forEach(todo => {\r\n        todo.done = done\r\n        this.setLocalStorage()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  @import './index.scss';\r\n</style>\r\n"]}]}