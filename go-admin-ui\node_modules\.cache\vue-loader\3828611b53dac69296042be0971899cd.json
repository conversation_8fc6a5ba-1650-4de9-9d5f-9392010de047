{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue?vue&type=style&index=0&id=38230808&lang=css", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue", "mtime": 1753924830277}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucGFuZWwgLmVsLXRyYW5zZmVyX19idXR0b25zew0KICB3aWR0aDogMTUwcHg7DQp9DQoucGFuZWwgLmVsLXRyYW5zZmVyX19idXR0b25zIC5lbC1idXR0b24gKyAuZWwtYnV0dG9uew0KICBtYXJnaW4tbGVmdDowOw0KfQ0KLnBhbmVsIC5lbC10cmFuc2Zlci1wYW5lbHsNCiAgd2lkdGg6IDMwMHB4Ow0KfQ0KDQouZWwtY29sIHsNCnBhZGRpbmc6IDAgNXB4Ow0KfQ0KLmVsLWRyYXdlcl9faGVhZGVyew0KbWFyZ2luLWJvdHRvbTogMDsNCn0NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue"], "names": [], "mappings": ";AA+lBA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACd;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACd;AACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAChB", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/admin/sys-menu/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form :inline=\"true\">\r\n          <el-form-item label=\"菜单名称\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"queryParams.visible\" placeholder=\"菜单状态\" clearable size=\"small\">\r\n              <el-option\r\n                v-for=\"dict in visibleOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button\r\n              v-permisaction=\"['admin:sysMenu:add']\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n            >新增</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"menuList\"\r\n          border\r\n          row-key=\"menuId\"\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"180px\" />\r\n          <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100px\">\r\n            <template slot-scope=\"scope\">\r\n              <svg-icon :icon-class=\"scope.row.icon\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"60px\" />\r\n          <el-table-column prop=\"permission\" label=\"权限标识\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover v-if=\"scope.row.sysApi.length>0\" trigger=\"hover\" placement=\"top\">\r\n                <el-table\r\n                  :data=\"scope.row.sysApi\"\r\n                  border\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-table-column\r\n                    prop=\"title\"\r\n                    label=\"title\"\r\n                    width=\"260px\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.type=='SYS' && scope.row.title!=''\"><el-tag type=\"success\">{{ '['+scope.row.type +'] '+ scope.row.title }}</el-tag></span>\r\n                      <span v-if=\"scope.row.type!='SYS' && scope.row.title!=''\"><el-tag type=\"\">{{ '['+scope.row.type +'] '+scope.row.title }}</el-tag></span>\r\n                      <span v-if=\"scope.row.title==''\"><el-tag type=\"danger\">暂无</el-tag></span>\r\n\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"path\"\r\n                    label=\"path\"\r\n                    width=\"270px\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <el-tag v-if=\"scope.row.action=='GET'\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='POST'\" type=\"success\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='PUT'\" type=\"warning\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='DELETE'\" type=\"danger\">{{ scope.row.action }}</el-tag>\r\n                      {{ scope.row.path }}\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                </el-table>\r\n                <div slot=\"reference\" class=\"name-wrapper\">\r\n                  <span v-if=\"scope.row.permission==''\">-</span>\r\n                  <span v-else>{{ scope.row.permission }}</span>\r\n                </div>\r\n              </el-popover>\r\n              <span v-else>\r\n                <span v-if=\"scope.row.permission==''\">-</span>\r\n                <span v-else>{{ scope.row.permission }}</span>\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"path\" label=\"组件路径\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.menuType=='A'\">{{ scope.row.path }}</span>\r\n              <span v-else>{{ scope.row.component }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"visible\" label=\"可见\" :formatter=\"visibleFormat\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.visible === '1' ? 'danger' : 'success'\"\r\n                disable-transitions\r\n              >{{ visibleFormat(scope.row) }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:add']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-plus\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 添加或修改菜单对话框 -->\r\n        <el-drawer\r\n          ref=\"drawer\"\r\n          :title=\"title\"\r\n          :before-close=\"cancel\"\r\n          :visible.sync=\"open\"\r\n          direction=\"rtl\"\r\n          custom-class=\"demo-drawer\"\r\n          size=\"830px\"\r\n        >\r\n          <div class=\"demo-drawer__content\">\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-position=\"top\" label-width=\"106px\">\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"parentId\">\r\n                    <span slot=\"label\">\r\n                      上级菜单\r\n                      <el-tooltip content=\"指当前菜单停靠的菜单归属\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <treeselect\r\n                      v-model=\"form.parentId\"\r\n                      :options=\"menuOptions\"\r\n                      :normalizer=\"normalizer\"\r\n                      :show-count=\"true\"\r\n                      placeholder=\"选择上级菜单\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item prop=\"title\">\r\n                    <span slot=\"label\">\r\n                      菜单标题\r\n                      <el-tooltip content=\"菜单位置显示的说明信息\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.title\" placeholder=\"请输入菜单标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item prop=\"sort\">\r\n                    <span slot=\"label\">\r\n                      显示排序\r\n                      <el-tooltip content=\"根据序号升序排列\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input-number v-model=\"form.sort\" controls-position=\"right\" :min=\"0\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"menuType\">\r\n                    <span slot=\"label\">\r\n                      菜单类型\r\n                      <el-tooltip content=\"包含目录：以及菜单或者菜单组，菜单：具体对应某一个页面，按钮：功能才做按钮；\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.menuType\">\r\n                      <el-radio label=\"M\">目录</el-radio>\r\n                      <el-radio label=\"C\">菜单</el-radio>\r\n                      <el-radio label=\"F\">按钮</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"菜单图标\">\r\n                    <el-popover\r\n                      placement=\"bottom-start\"\r\n                      width=\"460\"\r\n                      trigger=\"click\"\r\n                      @show=\"$refs['iconSelect'].reset()\"\r\n                    >\r\n                      <IconSelect ref=\"iconSelect\" @selected=\"selected\" />\r\n                      <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                        <svg-icon\r\n                          v-if=\"form.icon\"\r\n                          slot=\"prefix\"\r\n                          :icon-class=\"form.icon\"\r\n                          class=\"el-input__icon\"\r\n                          style=\"height: 32px;width: 16px;\"\r\n                        />\r\n                        <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\r\n                      </el-input>\r\n                    </el-popover>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'M' || form.menuType == 'C'\" prop=\"menuName\">\r\n                    <span slot=\"label\">\r\n                      路由名称\r\n                      <el-tooltip content=\"需要和页面name保持一致，对应页面即可选择缓存\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.menuName\" placeholder=\"请输入路由名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col v-if=\"form.menuType == 'M' || form.menuType == 'C'\" :span=\"12\">\r\n                  <el-form-item prop=\"component\">\r\n                    <span slot=\"label\">\r\n                      组件路径\r\n                      <el-tooltip content=\"菜单对应的具体vue页面文件路径views的下级路径/admin/sys-api/index；目录类型：填写Layout，如何有二级目录请参照日志目录填写；\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'M' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      是否外链\r\n                      <el-tooltip content=\"可以通过iframe打开指定地址\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.isFrame\">\r\n                      <el-radio label=\"0\">是</el-radio>\r\n                      <el-radio label=\"1\">否</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType != 'F'\" prop=\"path\">\r\n                    <span slot=\"label\">\r\n                      路由地址\r\n                      <el-tooltip content=\"访问此页面自定义的url地址，建议/开头书写，例如 /app-name/menu-name\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'F' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      权限标识\r\n                      <el-tooltip content=\"前端权限控制按钮是否显示\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.permission\" placeholder=\"请权限标识\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType != 'F'\">\r\n                    <span slot=\"label\">\r\n                      菜单状态\r\n                      <el-tooltip content=\"需要显示在菜单列表的菜单设置为显示，否则设置为隐藏\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.visible\">\r\n                      <el-radio\r\n                        v-for=\"dict in visibleOptions\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.value\"\r\n                      >{{ dict.label }}</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item v-if=\"form.menuType == 'F' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      api权限\r\n                      <el-tooltip content=\"配置在这个才做上需要使用到的接口，否则在设置用户角色时，接口将无权访问。\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-transfer\r\n                      v-model=\"form.apis\"\r\n                      style=\"text-align: left; display: inline-block\"\r\n                      filterable\r\n                      :props=\"{\r\n                        key: 'id',\r\n                        label: 'title'\r\n                      }\"\r\n                      :titles=\"['未授权', '已授权']\"\r\n                      :button-texts=\"['收回', '授权 ']\"\r\n                      :format=\"{\r\n                        noChecked: '${total}',\r\n                        hasChecked: '${checked}/${total}'\r\n                      }\"\r\n                      class=\"panel\"\r\n                      :data=\"sysapiList\"\r\n                      @change=\"handleChange\"\r\n                    >\r\n                      <span slot-scope=\"{ option }\">{{ option.title }}</span>\r\n                    </el-transfer>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n            <div class=\"demo-drawer__footer\">\r\n              <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n              <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n          </div>\r\n\r\n        </el-drawer>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from '@/api/admin/sys-menu'\r\nimport { listSysApi } from '@/api/admin/sys-api'\r\n\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\r\nimport IconSelect from '@/components/IconSelect'\r\n\r\nexport default {\r\n  name: 'SysMenuManage',\r\n  components: { Treeselect, IconSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      sysapiList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 菜单状态数据字典\r\n      visibleOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        title: undefined,\r\n        visible: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        apis: [],\r\n        sysApi: []\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        title: [{ required: true, message: '菜单标题不能为空', trigger: 'blur' }],\r\n        sort: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n\r\n    this.getApiList()\r\n    this.getDicts('sys_show_hide').then(response => {\r\n      this.visibleOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    handleChange(value, direction, movedKeys) {\r\n      console.log(value, direction, movedKeys)\r\n      const list = this.form.sysApi\r\n      this.form.apis = value\r\n      if (direction === 'right') {\r\n        for (let x = 0; x < movedKeys.length; x++) {\r\n          for (let index = 0; index < this.sysapiList.length; index++) {\r\n            const element = this.sysapiList[index]\r\n            if (element.id === movedKeys[x]) {\r\n              list.push(element)\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.form.sysApi = list\r\n      } else if (direction === 'left') {\r\n        const l = []\r\n        for (let index = 0; index < movedKeys.length; index++) {\r\n          const element = movedKeys[index]\r\n          for (let x = 0; x < list.length; x++) {\r\n            const e = list[x]\r\n            if (element !== e.id) {\r\n              l.push()\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.form.sysApi = l\r\n      }\r\n      // this.setApis(this.form.SysApi)\r\n      console.log(this.form.sysApi)\r\n    },\r\n    getApiList() {\r\n      this.loading = true\r\n      listSysApi({ 'pageSize': 10000, 'type': 'BUS' }).then(response => {\r\n        this.sysapiList = response.data.list\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    handleClose(done) {\r\n      // if (this.loading) {\r\n      //   return\r\n      // }\r\n      // this.$confirm('需要提交表单吗？')\r\n      //   .then(_ => {\r\n      //     this.loading = true\r\n      //     this.timer = setTimeout(() => {\r\n      //       done()\r\n      //       // 动画关闭需要一定的时间\r\n      //       setTimeout(() => {\r\n      //         this.loading = false\r\n      //       }, 400)\r\n      //     }, 1000)\r\n      //   })\r\n      //   .catch(_ => {})\r\n    },\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = response.data\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.title,\r\n        children: node.children\r\n      }\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = []\r\n        const menu = { menuId: 0, title: '主类目', children: [] }\r\n        menu.children = response.data\r\n        this.menuOptions.push(menu)\r\n      })\r\n    },\r\n    // 菜单显示状态字典翻译\r\n    visibleFormat(row) {\r\n      if (row.menuType === 'F') {\r\n        return '-- --'\r\n      }\r\n      return this.selectDictLabel(this.visibleOptions, row.visible)\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: undefined,\r\n        parentId: 0,\r\n        menuName: undefined,\r\n        icon: undefined,\r\n        menuType: 'M',\r\n        apis: [],\r\n        sort: 0,\r\n        action: this.form.menuType === 'A' ? this.form.action : '',\r\n        isFrame: '1',\r\n        visible: '0'\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset()\r\n      this.getTreeselect()\r\n      if (row != null) {\r\n        this.form.parentId = row.menuId\r\n      }\r\n      this.open = true\r\n      this.title = '添加菜单'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.getTreeselect()\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改菜单'\r\n      })\r\n    },\r\n    setApis(apiArray) {\r\n      var l = []\r\n      for (var index = 0; index < apiArray.length; index++) {\r\n        const element = apiArray[index]\r\n        l.push(element.id)\r\n      }\r\n      this.form.apis = l\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId !== undefined) {\r\n            updateMenu(this.form, this.form.menuId).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除名称为\"' + row.title + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        var Ids = (row.menuId && [row.menuId]) || this.ids\r\n        return delMenu({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"css\">\r\n.panel .el-transfer__buttons{\r\n  width: 150px;\r\n}\r\n.panel .el-transfer__buttons .el-button + .el-button{\r\n  margin-left:0;\r\n}\r\n.panel .el-transfer-panel{\r\n  width: 300px;\r\n}\r\n\r\n.el-col {\r\npadding: 0 5px;\r\n}\r\n.el-drawer__header{\r\nmargin-bottom: 0;\r\n}\r\n</style>\r\n"]}]}