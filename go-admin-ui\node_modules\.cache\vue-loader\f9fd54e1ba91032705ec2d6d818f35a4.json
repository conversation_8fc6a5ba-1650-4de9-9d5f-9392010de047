{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\build\\index.vue?vue&type=template&id=bb39f04a", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\build\\index.vue", "mtime": 1753924830295}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxCYXNpY0xheW91dD4NCiAgICA8dGVtcGxhdGUgI3dyYXBwZXI+DQogICAgICA8ZWwtY2FyZCBjbGFzcz0iYm94LWNhcmQiPg0KICAgICAgICA8ZGl2IHYtbG9hZGluZz0ibG9hZGluZyIgOnN0eWxlPSInaGVpZ2h0OicrIGhlaWdodCI+DQogICAgICAgICAgPGlmcmFtZSA6c3JjPSJzcmMiIGZyYW1lYm9yZGVyPSJubyIgc3R5bGU9IndpZHRoOiAxMDAlO2hlaWdodDogMTAwJSIgc2Nyb2xsaW5nPSJhdXRvIiAvPg0KICAgICAgICA8L2Rpdj4NCiAgICAgIDwvZWwtY2FyZD4NCiAgICA8L3RlbXBsYXRlPg0KICA8L0Jhc2ljTGF5b3V0Pg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\build\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/build/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <div v-loading=\"loading\" :style=\"'height:'+ height\">\r\n          <iframe :src=\"src\" frameborder=\"no\" style=\"width: 100%;height: 100%\" scrolling=\"auto\" />\r\n        </div>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n  components: {\r\n\r\n  },\r\n  data() {\r\n    return {\r\n      src: process.env.VUE_APP_BASE_API + '/form-generator/index.html',\r\n      height: document.documentElement.clientHeight - 94.5 + 'px;',\r\n      loading: true\r\n    }\r\n  },\r\n  mounted: function() {\r\n    setTimeout(() => {\r\n      this.loading = false\r\n    }, 230)\r\n    const that = this\r\n    window.onresize = function temp() {\r\n      that.height = document.documentElement.clientHeight - 94.5 + 'px;'\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}