{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue", "mtime": 1753924830065}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdUcmVuZCcsDQogIHByb3BzOiB7DQogICAgcmF0ZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0sDQogICAgZmxhZzogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue"], "names": [], "mappings": ";AAWA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;<PERSON>Z,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/Trend/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"chart-trend\">\r\n    <slot name=\"term\" />\r\n    <span>{{ rate }}%</span>\r\n    <span :class=\"[flag]\">\r\n      <i :class=\"'el-icon-caret-' + flag\" />\r\n    </span>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Trend',\r\n  props: {\r\n    rate: {\r\n      type: String,\r\n      default: '',\r\n      required: true\r\n    },\r\n    flag: {\r\n      type: String,\r\n      default: '',\r\n      required: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chart-trend {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 22px;\r\n  .trend-icon {\r\n    font-size: 12px;\r\n  }\r\n}\r\n.top,\r\n.bottom {\r\n  margin-left: 4px;\r\n  position: relative;\r\n  top: 1px;\r\n  width: 15px;\r\n  display: inline-block;\r\n  i {\r\n    font-size: 12px;\r\n    transform: scale(0.83);\r\n  }\r\n}\r\n\r\n.top {\r\n      i{\r\n          color: #f5222d!important;\r\n      }\r\n    }\r\n    .bottom {\r\n      top: -1px;\r\n      i{\r\n          color: #52c41a!important;\r\n      }\r\n    }\r\n</style>\r\n"]}]}