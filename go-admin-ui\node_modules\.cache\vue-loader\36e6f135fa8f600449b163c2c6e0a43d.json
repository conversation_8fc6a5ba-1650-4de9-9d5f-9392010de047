{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue?vue&type=template&id=c6aa0552&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue", "mtime": 1753924830053}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImNoYXJ0LW1pbmktcHJvZ3Jlc3MiPg0KICAgIDxkaXYgY2xhc3M9InRhcmdldCIgOnN0eWxlPSJ7IGxlZnQ6IHRhcmdldCArICclJ30iPg0KICAgICAgPHNwYW4gOnN0eWxlPSJ7IGJhY2tncm91bmRDb2xvcjogY29sb3IgfSIgLz4NCiAgICAgIDxzcGFuIDpzdHlsZT0ieyBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9yIH0iIC8+DQogICAgPC9kaXY+DQogICAgPGRpdiBjbGFzcz0icHJvZ3Jlc3Mtd3JhcHBlciI+DQogICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzcyIgOnN0eWxlPSJ7IGJhY2tncm91bmRDb2xvcjogY29sb3IsIHdpZHRoOiBwZXJjZW50YWdlICsgJyUnLCBoZWlnaHQ6IGhlaWdodCB9IiAvPg0KICAgIDwvZGl2Pg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\MiniProgress\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACtG,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/MiniProgress/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"chart-mini-progress\">\r\n    <div class=\"target\" :style=\"{ left: target + '%'}\">\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n      <span :style=\"{ backgroundColor: color }\" />\r\n    </div>\r\n    <div class=\"progress-wrapper\">\r\n      <div class=\"progress\" :style=\"{ backgroundColor: color, width: percentage + '%', height: height }\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MiniProgress',\r\n  props: {\r\n    target: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '10px'\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: '#13C2C2'\r\n    },\r\n    percentage: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-mini-progress {\r\n    padding: 5px 0;\r\n    position: relative;\r\n    width: 100%;\r\n    .target {\r\n      position: absolute;\r\n      top: 0;\r\n      bottom: 0;\r\n      span {\r\n        border-radius: 100px;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        height: 4px;\r\n        width: 2px;\r\n        &:last-child {\r\n          top: auto;\r\n          bottom: 0;\r\n        }\r\n      }\r\n    }\r\n    .progress-wrapper {\r\n      background-color: #f5f5f5;\r\n      position: relative;\r\n      .progress {\r\n        transition: all .4s cubic-bezier(.08,.82,.17,1) 0s;\r\n        border-radius: 1px 0 0 1px;\r\n        background-color: #1890ff;\r\n        width: 0;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}