{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue?vue&type=template&id=7925d3e0&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue", "mtime": 1753924830065}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImNoYXJ0LXRyZW5kIj4NCiAgICA8c2xvdCBuYW1lPSJ0ZXJtIiAvPg0KICAgIDxzcGFuPnt7IHJhdGUgfX0lPC9zcGFuPg0KICAgIDxzcGFuIDpjbGFzcz0iW2ZsYWddIj4NCiAgICAgIDxpIDpjbGFzcz0iJ2VsLWljb24tY2FyZXQtJyArIGZsYWciIC8+DQogICAgPC9zcGFuPg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\Trend\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/Trend/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"chart-trend\">\r\n    <slot name=\"term\" />\r\n    <span>{{ rate }}%</span>\r\n    <span :class=\"[flag]\">\r\n      <i :class=\"'el-icon-caret-' + flag\" />\r\n    </span>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Trend',\r\n  props: {\r\n    rate: {\r\n      type: String,\r\n      default: '',\r\n      required: true\r\n    },\r\n    flag: {\r\n      type: String,\r\n      default: '',\r\n      required: true\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.chart-trend {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 22px;\r\n  .trend-icon {\r\n    font-size: 12px;\r\n  }\r\n}\r\n.top,\r\n.bottom {\r\n  margin-left: 4px;\r\n  position: relative;\r\n  top: 1px;\r\n  width: 15px;\r\n  display: inline-block;\r\n  i {\r\n    font-size: 12px;\r\n    transform: scale(0.83);\r\n  }\r\n}\r\n\r\n.top {\r\n      i{\r\n          color: #f5222d!important;\r\n      }\r\n    }\r\n    .bottom {\r\n      top: -1px;\r\n      i{\r\n          color: #52c41a!important;\r\n      }\r\n    }\r\n</style>\r\n"]}]}