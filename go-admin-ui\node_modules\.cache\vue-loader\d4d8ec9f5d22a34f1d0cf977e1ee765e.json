{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue?vue&type=template&id=48c369af&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue", "mtime": 1753924830284}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\PanelGroup.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/components/PanelGroup.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-row :gutter=\"40\" class=\"panel-group\">\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('newVisitis')\">\r\n        <div class=\"card-panel-icon-wrapper icon-people\">\r\n          <svg-icon icon-class=\"peoples\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            新用户\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"102400\" :duration=\"2600\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('messages')\">\r\n        <div class=\"card-panel-icon-wrapper icon-message\">\r\n          <svg-icon icon-class=\"message\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            消息\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"81212\" :duration=\"3000\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('purchases')\">\r\n        <div class=\"card-panel-icon-wrapper icon-money\">\r\n          <svg-icon icon-class=\"money\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            金额\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"9280\" :duration=\"3200\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('shoppings')\">\r\n        <div class=\"card-panel-icon-wrapper icon-shopping\">\r\n          <svg-icon icon-class=\"shopping\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            销量\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"13600\" :duration=\"3600\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n  </el-row>\r\n</template>\r\n\r\n<script>\r\nimport CountTo from 'vue-count-to'\r\n\r\nexport default {\r\n  components: {\r\n    CountTo\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.$emit('handleSetLineChartData', type)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.panel-group {\r\n  margin-top: 18px;\r\n\r\n  .card-panel-col {\r\n    margin-bottom: 32px;\r\n  }\r\n\r\n  .card-panel {\r\n    height: 108px;\r\n    cursor: pointer;\r\n    font-size: 12px;\r\n    position: relative;\r\n    overflow: hidden;\r\n    color: #666;\r\n    background: #fff;\r\n    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);\r\n    border-color: rgba(0, 0, 0, .05);\r\n\r\n    &:hover {\r\n      .card-panel-icon-wrapper {\r\n        color: #fff;\r\n      }\r\n\r\n      .icon-people {\r\n        background: #40c9c6;\r\n      }\r\n\r\n      .icon-message {\r\n        background: #36a3f7;\r\n      }\r\n\r\n      .icon-money {\r\n        background: #f4516c;\r\n      }\r\n\r\n      .icon-shopping {\r\n        background: #34bfa3\r\n      }\r\n    }\r\n\r\n    .icon-people {\r\n      color: #40c9c6;\r\n    }\r\n\r\n    .icon-message {\r\n      color: #36a3f7;\r\n    }\r\n\r\n    .icon-money {\r\n      color: #f4516c;\r\n    }\r\n\r\n    .icon-shopping {\r\n      color: #34bfa3\r\n    }\r\n\r\n    .card-panel-icon-wrapper {\r\n      float: left;\r\n      margin: 14px 0 0 14px;\r\n      padding: 16px;\r\n      transition: all 0.38s ease-out;\r\n      border-radius: 6px;\r\n    }\r\n\r\n    .card-panel-icon {\r\n      float: left;\r\n      font-size: 48px;\r\n    }\r\n\r\n    .card-panel-description {\r\n      float: right;\r\n      font-weight: bold;\r\n      margin: 26px;\r\n      margin-left: 0px;\r\n\r\n      .card-panel-text {\r\n        line-height: 18px;\r\n        color: rgba(0, 0, 0, 0.45);\r\n        font-size: 16px;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .card-panel-num {\r\n        font-size: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width:550px) {\r\n  .card-panel-description {\r\n    display: none;\r\n  }\r\n\r\n  .card-panel-icon-wrapper {\r\n    float: none !important;\r\n    width: 100%;\r\n    height: 100%;\r\n    margin: 0 !important;\r\n\r\n    .svg-icon {\r\n      display: block;\r\n      margin: 14px auto !important;\r\n      float: none !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}