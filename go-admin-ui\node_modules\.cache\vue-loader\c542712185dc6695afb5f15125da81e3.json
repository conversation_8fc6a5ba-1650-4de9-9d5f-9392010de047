{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue?vue&type=template&id=cbc28918&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue", "mtime": 1753924830307}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImVyclBhZ2UtY29udGFpbmVyIj4NCiAgICA8ZWwtYnV0dG9uIGljb249ImVsLWljb24tYXJyb3ctbGVmdCIgY2xhc3M9InBhbi1iYWNrLWJ0biIgQGNsaWNrPSJiYWNrIj4NCiAgICAgIOi/lOWbng0KICAgIDwvZWwtYnV0dG9uPg0KICAgIDxlbC1yb3c+DQogICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+DQogICAgICAgIDxoMSBjbGFzcz0idGV4dC1qdW1ibyB0ZXh0LWdpbm9ybW91cyI+DQogICAgICAgICAgT29wcyENCiAgICAgICAgPC9oMT4NCiAgICAgICAgZ2lm5p2l5rqQPGEgaHJlZj0iaHR0cHM6Ly96aC5haXJibmIuY29tLyIgdGFyZ2V0PSJfYmxhbmsiPmFpcmJuYjwvYT4g6aG16Z2iDQogICAgICAgIDxoMj7kvaDmsqHmnInmnYPpmZDljrvor6XpobXpnaI8L2gyPg0KICAgICAgICA8aDY+5aaC5pyJ5LiN5ruh6K+36IGU57O75L2g6aKG5a+8PC9oNj4NCiAgICAgICAgPHVsIGNsYXNzPSJsaXN0LXVuc3R5bGVkIj4NCiAgICAgICAgICA8bGk+5oiW6ICF5L2g5Y+v5Lul5Y67OjwvbGk+DQogICAgICAgICAgPGxpIGNsYXNzPSJsaW5rLXR5cGUiPg0KICAgICAgICAgICAgPHJvdXRlci1saW5rIHRvPSIvZGFzaGJvYXJkIj4NCiAgICAgICAgICAgICAg5Zue6aaW6aG1DQogICAgICAgICAgICA8L3JvdXRlci1saW5rPg0KICAgICAgICAgIDwvbGk+DQogICAgICAgICAgPGxpIGNsYXNzPSJsaW5rLXR5cGUiPg0KICAgICAgICAgICAgPGEgaHJlZj0iaHR0cHM6Ly93d3cudGFvYmFvLmNvbS8iPumaj+S+v+eci+ecizwvYT4NCiAgICAgICAgICA8L2xpPg0KICAgICAgICAgIDxsaT48YSBocmVmPSIjIiBAY2xpY2sucHJldmVudD0iZGlhbG9nVmlzaWJsZT10cnVlIj7ngrnmiJHnnIvlm748L2E+PC9saT4NCiAgICAgICAgPC91bD4NCiAgICAgIDwvZWwtY29sPg0KICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPg0KICAgICAgICA8aW1nIDpzcmM9ImVyckdpZiIgd2lkdGg9IjMxMyIgaGVpZ2h0PSI0MjgiIGFsdD0iR2lybCBoYXMgZHJvcHBlZCBoZXIgaWNlIGNyZWFtLiI+DQogICAgICA8L2VsLWNvbD4NCiAgICA8L2VsLXJvdz4NCiAgICA8ZWwtZGlhbG9nIDp2aXNpYmxlLnN5bmM9ImRpYWxvZ1Zpc2libGUiIHRpdGxlPSLpmo/kvr/nnIsiIDpjbG9zZS1vbi1jbGljay1tb2RhbD0iZmFsc2UiPg0KICAgICAgPGltZyA6c3JjPSJld2l6YXJkQ2xhcCIgY2xhc3M9InBhbi1pbWciPg0KICAgIDwvZWwtZGlhbG9nPg0KICA8L2Rpdj4NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\error-page\\401.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/error-page/401.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"errPage-container\">\r\n    <el-button icon=\"el-icon-arrow-left\" class=\"pan-back-btn\" @click=\"back\">\r\n      返回\r\n    </el-button>\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <h1 class=\"text-jumbo text-ginormous\">\r\n          Oops!\r\n        </h1>\r\n        gif来源<a href=\"https://zh.airbnb.com/\" target=\"_blank\">airbnb</a> 页面\r\n        <h2>你没有权限去该页面</h2>\r\n        <h6>如有不满请联系你领导</h6>\r\n        <ul class=\"list-unstyled\">\r\n          <li>或者你可以去:</li>\r\n          <li class=\"link-type\">\r\n            <router-link to=\"/dashboard\">\r\n              回首页\r\n            </router-link>\r\n          </li>\r\n          <li class=\"link-type\">\r\n            <a href=\"https://www.taobao.com/\">随便看看</a>\r\n          </li>\r\n          <li><a href=\"#\" @click.prevent=\"dialogVisible=true\">点我看图</a></li>\r\n        </ul>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <img :src=\"errGif\" width=\"313\" height=\"428\" alt=\"Girl has dropped her ice cream.\">\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog :visible.sync=\"dialogVisible\" title=\"随便看\" :close-on-click-modal=\"false\">\r\n      <img :src=\"ewizardClap\" class=\"pan-img\">\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport errGif from '@/assets/401_images/401.gif'\r\n\r\nexport default {\r\n  name: 'Page401',\r\n  data() {\r\n    return {\r\n      errGif: errGif + '?' + +new Date(),\r\n      ewizardClap: 'https://wpimg.wallstcn.com/007ef517-bafd-4066-aae4-6883632d9646',\r\n      dialogVisible: false\r\n    }\r\n  },\r\n  methods: {\r\n    back() {\r\n      if (this.$route.query.noGoBack) {\r\n        this.$router.push({ path: '/dashboard' })\r\n      } else {\r\n        this.$router.go(-1)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .errPage-container {\r\n    width: 800px;\r\n    max-width: 100%;\r\n    margin: 100px auto;\r\n    .pan-back-btn {\r\n      background: #008489;\r\n      color: #fff;\r\n      border: none!important;\r\n    }\r\n    .pan-gif {\r\n      margin: 0 auto;\r\n      display: block;\r\n    }\r\n    .pan-img {\r\n      display: block;\r\n      margin: 0 auto;\r\n      width: 100%;\r\n    }\r\n    .text-jumbo {\r\n      font-size: 60px;\r\n      font-weight: 700;\r\n      color: #484848;\r\n    }\r\n    .list-unstyled {\r\n      font-size: 14px;\r\n      li {\r\n        padding-bottom: 5px;\r\n      }\r\n      a {\r\n        color: #008489;\r\n        text-decoration: none;\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}