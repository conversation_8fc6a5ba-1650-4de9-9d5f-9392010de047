{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\RaddarChart.vue?vue&type=template&id=bafd8c8c", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\RaddarChart.vue", "mtime": 1753924830287}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgOmNsYXNzPSJjbGFzc05hbWUiIDpzdHlsZT0ie2hlaWdodDpoZWlnaHQsd2lkdGg6d2lkdGh9IiAvPg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\admin\\components\\RaddarChart.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dashboard/admin/components/RaddarChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 3000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        radar: {\r\n          radius: '66%',\r\n          center: ['50%', '42%'],\r\n          splitNumber: 8,\r\n          splitArea: {\r\n            areaStyle: {\r\n              color: 'rgba(127,95,132,.3)',\r\n              opacity: 1,\r\n              shadowBlur: 45,\r\n              shadowColor: 'rgba(0,0,0,.5)',\r\n              shadowOffsetX: 0,\r\n              shadowOffsetY: 15\r\n            }\r\n          },\r\n          indicator: [\r\n            { name: 'Sales', max: 10000 },\r\n            { name: 'Administration', max: 20000 },\r\n            { name: 'Information Techology', max: 20000 },\r\n            { name: 'Customer Support', max: 20000 },\r\n            { name: 'Development', max: 20000 },\r\n            { name: 'Marketing', max: 20000 }\r\n          ]\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Allocated Budget', 'Expected Spending', 'Actual Spending']\r\n        },\r\n        series: [{\r\n          type: 'radar',\r\n          symbolSize: 0,\r\n          areaStyle: {\r\n            normal: {\r\n              shadowBlur: 13,\r\n              shadowColor: 'rgba(0,0,0,.2)',\r\n              shadowOffsetX: 0,\r\n              shadowOffsetY: 10,\r\n              opacity: 1\r\n            }\r\n          },\r\n          data: [\r\n            {\r\n              value: [5000, 7000, 12000, 11000, 15000, 14000],\r\n              name: 'Allocated Budget'\r\n            },\r\n            {\r\n              value: [4000, 9000, 15000, 15000, 13000, 11000],\r\n              name: 'Expected Spending'\r\n            },\r\n            {\r\n              value: [5500, 11000, 12000, 15000, 12000, 12000],\r\n              name: 'Actual Spending'\r\n            }\r\n          ],\r\n          animationDuration: animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}