{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-user.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\api\\admin\\sys-user.js", "mtime": 1753924829933}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "listUser", "query", "url", "method", "params", "getUser", "userId", "getUserInit", "addUser", "data", "updateUser", "<PERSON><PERSON><PERSON>", "exportUser", "resetUserPwd", "password", "changeUserStatus", "e", "status", "updateUserProfile", "importTemplate", "updateUserPwd", "oldPassword", "newPassword", "uploadAvatar", "getUserProfile"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/api/admin/sys-user.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询用户列表\r\nexport function listUser(query) {\r\n  return request({\r\n    url: '/api/v1/sys-user',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询用户详细\r\nexport function getUser(userId) {\r\n  return request({\r\n    url: '/api/v1/sys-user/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getUserInit() {\r\n  return request({\r\n    url: '/api/v1/sys-user/',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增用户\r\nexport function addUser(data) {\r\n  return request({\r\n    url: '/api/v1/sys-user',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改用户\r\nexport function updateUser(data) {\r\n  return request({\r\n    url: '/api/v1/sys-user',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除用户\r\nexport function delUser(data) {\r\n  return request({\r\n    url: '/api/v1/sys-user',\r\n    method: 'delete',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 导出用户\r\nexport function exportUser(query) {\r\n  return request({\r\n    url: '/api/v1/sys-user/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function resetUserPwd(userId, password) {\r\n  const data = {\r\n    userId,\r\n    password\r\n  }\r\n  return request({\r\n    url: '/api/v1/user/pwd/reset',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户状态修改\r\nexport function changeUserStatus(e) {\r\n  const data = {\r\n    userId: e.userId,\r\n    status: e.status\r\n  }\r\n  return request({\r\n    url: '/api/v1/user/status',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改用户个人信息\r\nexport function updateUserProfile(data) {\r\n  return request({\r\n    url: '/api/v1/sys-user/profile',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 下载用户导入模板\r\nexport function importTemplate() {\r\n  return request({\r\n    url: '/api/v1/sys-user/importTemplate',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 用户密码重置\r\nexport function updateUserPwd(oldPassword, newPassword) {\r\n  const data = {\r\n    oldPassword,\r\n    newPassword\r\n  }\r\n  return request({\r\n    url: '/api/v1/user/pwd/set',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 用户头像上传\r\nexport function uploadAvatar(data) {\r\n  return request({\r\n    url: '/api/v1/user/avatar',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询用户个人信息\r\nexport function getUserProfile() {\r\n  return request({\r\n    url: '/api/v1/user/profile',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAOP,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB,GAAGI,MAAM;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEA,OAAO,SAASI,WAAWA,CAAA,EAAG;EAC5B,OAAOR,OAAO,CAAC;IACbG,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASE,OAAOA,CAACF,IAAI,EAAE;EAC5B,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,QAAQ;IAChBM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,UAAUA,CAACX,KAAK,EAAE;EAChC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEH;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASY,YAAYA,CAACP,MAAM,EAAEQ,QAAQ,EAAE;EAC7C,IAAML,IAAI,GAAG;IACXH,MAAM,EAANA,MAAM;IACNQ,QAAQ,EAARA;EACF,CAAC;EACD,OAAOf,OAAO,CAAC;IACbG,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASM,gBAAgBA,CAACC,CAAC,EAAE;EAClC,IAAMP,IAAI,GAAG;IACXH,MAAM,EAAEU,CAAC,CAACV,MAAM;IAChBW,MAAM,EAAED,CAAC,CAACC;EACZ,CAAC;EACD,OAAOlB,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASS,iBAAiBA,CAACT,IAAI,EAAE;EACtC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASU,cAAcA,CAAA,EAAG;EAC/B,OAAOpB,OAAO,CAAC;IACbG,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASiB,aAAaA,CAACC,WAAW,EAAEC,WAAW,EAAE;EACtD,IAAMb,IAAI,GAAG;IACXY,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA;EACF,CAAC;EACD,OAAOvB,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASc,YAAYA,CAACd,IAAI,EAAE;EACjC,OAAOV,OAAO,CAAC;IACbG,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASe,cAAcA,CAAA,EAAG;EAC/B,OAAOzB,OAAO,CAAC;IACbG,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}