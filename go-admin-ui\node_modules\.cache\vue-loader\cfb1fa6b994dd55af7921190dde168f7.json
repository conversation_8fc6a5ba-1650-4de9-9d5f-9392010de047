{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue?vue&type=style&index=0&id=218e8838&lang=scss&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue", "mtime": 1753924830445}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\sass-loader@9.0.3\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYm94LWNlbnRlciB7DQogIG1hcmdpbjogMCBhdXRvOw0KICBkaXNwbGF5OiB0YWJsZTsNCn0NCg0KLnRleHQtbXV0ZWQgew0KICBjb2xvcjogIzc3NzsNCn0NCg0KLnVzZXItcHJvZmlsZSB7DQogIC51c2VyLW5hbWUgew0KICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICB9DQoNCiAgLmJveC1jZW50ZXIgew0KICAgIHBhZGRpbmctdG9wOiAxMHB4Ow0KICB9DQoNCiAgLnVzZXItcm9sZSB7DQogICAgcGFkZGluZy10b3A6IDEwcHg7DQogICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCg0KICAuYm94LXNvY2lhbCB7DQogICAgcGFkZGluZy10b3A6IDMwcHg7DQoNCiAgICAuZWwtdGFibGUgew0KICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNkZmU2ZWM7DQogICAgfQ0KICB9DQoNCiAgLnVzZXItZm9sbG93IHsNCiAgICBwYWRkaW5nLXRvcDogMjBweDsNCiAgfQ0KfQ0KDQoudXNlci1iaW8gew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCg0KICBzcGFuIHsNCiAgICBwYWRkaW5nLWxlZnQ6IDRweDsNCiAgfQ0KDQogIC51c2VyLWJpby1zZWN0aW9uIHsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgcGFkZGluZzogMTVweCAwOw0KDQogICAgLnVzZXItYmlvLXNlY3Rpb24taGVhZGVyIHsNCiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZGZlNmVjOw0KICAgICAgcGFkZGluZy1ib3R0b206IDEwcHg7DQogICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\UserCard.vue"], "names": [], "mappings": ";AA4EA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEd,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/UserCard.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card style=\"margin-bottom:20px;\">\r\n    <div slot=\"header\" class=\"clearfix\">\r\n      <span>About me</span>\r\n    </div>\r\n\r\n    <div class=\"user-profile\">\r\n      <div class=\"box-center\">\r\n        <pan-thumb :image=\"user.avatar\" :height=\"'100px'\" :width=\"'100px'\" :hoverable=\"false\">\r\n          <div>Hello</div>\r\n          {{ user.role }}\r\n        </pan-thumb>\r\n      </div>\r\n      <div class=\"box-center\">\r\n        <div class=\"user-name text-center\">{{ user.name }}</div>\r\n        <div class=\"user-role text-center text-muted\">{{ user.role | uppercaseFirst }}</div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"user-bio\">\r\n      <div class=\"user-education user-bio-section\">\r\n        <div class=\"user-bio-section-header\"><svg-icon icon-class=\"education\" /><span>Education</span></div>\r\n        <div class=\"user-bio-section-body\">\r\n          <div class=\"text-muted\">\r\n            JS in Computer Science from the University of Technology\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"user-skills user-bio-section\">\r\n        <div class=\"user-bio-section-header\"><svg-icon icon-class=\"skill\" /><span>Skills</span></div>\r\n        <div class=\"user-bio-section-body\">\r\n          <div class=\"progress-item\">\r\n            <span>Vue</span>\r\n            <el-progress :percentage=\"70\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>JavaScript</span>\r\n            <el-progress :percentage=\"18\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>Css</span>\r\n            <el-progress :percentage=\"12\" />\r\n          </div>\r\n          <div class=\"progress-item\">\r\n            <span>ESLint</span>\r\n            <el-progress :percentage=\"100\" status=\"success\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport PanThumb from '@/components/PanThumb'\r\n\r\nexport default {\r\n  components: { PanThumb },\r\n  props: {\r\n    user: {\r\n      type: Object,\r\n      default: () => {\r\n        return {\r\n          name: '',\r\n          email: '',\r\n          avatar: '',\r\n          roles: ''\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.box-center {\r\n  margin: 0 auto;\r\n  display: table;\r\n}\r\n\r\n.text-muted {\r\n  color: #777;\r\n}\r\n\r\n.user-profile {\r\n  .user-name {\r\n    font-weight: bold;\r\n  }\r\n\r\n  .box-center {\r\n    padding-top: 10px;\r\n  }\r\n\r\n  .user-role {\r\n    padding-top: 10px;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .box-social {\r\n    padding-top: 30px;\r\n\r\n    .el-table {\r\n      border-top: 1px solid #dfe6ec;\r\n    }\r\n  }\r\n\r\n  .user-follow {\r\n    padding-top: 20px;\r\n  }\r\n}\r\n\r\n.user-bio {\r\n  margin-top: 20px;\r\n  color: #606266;\r\n\r\n  span {\r\n    padding-left: 4px;\r\n  }\r\n\r\n  .user-bio-section {\r\n    font-size: 14px;\r\n    padding: 15px 0;\r\n\r\n    .user-bio-section-header {\r\n      border-bottom: 1px solid #dfe6ec;\r\n      padding-bottom: 10px;\r\n      margin-bottom: 10px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}