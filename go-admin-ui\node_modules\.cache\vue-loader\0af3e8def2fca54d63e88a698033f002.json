{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue?vue&type=template&id=74e6bfaa", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue", "mtime": 1753924830444}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImJsb2NrIj4NCiAgICA8ZWwtdGltZWxpbmU+DQogICAgICA8ZWwtdGltZWxpbmUtaXRlbSB2LWZvcj0iKGl0ZW0saW5kZXgpIG9mIHRpbWVsaW5lIiA6a2V5PSJpbmRleCIgOnRpbWVzdGFtcD0iaXRlbS50aW1lc3RhbXAiIHBsYWNlbWVudD0idG9wIj4NCiAgICAgICAgPGVsLWNhcmQ+DQogICAgICAgICAgPGg0Pnt7IGl0ZW0udGl0bGUgfX08L2g0Pg0KICAgICAgICAgIDxwPnt7IGl0ZW0uY29udGVudCB9fTwvcD4NCiAgICAgICAgPC9lbC1jYXJkPg0KICAgICAgPC9lbC10aW1lbGluZS1pdGVtPg0KICAgIDwvZWwtdGltZWxpbmU+DQogIDwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\profile\\components\\Timeline.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/profile/components/Timeline.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"block\">\r\n    <el-timeline>\r\n      <el-timeline-item v-for=\"(item,index) of timeline\" :key=\"index\" :timestamp=\"item.timestamp\" placement=\"top\">\r\n        <el-card>\r\n          <h4>{{ item.title }}</h4>\r\n          <p>{{ item.content }}</p>\r\n        </el-card>\r\n      </el-timeline-item>\r\n    </el-timeline>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      timeline: [\r\n        {\r\n          timestamp: '2019/4/20',\r\n          title: 'Update Github template',\r\n          content: 'PanJiaChen committed 2019/4/20 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/21',\r\n          title: 'Update Github template',\r\n          content: 'PanJiaChen committed 2019/4/21 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/22',\r\n          title: 'Build Template',\r\n          content: 'PanJiaChen committed 2019/4/22 20:46'\r\n        },\r\n        {\r\n          timestamp: '2019/4/23',\r\n          title: 'Release New Version',\r\n          content: 'PanJiaChen committed 2019/4/23 20:46'\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}