{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue", "mtime": 1753924830296}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdCYXNpY0luZm9Gb3JtJywNCiAgcHJvcHM6IHsNCiAgICBpbmZvOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiBudWxsDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBydWxlczogew0KICAgICAgICB0YWJsZU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6KGo5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBwYXR0ZXJuOiAvXlthLXpcLl9dKiQvZywgdHJpZ2dlcjogJ2JsdXInLCBtZXNzYWdlOiAn5Y+q5YWB6K645bCP5YaZ5a2X5q+NLOS+i+WmgiBzeXNfZGVtbyDmoLzlvI8nIH0NCiAgICAgICAgXSwNCiAgICAgICAgdGFibGVDb21tZW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiPnOWNleWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGNsYXNzTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmqKHlnovlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IHBhdHRlcm46IC9eW0EtWl1bQS16MC05XSokL2csIHRyaWdnZXI6ICdibHVyJywgbWVzc2FnZTogJ+W/hemhu+S7peWkp+WGmeWtl+avjeW8gOWktCzkvovlpoIgU3lzRGVtbyDmoLzlvI8nIH0NCiAgICAgICAgXSwNCiAgICAgICAgZnVuY3Rpb25BdXRob3I6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5L2c6ICFJywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBwYXR0ZXJuOiAvXltBLVphLXpdKyQvLCB0cmlnZ2VyOiAnYmx1cicsIG1lc3NhZ2U6ICfmoKHpqozop4TliJk6ICDlj6rlhYHorrjovpPlhaXlrZfmr40gYS16IOaIluWkp+WGmSBBLVonIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dev-tools\\gen\\basicInfoForm.vue"], "names": [], "mappings": ";AAsEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAChF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACvD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACtF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACrD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACnF;MACF;IACF;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/dev-tools/gen/basicInfoForm.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-form ref=\"basicInfoForm\" :model=\"info\" :rules=\"rules\" label-width=\"150px\">\r\n    <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tableName\">\r\n          <span slot=\"label\">\r\n            数据表名称\r\n            <el-tooltip content=\"数据库表名称，针对gorm对应的table()使用，⚠️这里必须是蛇形结构\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.tableName\" placeholder=\"请输入表名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"tableComment\">\r\n          <span slot=\"label\">\r\n            菜单名称\r\n            <el-tooltip content=\"同步的数据库表名称，生成配置数据时，用作菜单名称\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.tableComment\" placeholder=\"请输入菜单名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item prop=\"className\">\r\n          <span slot=\"label\">\r\n            结构体模型名称\r\n            <el-tooltip content=\"结构体模型名称，代码中的struct名称定义使用\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-input v-model=\"info.className\" placeholder=\"请输入\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item label=\"作者名称\" prop=\"functionAuthor\">\r\n          <el-input v-model=\"info.functionAuthor\" placeholder=\"请输入作者名称\" />\r\n        </el-form-item>\r\n      </el-col>\r\n      <!-- <el-col :span=\"12\">\r\n        <el-form-item prop=\"isLogicalDelete\">\r\n          <span slot=\"label\">\r\n            是否逻辑删除\r\n            <el-tooltip content=\"目前只支持逻辑删除\" placement=\"top\">\r\n              <i class=\"el-icon-question\" />\r\n            </el-tooltip>\r\n          </span>\r\n          <el-radio-group v-model=\"info.isLogicalDelete\">\r\n            <el-radio label=\"1\">是</el-radio>\r\n            <el-radio label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n\r\n        </el-form-item>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <el-form-item v-if=\"info.isLogicalDelete == '1'\" label=\"逻辑删除字段\" prop=\"logicalDeleteColumn\">\r\n          <el-input v-model=\"info.logicalDeleteColumn\" placeholder=\"请输入\" />\r\n        </el-form-item>\r\n      </el-col> -->\r\n      <el-col :span=\"24\">\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"info.remark\" type=\"textarea\" :rows=\"3\" />\r\n        </el-form-item>\r\n      </el-col>\r\n    </el-row>\r\n  </el-form>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'BasicInfoForm',\r\n  props: {\r\n    info: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      rules: {\r\n        tableName: [\r\n          { required: true, message: '请输入表名称', trigger: 'blur' },\r\n          { pattern: /^[a-z\\._]*$/g, trigger: 'blur', message: '只允许小写字母,例如 sys_demo 格式' }\r\n        ],\r\n        tableComment: [\r\n          { required: true, message: '请输入菜单名称', trigger: 'blur' }\r\n        ],\r\n        className: [\r\n          { required: true, message: '请输入模型名称', trigger: 'blur' },\r\n          { pattern: /^[A-Z][A-z0-9]*$/g, trigger: 'blur', message: '必须以大写字母开头,例如 SysDemo 格式' }\r\n        ],\r\n        functionAuthor: [\r\n          { required: true, message: '请输入作者', trigger: 'blur' },\r\n          { pattern: /^[A-Za-z]+$/, trigger: 'blur', message: '校验规则:  只允许输入字母 a-z 或大写 A-Z' }\r\n        ]\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}