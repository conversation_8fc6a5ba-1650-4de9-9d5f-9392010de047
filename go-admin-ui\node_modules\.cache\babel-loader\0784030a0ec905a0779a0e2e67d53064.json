{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\error-log.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\utils\\error-log.js", "mtime": 1753924830248}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaW5jbHVkZXMuanMiOwppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmltcG9ydCBzdG9yZSBmcm9tICdAL3N0b3JlJzsKaW1wb3J0IHsgaXNTdHJpbmcsIGlzQXJyYXkgfSBmcm9tICdAL3V0aWxzL3ZhbGlkYXRlJzsKaW1wb3J0IHNldHRpbmdzIGZyb20gJ0Avc2V0dGluZ3MnOwoKLy8geW91IGNhbiBzZXQgaW4gc2V0dGluZ3MuanMKLy8gZXJyb3JMb2c6J3Byb2R1Y3Rpb24nIHwgWydwcm9kdWN0aW9uJywgJ2RldmVsb3BtZW50J10KdmFyIG5lZWRFcnJvckxvZyA9IHNldHRpbmdzLmVycm9yTG9nOwpmdW5jdGlvbiBjaGVja05lZWQoKSB7CiAgdmFyIGVudiA9IHByb2Nlc3MuZW52Lk5PREVfRU5WOwogIGlmIChpc1N0cmluZyhuZWVkRXJyb3JMb2cpKSB7CiAgICByZXR1cm4gZW52ID09PSBuZWVkRXJyb3JMb2c7CiAgfQogIGlmIChpc0FycmF5KG5lZWRFcnJvckxvZykpIHsKICAgIHJldHVybiBuZWVkRXJyb3JMb2cuaW5jbHVkZXMoZW52KTsKICB9CiAgcmV0dXJuIGZhbHNlOwp9CmlmIChjaGVja05lZWQoKSkgewogIFZ1ZS5jb25maWcuZXJyb3JIYW5kbGVyID0gZnVuY3Rpb24gKGVyciwgdm0sIGluZm8sIGEpIHsKICAgIC8vIERvbid0IGFzayBtZSB3aHkgSSB1c2UgVnVlLm5leHRUaWNrLCBpdCBqdXN0IGEgaGFjay4KICAgIC8vIGRldGFpbCBzZWUgaHR0cHM6Ly9mb3J1bS52dWVqcy5vcmcvdC9kaXNwYXRjaC1pbi12dWUtY29uZmlnLWVycm9yaGFuZGxlci1oYXMtc29tZS1wcm9ibGVtLzIzNTAwCiAgICBWdWUubmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICBzdG9yZS5kaXNwYXRjaCgnZXJyb3JMb2cvYWRkRXJyb3JMb2cnLCB7CiAgICAgICAgZXJyOiBlcnIsCiAgICAgICAgdm06IHZtLAogICAgICAgIGluZm86IGluZm8sCiAgICAgICAgdXJsOiB3aW5kb3cubG9jYXRpb24uaHJlZgogICAgICB9KTsKICAgICAgY29uc29sZS5lcnJvcihlcnIsIGluZm8pOwogICAgfSk7CiAgfTsKfQ=="}, {"version": 3, "names": ["<PERSON><PERSON>", "store", "isString", "isArray", "settings", "needErrorLog", "errorLog", "checkNeed", "env", "process", "NODE_ENV", "includes", "config", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "info", "a", "nextTick", "dispatch", "url", "window", "location", "href", "console", "error"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/utils/error-log.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport store from '@/store'\r\nimport { isString, isArray } from '@/utils/validate'\r\nimport settings from '@/settings'\r\n\r\n// you can set in settings.js\r\n// errorLog:'production' | ['production', 'development']\r\nconst { errorLog: needErrorLog } = settings\r\n\r\nfunction checkNeed() {\r\n  const env = process.env.NODE_ENV\r\n  if (isString(needErrorLog)) {\r\n    return env === needErrorLog\r\n  }\r\n  if (isArray(needErrorLog)) {\r\n    return needErrorLog.includes(env)\r\n  }\r\n  return false\r\n}\r\n\r\nif (checkNeed()) {\r\n  Vue.config.errorHandler = function(err, vm, info, a) {\r\n  // Don't ask me why I use Vue.nextTick, it just a hack.\r\n  // detail see https://forum.vuejs.org/t/dispatch-in-vue-config-errorhandler-has-some-problem/23500\r\n    Vue.nextTick(() => {\r\n      store.dispatch('errorLog/addErrorLog', {\r\n        err,\r\n        vm,\r\n        info,\r\n        url: window.location.href\r\n      })\r\n      console.error(err, info)\r\n    })\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,QAAQ,EAAEC,OAAO,QAAQ,kBAAkB;AACpD,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;AACA;AACA,IAAkBC,YAAY,GAAKD,QAAQ,CAAnCE,QAAQ;AAEhB,SAASC,SAASA,CAAA,EAAG;EACnB,IAAMC,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,QAAQ;EAChC,IAAIR,QAAQ,CAACG,YAAY,CAAC,EAAE;IAC1B,OAAOG,GAAG,KAAKH,YAAY;EAC7B;EACA,IAAIF,OAAO,CAACE,YAAY,CAAC,EAAE;IACzB,OAAOA,YAAY,CAACM,QAAQ,CAACH,GAAG,CAAC;EACnC;EACA,OAAO,KAAK;AACd;AAEA,IAAID,SAAS,CAAC,CAAC,EAAE;EACfP,GAAG,CAACY,MAAM,CAACC,YAAY,GAAG,UAASC,GAAG,EAAEC,EAAE,EAAEC,IAAI,EAAEC,CAAC,EAAE;IACrD;IACA;IACEjB,GAAG,CAACkB,QAAQ,CAAC,YAAM;MACjBjB,KAAK,CAACkB,QAAQ,CAAC,sBAAsB,EAAE;QACrCL,GAAG,EAAHA,GAAG;QACHC,EAAE,EAAFA,EAAE;QACFC,IAAI,EAAJA,IAAI;QACJI,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACvB,CAAC,CAAC;MACFC,OAAO,CAACC,KAAK,CAACX,GAAG,EAAEE,IAAI,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}]}