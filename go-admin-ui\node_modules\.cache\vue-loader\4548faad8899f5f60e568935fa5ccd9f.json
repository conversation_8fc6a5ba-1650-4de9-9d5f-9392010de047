{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue?vue&type=template&id=fac8ca64&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1753924830216}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\TagsView\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD;UACE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1I,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1H,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtI,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/TagsView/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n    <el-tabs\r\n      v-model=\"editableTabsValue\"\r\n      type=\"card\"\r\n      @tab-remove=\"closeSelectedTag\"\r\n    >\r\n      <el-tab-pane\r\n        v-for=\"item in visitedViews\"\r\n        :key=\"item.path\"\r\n        :closable=\"item.fullPath === '/dashboard' ? false : true\"\r\n        :name=\"item.fullPath\"\r\n      >\r\n        <router-link\r\n          ref=\"tag\"\r\n          slot=\"label\"\r\n          tag=\"span\"\r\n          class=\"tags-view-item\"\r\n          :style=\"{ color: item.fullPath === $route.fullPath ? theme : '' }\"\r\n          :to=\"{ path: item.path, query: item.query, fullPath: item.fullPath }\"\r\n          @contextmenu.prevent.native=\"openMenu(item,$event)\"\r\n        >\r\n          {{ item.title }}\r\n        </router-link>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n      <li class=\"tags-item\" @click=\"refreshSelectedTag(selectedTag)\" @mouseover=\"handleTagsOver(1)\" @mouseleave=\"handleTagsLeave(1)\">刷新当前标签页</li>\r\n      <li v-if=\"!isAffix(selectedTag)\" class=\"tags-item\" @click=\"closeSelectedTag(selectedTag)\" @mouseover=\"handleTagsOver(2)\" @mouseleave=\"handleTagsLeave(2)\">关闭当前标签页</li>\r\n      <li class=\"tags-item\" @click=\"closeOthersTags\" @mouseover=\"handleTagsOver(3)\" @mouseleave=\"handleTagsLeave(3)\">关闭其他标签页</li>\r\n      <li class=\"tags-item\" @click=\"closeAllTags(selectedTag)\" @mouseover=\"handleTagsOver(4)\" @mouseleave=\"handleTagsLeave(4)\">关闭全部标签页</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      editableTabsValue: '/',\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: [],\r\n      visible: false\r\n    }\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags()\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.closeMenu)\r\n      } else {\r\n        document.body.removeEventListener('click', this.closeMenu)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTags()\r\n    this.addTags()\r\n    this.isActive()\r\n    this.beforeUnload()\r\n  },\r\n  methods: {\r\n    // 刷新前缓存tab\r\n    beforeUnload() {\r\n      // 监听页面刷新\r\n      window.addEventListener('beforeunload', () => {\r\n        const tabViews = this.visitedViews.map(item => {\r\n          return {\r\n            fullPath: item.fullPath,\r\n            hash: item.hash,\r\n            meta: { ...item.meta },\r\n            name: item.name,\r\n            params: { ...item.params },\r\n            path: item.path,\r\n            query: { ...item.query },\r\n            title: item.title\r\n          }\r\n        })\r\n        sessionStorage.setItem('tabViews', JSON.stringify(tabViews))\r\n      })\r\n      // 页面初始化加载判断缓存中是否有数据\r\n      const oldViews = JSON.parse(sessionStorage.getItem('tabViews')) || []\r\n      if (oldViews.length > 0) {\r\n        this.$store.state.tagsView.visitedViews = oldViews\r\n      }\r\n    },\r\n    handleTagsOver(index) {\r\n      const tags = document.querySelectorAll('.tags-item')\r\n      const item = tags[index - 1]\r\n      item.style.cssText = `color:${this.$store.state.settings.theme};background:${\r\n        this.$store.state.settings.theme.colorRgb()\r\n      }`\r\n    },\r\n    handleTagsLeave(index) {\r\n      const tags = document.querySelectorAll('.tags-item')\r\n      const item = tags[index - 1]\r\n      item.style.cssText = `color:#606266`\r\n    },\r\n    isActive() {\r\n      const index = this.visitedViews.findIndex(item => item.fullPath === this.$route.fullPath)\r\n      const pathIndex = index > -1 ? index : 0\r\n      this.editableTabsValue = this.visitedViews[pathIndex].fullPath\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix\r\n    },\r\n    filterAffixTags(routes, basePath = '/') {\r\n      let tags = []\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path)\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: { ...route.meta }\r\n          })\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path)\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags]\r\n          }\r\n        }\r\n      })\r\n      return tags\r\n    },\r\n    initTags() {\r\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch('tagsView/addVisitedView', tag)\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const { name } = this.$route\r\n      if (name) {\r\n        this.$store.dispatch('tagsView/addView', this.$route)\r\n        this.isActive()\r\n      }\r\n      return false\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path === this.$route.path) {\r\n            // this.$refs.scrollPane.moveToTarget(tag)\r\n            // when query is different then update\r\n            if (tag.to.fullPath !== this.$route.fullPath) {\r\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n            }\r\n            break\r\n          }\r\n        }\r\n      })\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n        const { fullPath } = view\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: '/redirect' + fullPath\r\n          })\r\n        })\r\n      })\r\n    },\r\n    closeSelectedTag(view) {\r\n      const routerPath = view.fullPath ? view.fullPath : view\r\n      const index = this.visitedViews.findIndex(item => item.fullPath === routerPath)\r\n      if (index > -1) {\r\n        const path = this.visitedViews[index]\r\n        this.$store.dispatch('tagsView/delView', path).then(({ visitedViews }) => {\r\n          if (this.editableTabsValue === path.fullPath) {\r\n            this.toLastView(visitedViews, path)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag.path).catch(e => e)\r\n      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\r\n        this.moveToCurrentTag()\r\n      })\r\n    },\r\n    closeAllTags(view) {\r\n      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {\r\n        if (this.affixTags.some(tag => tag.path === view.path)) {\r\n          return\r\n        }\r\n        this.toLastView(visitedViews, view)\r\n      })\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0]\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath).catch(err => err)\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name === 'Dashboard') {\r\n          // to reload home page\r\n          this.$router.replace({ path: '/redirect' + view.fullPath })\r\n        } else {\r\n          this.$router.push('/')\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105\r\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n      const offsetWidth = this.$el.offsetWidth // container width\r\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\r\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft\r\n      } else {\r\n        this.left = left\r\n      }\r\n\r\n      this.top = e.clientY\r\n      this.visible = true\r\n      this.selectedTag = tag\r\n    },\r\n    closeMenu() {\r\n      this.visible = false\r\n    }\r\n  }\r\n}\r\n\r\n// eslint-disable-next-line no-extend-native\r\nString.prototype.colorRgb = function() {\r\n  let sColor = this.toLowerCase()\r\n  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n  if (sColor && reg.test(sColor)) {\r\n    if (sColor.length === 4) {\r\n      let sColorNew = '#'\r\n      for (let i = 1; i < 4; i += 1) {\r\n        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n      }\r\n      sColor = sColorNew\r\n    }\r\n    const sColorChange = []\r\n    for (let i = 1; i < 7; i += 2) {\r\n      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))\r\n    }\r\n    return 'rgba(' + sColorChange.join(',') + ',0.2)'\r\n  } else {\r\n    return sColor\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tags-view-container ::v-deep{\r\n  height: 43px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-bottom: 1px solid #d8dce5;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n  padding: 0 15px;\r\n  box-sizing: border-box;\r\n  .el-tabs__item{\r\n    &:hover{\r\n      color: #000;\r\n    }\r\n  }\r\n  .tags-view-item{\r\n    height: 40px;\r\n    display: inline-block;\r\n  }\r\n  .tags-view-wrapper {\r\n    .tags-view-item {\r\n      display: inline-block;\r\n      position: relative;\r\n      cursor: pointer;\r\n      height: 26px;\r\n      line-height: 26px;\r\n      border: 1px solid #d8dce5;\r\n      color: #495060;\r\n      background: #fff;\r\n      padding: 0 8px;\r\n      font-size: 12px;\r\n      margin-left: 5px;\r\n      margin-top: 4px;\r\n      &:first-of-type {\r\n        margin-left: 15px;\r\n      }\r\n      &:last-of-type {\r\n        margin-right: 15px;\r\n      }\r\n      &.active {\r\n        background-color: #42b983;\r\n        color: #fff;\r\n        border-color: #42b983;\r\n        &::before {\r\n          content: '';\r\n          background: #fff;\r\n          display: inline-block;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          position: relative;\r\n          margin-right: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .contextmenu {\r\n    margin: 0;\r\n    background: #fff;\r\n    z-index: 3000;\r\n    position: absolute;\r\n    list-style-type: none;\r\n    padding: 5px 0;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333;\r\n    box-shadow: 1px 2px 10px #ccc;\r\n    -moz-user-select:none;\r\n    -webkit-user-select:none;\r\n    user-select:none;\r\n    li {\r\n      list-style: none;\r\n      line-height: 36px;\r\n      padding: 2px 20px;\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      cursor: pointer;\r\n      outline: 0;\r\n      cursor: pointer;\r\n      &:hover {\r\n        background: #eee;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n//reset element css of el-icon-close\r\n.tags-view-wrapper {\r\n  .tags-view-item {\r\n    .el-icon-close {\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: 2px;\r\n      border-radius: 50%;\r\n      text-align: center;\r\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\r\n      transform-origin: 100% 50%;\r\n      &:before {\r\n        transform: scale(.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n      }\r\n      &:hover {\r\n        background-color: #b4bccc;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}