{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue", "mtime": 1753924830293}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiRDovRGVza3RvcC9Hb0FkbWluL2dvLWFkbWluLXVpL25vZGVfbW9kdWxlcy8uc3RvcmUvQGJhYmVsK3J1bnRpbWVANy4yOC4yL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IHsgbWFwR2V0dGVycyB9IGZyb20gJ3Z1ZXgnOwppbXBvcnQgUGFuVGh1bWIgZnJvbSAnQC9jb21wb25lbnRzL1BhblRodW1iJzsKaW1wb3J0IEdpdGh1YkNvcm5lciBmcm9tICdAL2NvbXBvbmVudHMvR2l0aHViQ29ybmVyJzsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEYXNoYm9hcmRFZGl0b3InLAogIGNvbXBvbmVudHM6IHsKICAgIFBhblRodW1iOiBQYW5UaHVtYiwKICAgIEdpdGh1YkNvcm5lcjogR2l0aHViQ29ybmVyCiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZW1wdHlHaWY6ICdodHRwczovL3dwaW1nLndhbGxzdGNuLmNvbS8wZTAzYjdkYS1kYjllLTQ4MTktYmExMC05MDE2ZGRmZGFlZDMnCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IF9vYmplY3RTcHJlYWQoe30sIG1hcEdldHRlcnMoWyduYW1lJywgJ2F2YXRhcicsICdyb2xlcyddKSkKfTs="}, {"version": 3, "names": ["mapGetters", "PanThumb", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "components", "data", "emptyGif", "computed", "_objectSpread"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\dashboard\\editor\\index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n    <div class=\" clearfix\">\r\n      <pan-thumb :image=\"avatar\" style=\"float: left\">\r\n        Your roles:\r\n        <span v-for=\"item in roles\" :key=\"item\" class=\"pan-info-roles\">{{ item }}</span>\r\n      </pan-thumb>\r\n      <github-corner style=\"position: absolute; top: 0px; border: 0; right: 0;\" />\r\n      <div class=\"info-container\">\r\n        <span class=\"display_name\">{{ name }}</span>\r\n        <span style=\"font-size:20px;padding-top:20px;display:inline-block;\">Editor's Dashboard</span>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <img :src=\"emptyGif\" class=\"emptyGif\">\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport PanThumb from '@/components/PanThumb'\r\nimport GithubCorner from '@/components/GithubCorner'\r\n\r\nexport default {\r\n  name: 'DashboardEditor',\r\n  components: { PanThumb, GithubCorner },\r\n  data() {\r\n    return {\r\n      emptyGif: 'https://wpimg.wallstcn.com/0e03b7da-db9e-4819-ba10-9016ddfdaed3'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'name',\r\n      'avatar',\r\n      'roles'\r\n    ])\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .emptyGif {\r\n    display: block;\r\n    width: 45%;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .dashboard-editor-container {\r\n    background-color: #e3e3e3;\r\n    min-height: 100vh;\r\n    padding: 50px 60px 0px;\r\n    .pan-info-roles {\r\n      font-size: 12px;\r\n      font-weight: 700;\r\n      color: #333;\r\n      display: block;\r\n    }\r\n    .info-container {\r\n      position: relative;\r\n      margin-left: 190px;\r\n      height: 150px;\r\n      line-height: 200px;\r\n      .display_name {\r\n        font-size: 48px;\r\n        line-height: 48px;\r\n        color: #212121;\r\n        position: absolute;\r\n        top: 25px;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";AAoBA,SAASA,UAAS,QAAS,MAAK;AAChC,OAAOC,QAAO,MAAO,uBAAsB;AAC3C,OAAOC,YAAW,MAAO,2BAA0B;AAEnD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE;IAAEH,QAAQ,EAARA,QAAQ;IAAEC,YAAW,EAAXA;EAAa,CAAC;EACtCG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,QAAQ,EAAAC,aAAA,KACHR,UAAU,CAAC,CACZ,MAAM,EACN,QAAQ,EACR,OAAM,CACP;AAEL", "ignoreList": []}]}