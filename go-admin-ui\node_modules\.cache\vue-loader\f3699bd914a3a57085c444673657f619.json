{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue?vue&type=template&id=5bc425de&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue", "mtime": 1753924830024}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxlbC1jYXJkIDpsb2FkaW5nPSJsb2FkaW5nIiA6Ym9keS1zdHlsZT0ieyBwYWRkaW5nOiAnMjBweCAyNHB4IDhweCcgfSIgOmJvcmRlcmVkPSJmYWxzZSI+DQogICAgPGRpdiBjbGFzcz0iY2hhcnQtY2FyZC1oZWFkZXIiPg0KICAgICAgPGRpdiBjbGFzcz0ibWV0YSI+DQogICAgICAgIDxzcGFuIGNsYXNzPSJjaGFydC1jYXJkLXRpdGxlIj4NCiAgICAgICAgICA8c2xvdCBuYW1lPSJ0aXRsZSI+DQogICAgICAgICAgICB7eyB0aXRsZSB9fQ0KICAgICAgICAgIDwvc2xvdD4NCiAgICAgICAgPC9zcGFuPg0KICAgICAgICA8c3BhbiBjbGFzcz0iY2hhcnQtY2FyZC1hY3Rpb24iPg0KICAgICAgICAgIDxzbG90IG5hbWU9ImFjdGlvbiIgLz4NCiAgICAgICAgPC9zcGFuPg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJ0b3RhbCI+DQogICAgICAgIDxzbG90IG5hbWU9InRvdGFsIj4NCiAgICAgICAgICA8c3Bhbj57eyB0eXBlb2YgdG90YWwgPT09ICdmdW5jdGlvbicgJiYgdG90YWwoKSB8fCB0b3RhbCB9fTwvc3Bhbj4NCiAgICAgICAgPC9zbG90Pg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogICAgPGRpdiBjbGFzcz0iY2hhcnQtY2FyZC1jb250ZW50Ij4NCiAgICAgIDxkaXYgY2xhc3M9ImNvbnRlbnQtZml4Ij4NCiAgICAgICAgPHNsb3QgLz4NCiAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KICAgIDxkaXYgY2xhc3M9ImNoYXJ0LWNhcmQtZm9vdGVyIj4NCiAgICAgIDxkaXYgY2xhc3M9ImZpZWxkIj4NCiAgICAgICAgPHNsb3QgbmFtZT0iZm9vdGVyIiAvPg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogIDwvZWwtY2FyZD4NCg=="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\ChartCard\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/ChartCard/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <el-card :loading=\"loading\" :body-style=\"{ padding: '20px 24px 8px' }\" :bordered=\"false\">\r\n    <div class=\"chart-card-header\">\r\n      <div class=\"meta\">\r\n        <span class=\"chart-card-title\">\r\n          <slot name=\"title\">\r\n            {{ title }}\r\n          </slot>\r\n        </span>\r\n        <span class=\"chart-card-action\">\r\n          <slot name=\"action\" />\r\n        </span>\r\n      </div>\r\n      <div class=\"total\">\r\n        <slot name=\"total\">\r\n          <span>{{ typeof total === 'function' && total() || total }}</span>\r\n        </slot>\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card-content\">\r\n      <div class=\"content-fix\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n    <div class=\"chart-card-footer\">\r\n      <div class=\"field\">\r\n        <slot name=\"footer\" />\r\n      </div>\r\n    </div>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'ChartCard',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    total: {\r\n      type: [Function, Number, String],\r\n      required: false,\r\n      default: null\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .chart-card-header {\r\n    position: relative;\r\n    overflow: hidden;\r\n    width: 100%;\r\n    .meta {\r\n      position: relative;\r\n      overflow: hidden;\r\n      width: 100%;\r\n      color: rgba(0, 0, 0, .45);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n  }\r\n  .chart-card-action {\r\n    cursor: pointer;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n  }\r\n  .chart-card-footer {\r\n    border-top: 1px solid #e8e8e8;\r\n    padding-top: 9px;\r\n    margin-top: 8px;\r\n    > * {\r\n      position: relative;\r\n    }\r\n    .field {\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 0;\r\n      color: rgba(0,0,0,.65);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  .chart-card-content {\r\n    margin-bottom: 12px;\r\n    position: relative;\r\n    height: 46px;\r\n    width: 100%;\r\n    .content-fix {\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n    }\r\n  }\r\n  .total {\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    word-break: break-all;\r\n    white-space: nowrap;\r\n    color: #000;\r\n    margin-top: 4px;\r\n    margin-bottom: 0;\r\n    font-size: 30px;\r\n    line-height: 38px;\r\n    height: 38px;\r\n  }\r\n</style>\r\n"]}]}