{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\BookTypeOption.vue?vue&type=template&id=6f0b9962", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\BookTypeOption.vue", "mtime": 1753924830410}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgc3R5bGU9ImRpc3BsYXk6aW5saW5lLWJsb2NrOyI+DQogICAgPGxhYmVsIGNsYXNzPSJyYWRpby1sYWJlbCI+Qm9vayBUeXBlOiA8L2xhYmVsPg0KICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0iYm9va1R5cGUiIHN0eWxlPSJ3aWR0aDoxMjBweDsiPg0KICAgICAgPGVsLW9wdGlvbg0KICAgICAgICB2LWZvcj0iaXRlbSBpbiBvcHRpb25zIg0KICAgICAgICA6a2V5PSJpdGVtIg0KICAgICAgICA6bGFiZWw9Iml0ZW0iDQogICAgICAgIDp2YWx1ZT0iaXRlbSINCiAgICAgIC8+DQogICAgPC9lbC1zZWxlY3Q+DQogIDwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\excel\\components\\BookTypeOption.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/views/excel/components/BookTypeOption.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div style=\"display:inline-block;\">\r\n    <label class=\"radio-label\">Book Type: </label>\r\n    <el-select v-model=\"bookType\" style=\"width:120px;\">\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item\"\r\n        :label=\"item\"\r\n        :value=\"item\"\r\n      />\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: 'xlsx'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      options: ['xlsx', 'csv', 'txt']\r\n    }\r\n  },\r\n  computed: {\r\n    bookType: {\r\n      get() {\r\n        return this.value\r\n      },\r\n      set(val) {\r\n        this.$emit('input', val)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}