{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1753924830210}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyBpc0V4dGVybmFsIH0gZnJvbSAnQC91dGlscy92YWxpZGF0ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBwcm9wczogew0KICAgIHRvOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGxpbmtQcm9wcyh1cmwpIHsNCiAgICAgIGlmIChpc0V4dGVybmFsKHVybCkpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICBpczogJ2EnLA0KICAgICAgICAgIGhyZWY6IHVybCwNCiAgICAgICAgICB0YXJnZXQ6ICdfYmxhbmsnLA0KICAgICAgICAgIHJlbDogJ25vb3BlbmVyJw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBpczogJ3JvdXRlci1saW5rJywNCiAgICAgICAgdG86IHVybA0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\Sidebar\\Link.vue"], "names": [], "mappings": ";AASA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAE5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,EAAE,CAAC,CAAC;MACR;IACF;EACF;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/Sidebar/Link.vue", "sourceRoot": "", "sourcesContent": ["\r\n<template>\r\n  <!-- eslint-disable vue/require-component-is -->\r\n  <component v-bind=\"linkProps(to)\">\r\n    <slot />\r\n  </component>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(url) {\r\n      if (isExternal(url)) {\r\n        return {\r\n          is: 'a',\r\n          href: url,\r\n          target: '_blank',\r\n          rel: 'noopener'\r\n        }\r\n      }\r\n      return {\r\n        is: 'router-link',\r\n        to: url\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}