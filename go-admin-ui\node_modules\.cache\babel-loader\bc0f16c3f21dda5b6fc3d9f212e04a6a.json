{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\store\\modules\\user.js", "mtime": 1753924830231}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["login", "logout", "getInfo", "refreshtoken", "getToken", "setToken", "removeToken", "router", "resetRouter", "storage", "state", "token", "name", "avatar", "introduction", "roles", "permissions", "permisaction", "mutations", "SET_TOKEN", "SET_INTRODUCTION", "SET_NAME", "SET_AVATAR", "indexOf", "process", "env", "VUE_APP_BASE_API", "SET_ROLES", "SET_PERMISSIONS", "actions", "_ref", "userInfo", "commit", "Promise", "resolve", "reject", "then", "response", "catch", "error", "_ref2", "data", "_response$data", "length", "LogOut", "_ref3", "clear", "refreshToken", "_ref4", "resetToken", "_ref5", "changeRoles", "_ref6", "role", "dispatch", "_ref7", "_asyncToGenerator", "_regenerator", "m", "_callee", "_yield$dispatch", "accessRoutes", "w", "_context", "n", "v", "root", "addRoutes", "a", "_x", "apply", "arguments", "namespaced"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/store/modules/user.js"], "sourcesContent": ["import { login, logout, getInfo, refreshtoken } from '@/api/user'\r\nimport { getToken, setToken, removeToken } from '@/utils/auth'\r\nimport router, { resetRouter } from '@/router'\r\nimport storage from '@/utils/storage'\r\n\r\nconst state = {\r\n  token: getToken(),\r\n  name: '',\r\n  avatar: '',\r\n  introduction: '',\r\n  roles: [],\r\n  permissions: [],\r\n  permisaction: []\r\n}\r\n\r\nconst mutations = {\r\n  SET_TOKEN: (state, token) => {\r\n    state.token = token\r\n  },\r\n  SET_INTRODUCTION: (state, introduction) => {\r\n    state.introduction = introduction\r\n  },\r\n  SET_NAME: (state, name) => {\r\n    state.name = name\r\n  },\r\n  SET_AVATAR: (state, avatar) => {\r\n    if (avatar.indexOf('http') !== -1) {\r\n      state.avatar = avatar\r\n    } else {\r\n      state.avatar = process.env.VUE_APP_BASE_API + avatar\r\n    }\r\n  },\r\n  SET_ROLES: (state, roles) => {\r\n    state.roles = roles\r\n  },\r\n  SET_PERMISSIONS: (state, permisaction) => {\r\n    state.permisaction = permisaction\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  // user login\r\n  login({ commit }, userInfo) {\r\n    return new Promise((resolve, reject) => {\r\n      login(userInfo).then(response => {\r\n        const { token } = response\r\n        commit('SET_TOKEN', token)\r\n        setToken(token)\r\n        resolve()\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n\r\n  // get user info\r\n  getInfo({ commit, state }) {\r\n    return new Promise((resolve, reject) => {\r\n      getInfo().then(response => {\r\n        if (!response || !response.data) {\r\n          commit('SET_TOKEN', '')\r\n          removeToken()\r\n          resolve()\r\n        }\r\n\r\n        const { roles, name, avatar, introduction, permissions } = response.data\r\n\r\n        // roles must be a non-empty array\r\n        if (!roles || roles.length <= 0) {\r\n          reject('getInfo: roles must be a non-null array!')\r\n        }\r\n        commit('SET_PERMISSIONS', permissions)\r\n        commit('SET_ROLES', roles)\r\n        commit('SET_NAME', name)\r\n        commit('SET_AVATAR', avatar)\r\n        commit('SET_INTRODUCTION', introduction)\r\n        resolve(response)\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n  // 退出系统\r\n  LogOut({ commit, state }) {\r\n    return new Promise((resolve, reject) => {\r\n      logout(state.token).then(() => {\r\n        commit('SET_TOKEN', '')\r\n        commit('SET_ROLES', [])\r\n        commit('SET_PERMISSIONS', [])\r\n        removeToken()\r\n        storage.clear()\r\n        resolve()\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n  // 刷新token\r\n  refreshToken({ commit, state }) {\r\n    return new Promise((resolve, reject) => {\r\n      refreshtoken({ token: state.token }).then(response => {\r\n        const { token } = response\r\n        commit('SET_TOKEN', token)\r\n        setToken(token)\r\n        resolve()\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n\r\n  // remove token\r\n  resetToken({ commit }) {\r\n    return new Promise(resolve => {\r\n      commit('SET_TOKEN', '')\r\n      removeToken()\r\n      resolve()\r\n    })\r\n  },\r\n\r\n  // dynamically modify permissions\r\n  changeRoles({ commit, dispatch }, role) {\r\n    // eslint-disable-next-line no-async-promise-executor\r\n    return new Promise(async resolve => {\r\n      const token = role + '-token'\r\n\r\n      commit('SET_TOKEN', token)\r\n      setToken(token)\r\n\r\n      const { roles } = await dispatch('getInfo')\r\n\r\n      resetRouter()\r\n\r\n      // generate accessible routes map based on roles\r\n      const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })\r\n\r\n      // dynamically add accessible routes\r\n      router.addRoutes(accessRoutes)\r\n\r\n      // reset visited views and cached views\r\n      dispatch('tagsView/delAllViews', null, { root: true })\r\n\r\n      resolve()\r\n    })\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,KAAK,IAALA,MAAK,EAAEC,MAAM,EAAEC,OAAO,IAAPA,QAAO,EAAEC,YAAY,QAAQ,YAAY;AACjE,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAC9D,OAAOC,MAAM,IAAIC,WAAW,QAAQ,UAAU;AAC9C,OAAOC,OAAO,MAAM,iBAAiB;AAErC,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAEP,QAAQ,CAAC,CAAC;EACjBQ,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,EAAE;EAChBC,KAAK,EAAE,EAAE;EACTC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE;AAChB,CAAC;AAED,IAAMC,SAAS,GAAG;EAChBC,SAAS,EAAE,SAAXA,SAASA,CAAGT,KAAK,EAAEC,KAAK,EAAK;IAC3BD,KAAK,CAACC,KAAK,GAAGA,KAAK;EACrB,CAAC;EACDS,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGV,KAAK,EAAEI,YAAY,EAAK;IACzCJ,KAAK,CAACI,YAAY,GAAGA,YAAY;EACnC,CAAC;EACDO,QAAQ,EAAE,SAAVA,QAAQA,CAAGX,KAAK,EAAEE,IAAI,EAAK;IACzBF,KAAK,CAACE,IAAI,GAAGA,IAAI;EACnB,CAAC;EACDU,UAAU,EAAE,SAAZA,UAAUA,CAAGZ,KAAK,EAAEG,MAAM,EAAK;IAC7B,IAAIA,MAAM,CAACU,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MACjCb,KAAK,CAACG,MAAM,GAAGA,MAAM;IACvB,CAAC,MAAM;MACLH,KAAK,CAACG,MAAM,GAAGW,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAAGb,MAAM;IACtD;EACF,CAAC;EACDc,SAAS,EAAE,SAAXA,SAASA,CAAGjB,KAAK,EAAEK,KAAK,EAAK;IAC3BL,KAAK,CAACK,KAAK,GAAGA,KAAK;EACrB,CAAC;EACDa,eAAe,EAAE,SAAjBA,eAAeA,CAAGlB,KAAK,EAAEO,YAAY,EAAK;IACxCP,KAAK,CAACO,YAAY,GAAGA,YAAY;EACnC;AACF,CAAC;AAED,IAAMY,OAAO,GAAG;EACd;EACA7B,KAAK,WAALA,KAAKA,CAAA8B,IAAA,EAAaC,QAAQ,EAAE;IAAA,IAApBC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACZ,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCnC,MAAK,CAAC+B,QAAQ,CAAC,CAACK,IAAI,CAAC,UAAAC,QAAQ,EAAI;QAC/B,IAAQ1B,KAAK,GAAK0B,QAAQ,CAAlB1B,KAAK;QACbqB,MAAM,CAAC,WAAW,EAAErB,KAAK,CAAC;QAC1BN,QAAQ,CAACM,KAAK,CAAC;QACfuB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC,CAACI,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBJ,MAAM,CAACI,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED;EACArC,OAAO,WAAPA,OAAOA,CAAAsC,KAAA,EAAoB;IAAA,IAAjBR,MAAM,GAAAQ,KAAA,CAANR,MAAM;MAAEtB,KAAK,GAAA8B,KAAA,CAAL9B,KAAK;IACrB,OAAO,IAAIuB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtCjC,QAAO,CAAC,CAAC,CAACkC,IAAI,CAAC,UAAAC,QAAQ,EAAI;QACzB,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACI,IAAI,EAAE;UAC/BT,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;UACvB1B,WAAW,CAAC,CAAC;UACb4B,OAAO,CAAC,CAAC;QACX;QAEA,IAAAQ,cAAA,GAA2DL,QAAQ,CAACI,IAAI;UAAhE1B,KAAK,GAAA2B,cAAA,CAAL3B,KAAK;UAAEH,IAAI,GAAA8B,cAAA,CAAJ9B,IAAI;UAAEC,MAAM,GAAA6B,cAAA,CAAN7B,MAAM;UAAEC,YAAY,GAAA4B,cAAA,CAAZ5B,YAAY;UAAEE,WAAW,GAAA0B,cAAA,CAAX1B,WAAW;;QAEtD;QACA,IAAI,CAACD,KAAK,IAAIA,KAAK,CAAC4B,MAAM,IAAI,CAAC,EAAE;UAC/BR,MAAM,CAAC,0CAA0C,CAAC;QACpD;QACAH,MAAM,CAAC,iBAAiB,EAAEhB,WAAW,CAAC;QACtCgB,MAAM,CAAC,WAAW,EAAEjB,KAAK,CAAC;QAC1BiB,MAAM,CAAC,UAAU,EAAEpB,IAAI,CAAC;QACxBoB,MAAM,CAAC,YAAY,EAAEnB,MAAM,CAAC;QAC5BmB,MAAM,CAAC,kBAAkB,EAAElB,YAAY,CAAC;QACxCoB,OAAO,CAACG,QAAQ,CAAC;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBJ,MAAM,CAACI,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAoB;IAAA,IAAjBb,MAAM,GAAAa,KAAA,CAANb,MAAM;MAAEtB,KAAK,GAAAmC,KAAA,CAALnC,KAAK;IACpB,OAAO,IAAIuB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtClC,MAAM,CAACS,KAAK,CAACC,KAAK,CAAC,CAACyB,IAAI,CAAC,YAAM;QAC7BJ,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBA,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACvBA,MAAM,CAAC,iBAAiB,EAAE,EAAE,CAAC;QAC7B1B,WAAW,CAAC,CAAC;QACbG,OAAO,CAACqC,KAAK,CAAC,CAAC;QACfZ,OAAO,CAAC,CAAC;MACX,CAAC,CAAC,CAACI,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBJ,MAAM,CAACI,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAQ,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAoB;IAAA,IAAjBhB,MAAM,GAAAgB,KAAA,CAANhB,MAAM;MAAEtB,KAAK,GAAAsC,KAAA,CAALtC,KAAK;IAC1B,OAAO,IAAIuB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtChC,YAAY,CAAC;QAAEQ,KAAK,EAAED,KAAK,CAACC;MAAM,CAAC,CAAC,CAACyB,IAAI,CAAC,UAAAC,QAAQ,EAAI;QACpD,IAAQ1B,KAAK,GAAK0B,QAAQ,CAAlB1B,KAAK;QACbqB,MAAM,CAAC,WAAW,EAAErB,KAAK,CAAC;QAC1BN,QAAQ,CAACM,KAAK,CAAC;QACfuB,OAAO,CAAC,CAAC;MACX,CAAC,CAAC,CAACI,KAAK,CAAC,UAAAC,KAAK,EAAI;QAChBJ,MAAM,CAACI,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED;EACAU,UAAU,WAAVA,UAAUA,CAAAC,KAAA,EAAa;IAAA,IAAVlB,MAAM,GAAAkB,KAAA,CAANlB,MAAM;IACjB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BF,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;MACvB1B,WAAW,CAAC,CAAC;MACb4B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC;EAED;EACAiB,WAAW,WAAXA,WAAWA,CAAAC,KAAA,EAAuBC,IAAI,EAAE;IAAA,IAA1BrB,MAAM,GAAAoB,KAAA,CAANpB,MAAM;MAAEsB,QAAQ,GAAAF,KAAA,CAARE,QAAQ;IAC5B;IACA,OAAO,IAAIrB,OAAO;MAAA,IAAAsB,KAAA,GAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,CAAC,SAAAC,QAAMzB,OAAO;QAAA,IAAAvB,KAAA,EAAAiD,eAAA,EAAA7C,KAAA,EAAA8C,YAAA;QAAA,OAAAJ,YAAA,GAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACxBrD,KAAK,GAAG0C,IAAI,GAAG,QAAQ;cAE7BrB,MAAM,CAAC,WAAW,EAAErB,KAAK,CAAC;cAC1BN,QAAQ,CAACM,KAAK,CAAC;cAAAoD,QAAA,CAAAC,CAAA;cAAA,OAESV,QAAQ,CAAC,SAAS,CAAC;YAAA;cAAAM,eAAA,GAAAG,QAAA,CAAAE,CAAA;cAAnClD,KAAK,GAAA6C,eAAA,CAAL7C,KAAK;cAEbP,WAAW,CAAC,CAAC;;cAEb;cAAAuD,QAAA,CAAAC,CAAA;cAAA,OAC2BV,QAAQ,CAAC,2BAA2B,EAAEvC,KAAK,EAAE;gBAAEmD,IAAI,EAAE;cAAK,CAAC,CAAC;YAAA;cAAjFL,YAAY,GAAAE,QAAA,CAAAE,CAAA;cAElB;cACA1D,MAAM,CAAC4D,SAAS,CAACN,YAAY,CAAC;;cAE9B;cACAP,QAAQ,CAAC,sBAAsB,EAAE,IAAI,EAAE;gBAAEY,IAAI,EAAE;cAAK,CAAC,CAAC;cAEtDhC,OAAO,CAAC,CAAC;YAAA;cAAA,OAAA6B,QAAA,CAAAK,CAAA;UAAA;QAAA,GAAAT,OAAA;MAAA,CACV;MAAA,iBAAAU,EAAA;QAAA,OAAAd,KAAA,CAAAe,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbC,UAAU,EAAE,IAAI;EAChB9D,KAAK,EAALA,KAAK;EACLQ,SAAS,EAATA,SAAS;EACTW,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}