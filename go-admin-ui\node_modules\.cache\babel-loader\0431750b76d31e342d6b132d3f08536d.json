{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\index.js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\components\\index.js", "mtime": 1753924830217}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\eslint-loader@2.2.1\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IHsgZGVmYXVsdCBhcyBBcHBNYWluIH0gZnJvbSAnLi9BcHBNYWluJzsKZXhwb3J0IHsgZGVmYXVsdCBhcyBOYXZiYXIgfSBmcm9tICcuL05hdmJhcic7CmV4cG9ydCB7IGRlZmF1bHQgYXMgU2V0dGluZ3MgfSBmcm9tICcuL1NldHRpbmdzJzsKZXhwb3J0IHsgZGVmYXVsdCBhcyBTaWRlYmFyIH0gZnJvbSAnLi9TaWRlYmFyL2luZGV4LnZ1ZSc7CmV4cG9ydCB7IGRlZmF1bHQgYXMgVGFnc1ZpZXcgfSBmcm9tICcuL1RhZ3NWaWV3L2luZGV4LnZ1ZSc7"}, {"version": 3, "names": ["default", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Settings", "Sidebar", "TagsView"], "sources": ["D:/Desktop/GoAdmin/go-admin-ui/src/layout/components/index.js"], "sourcesContent": ["export { default as AppMain } from './AppMain'\r\nexport { default as Navbar } from './Navbar'\r\nexport { default as Setting<PERSON> } from './Settings'\r\nexport { default as Sidebar } from './Sidebar/index.vue'\r\nexport { default as TagsView } from './TagsView/index.vue'\r\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,OAAO,QAAQ,WAAW;AAC9C,SAASD,OAAO,IAAIE,MAAM,QAAQ,UAAU;AAC5C,SAASF,OAAO,IAAIG,QAAQ,QAAQ,YAAY;AAChD,SAASH,OAAO,IAAII,OAAO,QAAQ,qBAAqB;AACxD,SAASJ,OAAO,IAAIK,QAAQ,QAAQ,sBAAsB", "ignoreList": []}]}