{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\index.vue?vue&type=template&id=13877386&scoped=true", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\index.vue", "mtime": 1753924830218}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgOmNsYXNzPSJjbGFzc09iaiIgY2xhc3M9ImFwcC13cmFwcGVyIiA6c3R5bGU9InsnLS1jdXJyZW50LWNvbG9yJzogJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lfSI+DQogICAgPGRpdiB2LWlmPSJkZXZpY2U9PT0nbW9iaWxlJyYmc2lkZWJhci5vcGVuZWQiIGNsYXNzPSJkcmF3ZXItYmciIEBjbGljaz0iaGFuZGxlQ2xpY2tPdXRzaWRlIiAvPg0KICAgIDxzaWRlYmFyIGNsYXNzPSJzaWRlYmFyLWNvbnRhaW5lciIgOnN0eWxlPSJ7IGJhY2tncm91bmRDb2xvcjogJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lU3R5bGUgPT09ICdkYXJrJyA/IHZhcmlhYmxlcy5tZW51QmcgOiB2YXJpYWJsZXMubWVudUxpZ2h0QmcgfSIgLz4NCiAgICA8ZGl2IDpjbGFzcz0ie2hhc1RhZ3NWaWV3Om5lZWRUYWdzVmlld30iIGNsYXNzPSJtYWluLWNvbnRhaW5lciI+DQogICAgICA8ZGl2IDpjbGFzcz0ieydmaXhlZC1oZWFkZXInOmZpeGVkSGVhZGVyfSI+DQogICAgICAgIDxuYXZiYXIgLz4NCiAgICAgICAgPHRhZ3MtdmlldyB2LWlmPSJuZWVkVGFnc1ZpZXciIC8+DQogICAgICA8L2Rpdj4NCiAgICAgIDxhcHAtbWFpbiAvPg0KICAgICAgPHJpZ2h0LXBhbmVsIHYtaWY9InNob3dTZXR0aW5ncyI+DQogICAgICAgIDxzZXR0aW5ncyAvPg0KICAgICAgPC9yaWdodC1wYW5lbD4NCiAgICA8L2Rpdj4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\layout\\index.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,<PERSON>AC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACzJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/layout/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': $store.state.settings.theme}\">\r\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\" />\r\n    <sidebar class=\"sidebar-container\" :style=\"{ backgroundColor: $store.state.settings.themeStyle === 'dark' ? variables.menuBg : variables.menuLightBg }\" />\r\n    <div :class=\"{hasTagsView:needTagsView}\" class=\"main-container\">\r\n      <div :class=\"{'fixed-header':fixedHeader}\">\r\n        <navbar />\r\n        <tags-view v-if=\"needTagsView\" />\r\n      </div>\r\n      <app-main />\r\n      <right-panel v-if=\"showSettings\">\r\n        <settings />\r\n      </right-panel>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RightPanel from '@/components/RightPanel'\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    RightPanel,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      showSettings: state => state.settings.showSettings,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/styles/mixin.scss\";\r\n  @import \"~@/styles/variables.scss\";\r\n\r\n  .app-wrapper {\r\n    @include clearfix;\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    &.mobile.openSidebar {\r\n      position: fixed;\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .drawer-bg {\r\n    background: #000;\r\n    opacity: 0.3;\r\n    width: 100%;\r\n    top: 0;\r\n    height: 100%;\r\n    position: absolute;\r\n    z-index: 999;\r\n  }\r\n\r\n  .fixed-header {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 9;\r\n    width: calc(100% - #{$sideBarWidth});\r\n    transition: width 0.28s;\r\n  }\r\n\r\n  .hideSidebar .fixed-header {\r\n    width: calc(100% - 54px)\r\n  }\r\n\r\n  .mobile .fixed-header {\r\n    width: 100%;\r\n  }\r\n</style>\r\n"]}]}