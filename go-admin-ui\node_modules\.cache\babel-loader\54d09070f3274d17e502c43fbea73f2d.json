{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue", "mtime": 1753924830277}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\babel.config.js", "mtime": 1753924829906}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\babel-loader@8.4.1\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["listMenu", "getMenu", "delMenu", "addMenu", "updateMenu", "listSysApi", "Treeselect", "IconSelect", "name", "components", "data", "loading", "menuList", "sysapiList", "menuOptions", "title", "open", "visibleOptions", "queryParams", "undefined", "visible", "form", "apis", "sysApi", "rules", "required", "message", "trigger", "sort", "created", "_this", "getList", "getApiList", "getDicts", "then", "response", "methods", "handleChange", "value", "direction", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "list", "x", "length", "index", "element", "id", "push", "l", "e", "_this2", "handleClose", "done", "selected", "icon", "_this3", "normalizer", "node", "children", "menuId", "label", "getTreeselect", "_this4", "menu", "visibleFormat", "row", "menuType", "selectDictLabel", "cancel", "reset", "parentId", "menuName", "action", "isFrame", "resetForm", "handleQuery", "handleAdd", "handleUpdate", "_this5", "<PERSON><PERSON><PERSON>", "apiArray", "submitForm", "_this6", "$refs", "validate", "valid", "code", "msgSuccess", "msg", "msgError", "handleDelete", "_this7", "$confirm", "confirmButtonText", "cancelButtonText", "type", "Ids", "ids", "catch"], "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\views\\admin\\sys-menu\\index.vue"], "sourcesContent": ["<template>\r\n  <BasicLayout>\r\n    <template #wrapper>\r\n      <el-card class=\"box-card\">\r\n        <el-form :inline=\"true\">\r\n          <el-form-item label=\"菜单名称\">\r\n            <el-input\r\n              v-model=\"queryParams.title\"\r\n              placeholder=\"请输入菜单名称\"\r\n              clearable\r\n              size=\"small\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\">\r\n            <el-select v-model=\"queryParams.visible\" placeholder=\"菜单状态\" clearable size=\"small\">\r\n              <el-option\r\n                v-for=\"dict in visibleOptions\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button\r\n              v-permisaction=\"['admin:sysMenu:add']\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n            >新增</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"menuList\"\r\n          border\r\n          row-key=\"menuId\"\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n        >\r\n          <el-table-column prop=\"title\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"180px\" />\r\n          <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100px\">\r\n            <template slot-scope=\"scope\">\r\n              <svg-icon :icon-class=\"scope.row.icon\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"sort\" label=\"排序\" width=\"60px\" />\r\n          <el-table-column prop=\"permission\" label=\"权限标识\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover v-if=\"scope.row.sysApi.length>0\" trigger=\"hover\" placement=\"top\">\r\n                <el-table\r\n                  :data=\"scope.row.sysApi\"\r\n                  border\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-table-column\r\n                    prop=\"title\"\r\n                    label=\"title\"\r\n                    width=\"260px\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.type=='SYS' && scope.row.title!=''\"><el-tag type=\"success\">{{ '['+scope.row.type +'] '+ scope.row.title }}</el-tag></span>\r\n                      <span v-if=\"scope.row.type!='SYS' && scope.row.title!=''\"><el-tag type=\"\">{{ '['+scope.row.type +'] '+scope.row.title }}</el-tag></span>\r\n                      <span v-if=\"scope.row.title==''\"><el-tag type=\"danger\">暂无</el-tag></span>\r\n\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"path\"\r\n                    label=\"path\"\r\n                    width=\"270px\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <el-tag v-if=\"scope.row.action=='GET'\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='POST'\" type=\"success\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='PUT'\" type=\"warning\">{{ scope.row.action }}</el-tag>\r\n                      <el-tag v-if=\"scope.row.action=='DELETE'\" type=\"danger\">{{ scope.row.action }}</el-tag>\r\n                      {{ scope.row.path }}\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                </el-table>\r\n                <div slot=\"reference\" class=\"name-wrapper\">\r\n                  <span v-if=\"scope.row.permission==''\">-</span>\r\n                  <span v-else>{{ scope.row.permission }}</span>\r\n                </div>\r\n              </el-popover>\r\n              <span v-else>\r\n                <span v-if=\"scope.row.permission==''\">-</span>\r\n                <span v-else>{{ scope.row.permission }}</span>\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"path\" label=\"组件路径\" :show-overflow-tooltip=\"true\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.menuType=='A'\">{{ scope.row.path }}</span>\r\n              <span v-else>{{ scope.row.component }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"visible\" label=\"可见\" :formatter=\"visibleFormat\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                :type=\"scope.row.visible === '1' ? 'danger' : 'success'\"\r\n                disable-transitions\r\n              >{{ visibleFormat(scope.row) }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdAt\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createdAt) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:add']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-plus\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增</el-button>\r\n              <el-button\r\n                v-permisaction=\"['admin:sysMenu:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 添加或修改菜单对话框 -->\r\n        <el-drawer\r\n          ref=\"drawer\"\r\n          :title=\"title\"\r\n          :before-close=\"cancel\"\r\n          :visible.sync=\"open\"\r\n          direction=\"rtl\"\r\n          custom-class=\"demo-drawer\"\r\n          size=\"830px\"\r\n        >\r\n          <div class=\"demo-drawer__content\">\r\n            <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-position=\"top\" label-width=\"106px\">\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"parentId\">\r\n                    <span slot=\"label\">\r\n                      上级菜单\r\n                      <el-tooltip content=\"指当前菜单停靠的菜单归属\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <treeselect\r\n                      v-model=\"form.parentId\"\r\n                      :options=\"menuOptions\"\r\n                      :normalizer=\"normalizer\"\r\n                      :show-count=\"true\"\r\n                      placeholder=\"选择上级菜单\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item prop=\"title\">\r\n                    <span slot=\"label\">\r\n                      菜单标题\r\n                      <el-tooltip content=\"菜单位置显示的说明信息\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.title\" placeholder=\"请输入菜单标题\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item prop=\"sort\">\r\n                    <span slot=\"label\">\r\n                      显示排序\r\n                      <el-tooltip content=\"根据序号升序排列\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input-number v-model=\"form.sort\" controls-position=\"right\" :min=\"0\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"menuType\">\r\n                    <span slot=\"label\">\r\n                      菜单类型\r\n                      <el-tooltip content=\"包含目录：以及菜单或者菜单组，菜单：具体对应某一个页面，按钮：功能才做按钮；\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.menuType\">\r\n                      <el-radio label=\"M\">目录</el-radio>\r\n                      <el-radio label=\"C\">菜单</el-radio>\r\n                      <el-radio label=\"F\">按钮</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"菜单图标\">\r\n                    <el-popover\r\n                      placement=\"bottom-start\"\r\n                      width=\"460\"\r\n                      trigger=\"click\"\r\n                      @show=\"$refs['iconSelect'].reset()\"\r\n                    >\r\n                      <IconSelect ref=\"iconSelect\" @selected=\"selected\" />\r\n                      <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                        <svg-icon\r\n                          v-if=\"form.icon\"\r\n                          slot=\"prefix\"\r\n                          :icon-class=\"form.icon\"\r\n                          class=\"el-input__icon\"\r\n                          style=\"height: 32px;width: 16px;\"\r\n                        />\r\n                        <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\r\n                      </el-input>\r\n                    </el-popover>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'M' || form.menuType == 'C'\" prop=\"menuName\">\r\n                    <span slot=\"label\">\r\n                      路由名称\r\n                      <el-tooltip content=\"需要和页面name保持一致，对应页面即可选择缓存\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.menuName\" placeholder=\"请输入路由名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col v-if=\"form.menuType == 'M' || form.menuType == 'C'\" :span=\"12\">\r\n                  <el-form-item prop=\"component\">\r\n                    <span slot=\"label\">\r\n                      组件路径\r\n                      <el-tooltip content=\"菜单对应的具体vue页面文件路径views的下级路径/admin/sys-api/index；目录类型：填写Layout，如何有二级目录请参照日志目录填写；\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'M' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      是否外链\r\n                      <el-tooltip content=\"可以通过iframe打开指定地址\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.isFrame\">\r\n                      <el-radio label=\"0\">是</el-radio>\r\n                      <el-radio label=\"1\">否</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType != 'F'\" prop=\"path\">\r\n                    <span slot=\"label\">\r\n                      路由地址\r\n                      <el-tooltip content=\"访问此页面自定义的url地址，建议/开头书写，例如 /app-name/menu-name\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType == 'F' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      权限标识\r\n                      <el-tooltip content=\"前端权限控制按钮是否显示\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-input v-model=\"form.permission\" placeholder=\"请权限标识\" maxlength=\"50\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"12\">\r\n                  <el-form-item v-if=\"form.menuType != 'F'\">\r\n                    <span slot=\"label\">\r\n                      菜单状态\r\n                      <el-tooltip content=\"需要显示在菜单列表的菜单设置为显示，否则设置为隐藏\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-radio-group v-model=\"form.visible\">\r\n                      <el-radio\r\n                        v-for=\"dict in visibleOptions\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.value\"\r\n                      >{{ dict.label }}</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item v-if=\"form.menuType == 'F' || form.menuType == 'C'\">\r\n                    <span slot=\"label\">\r\n                      api权限\r\n                      <el-tooltip content=\"配置在这个才做上需要使用到的接口，否则在设置用户角色时，接口将无权访问。\" placement=\"top\">\r\n                        <i class=\"el-icon-question\" />\r\n                      </el-tooltip>\r\n                    </span>\r\n                    <el-transfer\r\n                      v-model=\"form.apis\"\r\n                      style=\"text-align: left; display: inline-block\"\r\n                      filterable\r\n                      :props=\"{\r\n                        key: 'id',\r\n                        label: 'title'\r\n                      }\"\r\n                      :titles=\"['未授权', '已授权']\"\r\n                      :button-texts=\"['收回', '授权 ']\"\r\n                      :format=\"{\r\n                        noChecked: '${total}',\r\n                        hasChecked: '${checked}/${total}'\r\n                      }\"\r\n                      class=\"panel\"\r\n                      :data=\"sysapiList\"\r\n                      @change=\"handleChange\"\r\n                    >\r\n                      <span slot-scope=\"{ option }\">{{ option.title }}</span>\r\n                    </el-transfer>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n            <div class=\"demo-drawer__footer\">\r\n              <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n              <el-button @click=\"cancel\">取 消</el-button>\r\n            </div>\r\n          </div>\r\n\r\n        </el-drawer>\r\n      </el-card>\r\n    </template>\r\n  </BasicLayout>\r\n</template>\r\n\r\n<script>\r\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from '@/api/admin/sys-menu'\r\nimport { listSysApi } from '@/api/admin/sys-api'\r\n\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.css'\r\nimport IconSelect from '@/components/IconSelect'\r\n\r\nexport default {\r\n  name: 'SysMenuManage',\r\n  components: { Treeselect, IconSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      sysapiList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 菜单状态数据字典\r\n      visibleOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        title: undefined,\r\n        visible: undefined\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        apis: [],\r\n        sysApi: []\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        title: [{ required: true, message: '菜单标题不能为空', trigger: 'blur' }],\r\n        sort: [{ required: true, message: '菜单顺序不能为空', trigger: 'blur' }]\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n\r\n    this.getApiList()\r\n    this.getDicts('sys_show_hide').then(response => {\r\n      this.visibleOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    handleChange(value, direction, movedKeys) {\r\n      console.log(value, direction, movedKeys)\r\n      const list = this.form.sysApi\r\n      this.form.apis = value\r\n      if (direction === 'right') {\r\n        for (let x = 0; x < movedKeys.length; x++) {\r\n          for (let index = 0; index < this.sysapiList.length; index++) {\r\n            const element = this.sysapiList[index]\r\n            if (element.id === movedKeys[x]) {\r\n              list.push(element)\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.form.sysApi = list\r\n      } else if (direction === 'left') {\r\n        const l = []\r\n        for (let index = 0; index < movedKeys.length; index++) {\r\n          const element = movedKeys[index]\r\n          for (let x = 0; x < list.length; x++) {\r\n            const e = list[x]\r\n            if (element !== e.id) {\r\n              l.push()\r\n              break\r\n            }\r\n          }\r\n        }\r\n        this.form.sysApi = l\r\n      }\r\n      // this.setApis(this.form.SysApi)\r\n      console.log(this.form.sysApi)\r\n    },\r\n    getApiList() {\r\n      this.loading = true\r\n      listSysApi({ 'pageSize': 10000, 'type': 'BUS' }).then(response => {\r\n        this.sysapiList = response.data.list\r\n        this.loading = false\r\n      }\r\n      )\r\n    },\r\n    handleClose(done) {\r\n      // if (this.loading) {\r\n      //   return\r\n      // }\r\n      // this.$confirm('需要提交表单吗？')\r\n      //   .then(_ => {\r\n      //     this.loading = true\r\n      //     this.timer = setTimeout(() => {\r\n      //       done()\r\n      //       // 动画关闭需要一定的时间\r\n      //       setTimeout(() => {\r\n      //         this.loading = false\r\n      //       }, 400)\r\n      //     }, 1000)\r\n      //   })\r\n      //   .catch(_ => {})\r\n    },\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = response.data\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.title,\r\n        children: node.children\r\n      }\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = []\r\n        const menu = { menuId: 0, title: '主类目', children: [] }\r\n        menu.children = response.data\r\n        this.menuOptions.push(menu)\r\n      })\r\n    },\r\n    // 菜单显示状态字典翻译\r\n    visibleFormat(row) {\r\n      if (row.menuType === 'F') {\r\n        return '-- --'\r\n      }\r\n      return this.selectDictLabel(this.visibleOptions, row.visible)\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: undefined,\r\n        parentId: 0,\r\n        menuName: undefined,\r\n        icon: undefined,\r\n        menuType: 'M',\r\n        apis: [],\r\n        sort: 0,\r\n        action: this.form.menuType === 'A' ? this.form.action : '',\r\n        isFrame: '1',\r\n        visible: '0'\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList()\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset()\r\n      this.getTreeselect()\r\n      if (row != null) {\r\n        this.form.parentId = row.menuId\r\n      }\r\n      this.open = true\r\n      this.title = '添加菜单'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.getTreeselect()\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = '修改菜单'\r\n      })\r\n    },\r\n    setApis(apiArray) {\r\n      var l = []\r\n      for (var index = 0; index < apiArray.length; index++) {\r\n        const element = apiArray[index]\r\n        l.push(element.id)\r\n      }\r\n      this.form.apis = l\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId !== undefined) {\r\n            updateMenu(this.form, this.form.menuId).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.msgSuccess(response.msg)\r\n                this.open = false\r\n                this.getList()\r\n              } else {\r\n                this.msgError(response.msg)\r\n              }\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除名称为\"' + row.title + '\"的数据项?', '警告', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(function() {\r\n        var Ids = (row.menuId && [row.menuId]) || this.ids\r\n        return delMenu({ 'ids': Ids })\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          this.msgSuccess(response.msg)\r\n          this.open = false\r\n          this.getList()\r\n        } else {\r\n          this.msgError(response.msg)\r\n        }\r\n      }).catch(function() {})\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"css\">\r\n.panel .el-transfer__buttons{\r\n  width: 150px;\r\n}\r\n.panel .el-transfer__buttons .el-button + .el-button{\r\n  margin-left:0;\r\n}\r\n.panel .el-transfer-panel{\r\n  width: 300px;\r\n}\r\n\r\n.el-col {\r\npadding: 0 5px;\r\n}\r\n.el-drawer__header{\r\nmargin-bottom: 0;\r\n}\r\n</style>\r\n"], "mappings": ";AAoWA,SAASA,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAS,QAAS,sBAAqB;AACrF,SAASC,UAAS,QAAS,qBAAoB;AAE/C,OAAOC,UAAS,MAAO,yBAAwB;AAC/C,OAAO,iDAAgD;AACvD,OAAOC,UAAS,MAAO,yBAAwB;AAE/C,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IAAEH,UAAU,EAAVA,UAAU;IAAEC,UAAS,EAATA;EAAW,CAAC;EACtCG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,OAAO,EAAE,IAAI;MACb;MACAC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACd;MACAC,WAAW,EAAE,EAAE;MACf;MACAC,KAAK,EAAE,EAAE;MACT;MACAC,IAAI,EAAE,KAAK;MACX;MACAC,cAAc,EAAE,EAAE;MAClB;MACAC,WAAW,EAAE;QACXH,KAAK,EAAEI,SAAS;QAChBC,OAAO,EAAED;MACX,CAAC;MACD;MACAE,IAAI,EAAE;QACJC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE;MACV,CAAC;MACD;MACAC,KAAK,EAAE;QACLT,KAAK,EAAE,CAAC;UAAEU,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACjEC,IAAI,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAO,CAAC;MACjE;IACF;EACF,CAAC;EACDE,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR,IAAI,CAACC,OAAO,CAAC;IAEb,IAAI,CAACC,UAAU,CAAC;IAChB,IAAI,CAACC,QAAQ,CAAC,eAAe,CAAC,CAACC,IAAI,CAAC,UAAAC,QAAO,EAAK;MAC9CL,KAAI,CAACb,cAAa,GAAIkB,QAAQ,CAACzB,IAAG;IACpC,CAAC;EACH,CAAC;EACD0B,OAAO,EAAE;IACPC,YAAY,WAAZA,YAAYA,CAACC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;MACxCC,OAAO,CAACC,GAAG,CAACJ,KAAK,EAAEC,SAAS,EAAEC,SAAS;MACvC,IAAMG,IAAG,GAAI,IAAI,CAACtB,IAAI,CAACE,MAAK;MAC5B,IAAI,CAACF,IAAI,CAACC,IAAG,GAAIgB,KAAI;MACrB,IAAIC,SAAQ,KAAM,OAAO,EAAE;QACzB,KAAK,IAAIK,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIJ,SAAS,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UACzC,KAAK,IAAIE,KAAI,GAAI,CAAC,EAAEA,KAAI,GAAI,IAAI,CAACjC,UAAU,CAACgC,MAAM,EAAEC,KAAK,EAAE,EAAE;YAC3D,IAAMC,OAAM,GAAI,IAAI,CAAClC,UAAU,CAACiC,KAAK;YACrC,IAAIC,OAAO,CAACC,EAAC,KAAMR,SAAS,CAACI,CAAC,CAAC,EAAE;cAC/BD,IAAI,CAACM,IAAI,CAACF,OAAO;cACjB;YACF;UACF;QACF;QACA,IAAI,CAAC1B,IAAI,CAACE,MAAK,GAAIoB,IAAG;MACxB,OAAO,IAAIJ,SAAQ,KAAM,MAAM,EAAE;QAC/B,IAAMW,CAAA,GAAI,EAAC;QACX,KAAK,IAAIJ,MAAI,GAAI,CAAC,EAAEA,MAAI,GAAIN,SAAS,CAACK,MAAM,EAAEC,MAAK,EAAE,EAAE;UACrD,IAAMC,QAAM,GAAIP,SAAS,CAACM,MAAK;UAC/B,KAAK,IAAIF,EAAA,GAAI,CAAC,EAAEA,EAAA,GAAID,IAAI,CAACE,MAAM,EAAED,EAAC,EAAE,EAAE;YACpC,IAAMO,CAAA,GAAIR,IAAI,CAACC,EAAC;YAChB,IAAIG,QAAM,KAAMI,CAAC,CAACH,EAAE,EAAE;cACpBE,CAAC,CAACD,IAAI,CAAC;cACP;YACF;UACF;QACF;QACA,IAAI,CAAC5B,IAAI,CAACE,MAAK,GAAI2B,CAAA;MACrB;MACA;MACAT,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrB,IAAI,CAACE,MAAM;IAC9B,CAAC;IACDS,UAAU,WAAVA,UAAUA,CAAA,EAAG;MAAA,IAAAoB,MAAA;MACX,IAAI,CAACzC,OAAM,GAAI,IAAG;MAClBN,UAAU,CAAC;QAAE,UAAU,EAAE,KAAK;QAAE,MAAM,EAAE;MAAM,CAAC,CAAC,CAAC6B,IAAI,CAAC,UAAAC,QAAO,EAAK;QAChEiB,MAAI,CAACvC,UAAS,GAAIsB,QAAQ,CAACzB,IAAI,CAACiC,IAAG;QACnCS,MAAI,CAACzC,OAAM,GAAI,KAAI;MACrB,CACA;IACF,CAAC;IACD0C,WAAW,WAAXA,WAAWA,CAACC,IAAI,EAAE;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACD;IACD;IACAC,QAAQ,WAARA,QAAQA,CAAC/C,IAAI,EAAE;MACb,IAAI,CAACa,IAAI,CAACmC,IAAG,GAAIhD,IAAG;IACtB,CAAC;IACD,aACAuB,OAAO,WAAPA,OAAOA,CAAA,EAAG;MAAA,IAAA0B,MAAA;MACR,IAAI,CAAC9C,OAAM,GAAI,IAAG;MAClBX,QAAQ,CAAC,IAAI,CAACkB,WAAW,CAAC,CAACgB,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC1CsB,MAAI,CAAC7C,QAAO,GAAIuB,QAAQ,CAACzB,IAAG;QAC5B+C,MAAI,CAAC9C,OAAM,GAAI,KAAI;MACrB,CAAC;IACH,CAAC;IACD,eACA+C,UAAU,WAAVA,UAAUA,CAACC,IAAI,EAAE;MACf,IAAIA,IAAI,CAACC,QAAO,IAAK,CAACD,IAAI,CAACC,QAAQ,CAACf,MAAM,EAAE;QAC1C,OAAOc,IAAI,CAACC,QAAO;MACrB;MACA,OAAO;QACLZ,EAAE,EAAEW,IAAI,CAACE,MAAM;QACfC,KAAK,EAAEH,IAAI,CAAC5C,KAAK;QACjB6C,QAAQ,EAAED,IAAI,CAACC;MACjB;IACF,CAAC;IACD,gBACAG,aAAa,WAAbA,aAAaA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACdhE,QAAQ,CAAC,CAAC,CAACkC,IAAI,CAAC,UAAAC,QAAO,EAAK;QAC1B6B,MAAI,CAAClD,WAAU,GAAI,EAAC;QACpB,IAAMmD,IAAG,GAAI;UAAEJ,MAAM,EAAE,CAAC;UAAE9C,KAAK,EAAE,KAAK;UAAE6C,QAAQ,EAAE;QAAG;QACrDK,IAAI,CAACL,QAAO,GAAIzB,QAAQ,CAACzB,IAAG;QAC5BsD,MAAI,CAAClD,WAAW,CAACmC,IAAI,CAACgB,IAAI;MAC5B,CAAC;IACH,CAAC;IACD;IACAC,aAAa,WAAbA,aAAaA,CAACC,GAAG,EAAE;MACjB,IAAIA,GAAG,CAACC,QAAO,KAAM,GAAG,EAAE;QACxB,OAAO,OAAM;MACf;MACA,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACpD,cAAc,EAAEkD,GAAG,CAAC/C,OAAO;IAC9D,CAAC;IACD;IACAkD,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAACtD,IAAG,GAAI,KAAI;MAChB,IAAI,CAACuD,KAAK,CAAC;IACb,CAAC;IACD;IACAA,KAAK,WAALA,KAAKA,CAAA,EAAG;MACN,IAAI,CAAClD,IAAG,GAAI;QACVwC,MAAM,EAAE1C,SAAS;QACjBqD,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAEtD,SAAS;QACnBqC,IAAI,EAAErC,SAAS;QACfiD,QAAQ,EAAE,GAAG;QACb9C,IAAI,EAAE,EAAE;QACRM,IAAI,EAAE,CAAC;QACP8C,MAAM,EAAE,IAAI,CAACrD,IAAI,CAAC+C,QAAO,KAAM,GAAE,GAAI,IAAI,CAAC/C,IAAI,CAACqD,MAAK,GAAI,EAAE;QAC1DC,OAAO,EAAE,GAAG;QACZvD,OAAO,EAAE;MACX;MACA,IAAI,CAACwD,SAAS,CAAC,MAAM;IACvB,CAAC;IACD,aACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC9C,OAAO,CAAC;IACf,CAAC;IACD,aACA+C,SAAS,WAATA,SAASA,CAACX,GAAG,EAAE;MACb,IAAI,CAACI,KAAK,CAAC;MACX,IAAI,CAACR,aAAa,CAAC;MACnB,IAAII,GAAE,IAAK,IAAI,EAAE;QACf,IAAI,CAAC9C,IAAI,CAACmD,QAAO,GAAIL,GAAG,CAACN,MAAK;MAChC;MACA,IAAI,CAAC7C,IAAG,GAAI,IAAG;MACf,IAAI,CAACD,KAAI,GAAI,MAAK;IACpB,CAAC;IACD,aACAgE,YAAY,WAAZA,YAAYA,CAACZ,GAAG,EAAE;MAAA,IAAAa,MAAA;MAChB,IAAI,CAACT,KAAK,CAAC;MACX,IAAI,CAACR,aAAa,CAAC;MACnB9D,OAAO,CAACkE,GAAG,CAACN,MAAM,CAAC,CAAC3B,IAAI,CAAC,UAAAC,QAAO,EAAK;QACnC6C,MAAI,CAAC3D,IAAG,GAAIc,QAAQ,CAACzB,IAAG;QACxBsE,MAAI,CAAChE,IAAG,GAAI,IAAG;QACfgE,MAAI,CAACjE,KAAI,GAAI,MAAK;MACpB,CAAC;IACH,CAAC;IACDkE,OAAO,WAAPA,OAAOA,CAACC,QAAQ,EAAE;MAChB,IAAIhC,CAAA,GAAI,EAAC;MACT,KAAK,IAAIJ,KAAI,GAAI,CAAC,EAAEA,KAAI,GAAIoC,QAAQ,CAACrC,MAAM,EAAEC,KAAK,EAAE,EAAE;QACpD,IAAMC,OAAM,GAAImC,QAAQ,CAACpC,KAAK;QAC9BI,CAAC,CAACD,IAAI,CAACF,OAAO,CAACC,EAAE;MACnB;MACA,IAAI,CAAC3B,IAAI,CAACC,IAAG,GAAI4B,CAAA;IACnB,CAAC;IACD;IACAiC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAa;MAAA,IAAAC,MAAA;MACrB,IAAI,CAACC,KAAK,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC,UAAAC,KAAI,EAAK;QACnC,IAAIA,KAAK,EAAE;UACT,IAAIH,MAAI,CAAC/D,IAAI,CAACwC,MAAK,KAAM1C,SAAS,EAAE;YAClCf,UAAU,CAACgF,MAAI,CAAC/D,IAAI,EAAE+D,MAAI,CAAC/D,IAAI,CAACwC,MAAM,CAAC,CAAC3B,IAAI,CAAC,UAAAC,QAAO,EAAK;cACvD,IAAIA,QAAQ,CAACqD,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAACtD,QAAQ,CAACuD,GAAG;gBAC5BN,MAAI,CAACpE,IAAG,GAAI,KAAI;gBAChBoE,MAAI,CAACrD,OAAO,CAAC;cACf,OAAO;gBACLqD,MAAI,CAACO,QAAQ,CAACxD,QAAQ,CAACuD,GAAG;cAC5B;YACF,CAAC;UACH,OAAO;YACLvF,OAAO,CAACiF,MAAI,CAAC/D,IAAI,CAAC,CAACa,IAAI,CAAC,UAAAC,QAAO,EAAK;cAClC,IAAIA,QAAQ,CAACqD,IAAG,KAAM,GAAG,EAAE;gBACzBJ,MAAI,CAACK,UAAU,CAACtD,QAAQ,CAACuD,GAAG;gBAC5BN,MAAI,CAACpE,IAAG,GAAI,KAAI;gBAChBoE,MAAI,CAACrD,OAAO,CAAC;cACf,OAAO;gBACLqD,MAAI,CAACO,QAAQ,CAACxD,QAAQ,CAACuD,GAAG;cAC5B;YACF,CAAC;UACH;QACF;MACF,CAAC;IACH,CAAC;IACD,aACAE,YAAY,WAAZA,YAAYA,CAACzB,GAAG,EAAE;MAAA,IAAA0B,MAAA;MAChB,IAAI,CAACC,QAAQ,CAAC,YAAW,GAAI3B,GAAG,CAACpD,KAAI,GAAI,QAAQ,EAAE,IAAI,EAAE;QACvDgF,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC/D,IAAI,CAAC,YAAW;QACjB,IAAIgE,GAAE,GAAK/B,GAAG,CAACN,MAAK,IAAK,CAACM,GAAG,CAACN,MAAM,CAAC,IAAK,IAAI,CAACsC,GAAE;QACjD,OAAOjG,OAAO,CAAC;UAAE,KAAK,EAAEgG;QAAI,CAAC;MAC/B,CAAC,CAAC,CAAChE,IAAI,CAAC,UAACC,QAAQ,EAAK;QACpB,IAAIA,QAAQ,CAACqD,IAAG,KAAM,GAAG,EAAE;UACzBK,MAAI,CAACJ,UAAU,CAACtD,QAAQ,CAACuD,GAAG;UAC5BG,MAAI,CAAC7E,IAAG,GAAI,KAAI;UAChB6E,MAAI,CAAC9D,OAAO,CAAC;QACf,OAAO;UACL8D,MAAI,CAACF,QAAQ,CAACxD,QAAQ,CAACuD,GAAG;QAC5B;MACF,CAAC,CAAC,CAACU,KAAK,CAAC,YAAW,CAAC,CAAC;IACxB;EACF;AACF", "ignoreList": []}]}