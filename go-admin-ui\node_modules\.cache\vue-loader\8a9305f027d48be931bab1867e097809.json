{"remainingRequest": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js??vue-loader-options!D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\GithubCorner\\index.vue?vue&type=style&index=0&id=402c866a&scoped=true&lang=css", "dependencies": [{"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\GithubCorner\\index.vue", "mtime": 1753924830036}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\css-loader@3.6.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@15.11.1\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\postcss-loader@3.0.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\cache-loader@4.1.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\GoAdmin\\go-admin-ui\\node_modules\\.store\\vue-loader@17.4.2\\node_modules\\vue-loader\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZ2l0aHViLWNvcm5lcjpob3ZlciAub2N0by1hcm0gew0KICBhbmltYXRpb246IG9jdG9jYXQtd2F2ZSA1NjBtcyBlYXNlLWluLW91dA0KfQ0KDQpAa2V5ZnJhbWVzIG9jdG9jYXQtd2F2ZSB7DQogIDAlLA0KICAxMDAlIHsNCiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwKQ0KICB9DQogIDIwJSwNCiAgNjAlIHsNCiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgtMjVkZWcpDQogIH0NCiAgNDAlLA0KICA4MCUgew0KICAgIHRyYW5zZm9ybTogcm90YXRlKDEwZGVnKQ0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOjUwMHB4KSB7DQogIC5naXRodWItY29ybmVyOmhvdmVyIC5vY3RvLWFybSB7DQogICAgYW5pbWF0aW9uOiBub25lDQogIH0NCiAgLmdpdGh1Yi1jb3JuZXIgLm9jdG8tYXJtIHsNCiAgICBhbmltYXRpb246IG9jdG9jYXQtd2F2ZSA1NjBtcyBlYXNlLWluLW91dA0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\Desktop\\GoAdmin\\go-admin-ui\\src\\components\\GithubCorner\\index.vue"], "names": [], "mappings": ";AA0BA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACtB,CAAC,CAAC;EACF,CAAC,CAAC,CAAC,EAAE;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,CAAC,CAAC,CAAC;EACH,CAAC,CAAC,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,CAAC,CAAC,CAAC;EACH,CAAC,CAAC,EAAE;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB;EACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C;AACF", "file": "D:/Desktop/GoAdmin/go-admin-ui/src/components/GithubCorner/index.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <a href=\"https://github.com/wenjianz<PERSON>/go-admin\" target=\"_blank\" class=\"github-corner\" aria-label=\"View source on Github\">\r\n    <svg\r\n      width=\"80\"\r\n      height=\"80\"\r\n      viewBox=\"0 0 250 250\"\r\n      style=\"fill:#40c9c6; color:#fff;\"\r\n      aria-hidden=\"true\"\r\n    >\r\n      <path d=\"M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z\" />\r\n      <path\r\n        d=\"M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2\"\r\n        fill=\"currentColor\"\r\n        style=\"transform-origin: 130px 106px;\"\r\n        class=\"octo-arm\"\r\n      />\r\n      <path\r\n        d=\"M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z\"\r\n        fill=\"currentColor\"\r\n        class=\"octo-body\"\r\n      />\r\n    </svg>\r\n  </a>\r\n</template>\r\n\r\n<style scoped>\r\n.github-corner:hover .octo-arm {\r\n  animation: octocat-wave 560ms ease-in-out\r\n}\r\n\r\n@keyframes octocat-wave {\r\n  0%,\r\n  100% {\r\n    transform: rotate(0)\r\n  }\r\n  20%,\r\n  60% {\r\n    transform: rotate(-25deg)\r\n  }\r\n  40%,\r\n  80% {\r\n    transform: rotate(10deg)\r\n  }\r\n}\r\n\r\n@media (max-width:500px) {\r\n  .github-corner:hover .octo-arm {\r\n    animation: none\r\n  }\r\n  .github-corner .octo-arm {\r\n    animation: octocat-wave 560ms ease-in-out\r\n  }\r\n}\r\n</style>\r\n"]}]}